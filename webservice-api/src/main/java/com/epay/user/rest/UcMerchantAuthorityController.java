package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcMerchantAuthorityService;
import com.epay.user.service.dto.UcMerchantAuthorityAdd;
import com.epay.user.service.dto.UcMerchantAuthorityCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 机构内的商户和许可关系的关联
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/authority")
public class UcMerchantAuthorityController {

    private final UcMerchantAuthorityService merchantAuthorityService;

    /**
     * 查询机构商户和权限的关联
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> query(UcMerchantAuthorityCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(merchantAuthorityService.queryAll(criteria, pageable)), HttpStatus.OK);
    }

    /**
     * 建立机构内的商户和许可关系的关联
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcMerchantAuthorityAdd create) {
        return new ResponseEntity<>(merchantAuthorityService.create(create), HttpStatus.OK);
    }

    /**
     * 删除机构内的商户和许可关系的关联
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
        return new ResponseEntity<>(merchantAuthorityService.delete(id), HttpStatus.OK);
    }

}
