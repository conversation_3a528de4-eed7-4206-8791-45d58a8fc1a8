package com.epay.user.rest;

import com.epay.user.domain.UcUserMerchantAccess;
import com.epay.user.service.UcUserMerchantAccessService;
import com.epay.user.service.dto.UcUserMerchantAccessCriteria;
import com.epay.user.service.dto.UcUserMerchantAccessDto;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户对商户的操作许可
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/merchant/access")
public class UcUserMerchantAccessController {

    private final UcUserMerchantAccessService ucUserMerchantAccessService;

//    /**
//     * 查询用户对商户的操作许可
//     * @param criteria
//     * @param pageable
//     * @return
//     */
//    @GetMapping("")
//    public ResponseEntity<RestResult> query(UcUserMerchantAccessCriteria criteria, Pageable pageable) {
//        return new ResponseEntity<>(ucUserMerchantAccessService.queryAll(criteria, pageable), HttpStatus.OK);
//    }
//
//    /**
//     * 创建用户对商户的操作许可
//     * @param create
//     * @return
//     */
//    @PostMapping("")
//    public ResponseEntity<RestResult> create(@RequestBody @Validated UcUserMerchantAccess create) {
//        return new ResponseEntity<>(ucUserMerchantAccessService.create(create), HttpStatus.OK);
//    }
//
//    /**
//     * 删除用户对商户的操作许可
//     * @param id
//     * @return
//     */
//    @DeleteMapping("/{id}")
//    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
//        return new ResponseEntity<>(ucUserMerchantAccessService.delete(id), HttpStatus.OK);
//    }

}
