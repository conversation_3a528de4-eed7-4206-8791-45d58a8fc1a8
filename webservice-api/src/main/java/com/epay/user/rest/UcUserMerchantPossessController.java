package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcUserMerchantPossessService;
import com.epay.user.service.dto.UcUserMerchantPossessAdd;
import com.epay.user.service.dto.UcUserMerchantPossessCriteria;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户和商户关联
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/user/merchant")
public class UcUserMerchantPossessController {

    private final UcUserMerchantPossessService ucUserMerchantPossessService;

    /**
     * 查询用户和商户关联
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> query(UcUserMerchantPossessCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(ucUserMerchantPossessService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建用户和商户关联
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcUserMerchantPossessAdd create) {
        return new ResponseEntity<>(ucUserMerchantPossessService.create(create), HttpStatus.OK);
    }

    /**
     * 删除用户和商户关联
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> delete(@PathVariable String id) {
        return new ResponseEntity<>(ucUserMerchantPossessService.delete(id), HttpStatus.OK);
    }

}
