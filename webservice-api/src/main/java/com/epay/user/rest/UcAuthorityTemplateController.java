package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import com.epay.user.service.UcAuthorityTemplateService;
import com.epay.user.service.dto.UcAuthorityTemplateAdd;
import com.epay.user.service.dto.UcAuthorityTemplateCriteria;
import com.epay.user.service.dto.UcAuthorityTemplateUpdate;
import com.epay.utils.RestResult;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户的权限模板
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/authority/template")
public class UcAuthorityTemplateController {

    private final UcAuthorityTemplateService userAuthorityTemplateService;

    /**
     * 查询用户的权限模板
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020014')")
    public ResponseEntity<RestResult> query(UcAuthorityTemplateCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(userAuthorityTemplateService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建用户的权限模板
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020014')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcAuthorityTemplateAdd create) {
        create.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        create.setCreator(SecurityUtils.getCurrentLoginUser().getOrgName());
        return new ResponseEntity<>(userAuthorityTemplateService.create(create), HttpStatus.OK);
    }

    /**
     * 更新用户的权限模板
     * @param update
     * @param id
     * @return
     */
    @Log
    @PatchMapping("/{id}")
    @PreAuthorize("@el.check('A100020014')")
    public ResponseEntity<RestResult> update(@RequestBody @Validated UcAuthorityTemplateUpdate update, @PathVariable Integer id) {
        update.setId(id);
        return new ResponseEntity<>(userAuthorityTemplateService.update(update), HttpStatus.OK);
    }

    /**
     * 删除用户的权限模板
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020014')")
    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
        return new ResponseEntity<>(userAuthorityTemplateService.delete(id), HttpStatus.OK);
    }

}
