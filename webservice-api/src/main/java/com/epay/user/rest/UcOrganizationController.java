package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import com.epay.utils.SecurityUtils;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcOrganizationService;
import com.epay.user.service.dto.UcOrganizationAddUpdate;
import com.epay.user.service.dto.UcOrganizationCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 组织机构
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/organization")
public class UcOrganizationController {

    private final UcOrganizationService ucOrganizationService;

    /**
     * 查询组织机构
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> query(UcOrganizationCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(ucOrganizationService.queryAll(criteria, pageable), HttpStatus.OK);
    }

//    /**
//     * 创建组织机构
//     *
//     * @param create
//     * @return
//     */
//    @PostMapping()
//    @PreAuthorize("@el.check('A100020011')")
//    public ResponseEntity<RestResult> create(@RequestBody @Valid UcOrganizationAddUpdate create) {
//        return new ResponseEntity<>(ucOrganizationService.create(create), HttpStatus.OK);
//    }
//
//    /**
//     * 更新组织机构
//     *
//     * @param update
//     * @return
//     */
//    @PatchMapping("/{id}")
//    @PreAuthorize("@el.check('A100020011')")
//    public ResponseEntity<RestResult> update(@RequestBody @Validated UcOrganizationAddUpdate update, @PathVariable Integer id) {
//        update.setId(id);
//        return new ResponseEntity<>(ucOrganizationService.update(update), HttpStatus.OK);
//    }
//
//    /**
//     * 更新组织机构
//     *
//     * @param id
//     * @return
//     */
//    @DeleteMapping("/{id}")
//    @PreAuthorize("@el.check('A100020011')")
//    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
//        ucOrganizationService.delete(id);
//        return new ResponseEntity<>(RestUtil.toRest(RestEnum.SUCCESS), HttpStatus.OK);
//    }

}
