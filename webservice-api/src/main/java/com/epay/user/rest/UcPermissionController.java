package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.domain.UcPermission;
import com.epay.user.service.UcPermissionService;
import com.epay.user.service.dto.UcPermissionCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 许可
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/permission")
public class UcPermissionController {

    private final UcPermissionService ucPermissionService;

    /**
     * 查询许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    public ResponseEntity<RestResult> query(UcPermissionCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(ucPermissionService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建许可
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcPermission create) {
        return new ResponseEntity<>(ucPermissionService.create(create), HttpStatus.OK);
    }

    /**
     * 更新许可
     *
     * @param update
     * @return
     */
    @Log
    @PatchMapping("/{id}")
    public ResponseEntity<RestResult> update(@RequestBody @Validated UcPermission update, @PathVariable Integer id) {
        update.setId(id);
        ucPermissionService.update(update);
        return new ResponseEntity<>(RestUtil.toRest(RestEnum.SUCCESS), HttpStatus.OK);
    }

    /**
     * 删除许可
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
        return new ResponseEntity<>(ucPermissionService.delete(id), HttpStatus.OK);
    }

}
