package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcRoleService;
import com.epay.user.service.dto.UcRoleAdd;
import com.epay.user.service.dto.UcRoleCriteria;
import com.epay.user.service.dto.UcRoleUpdate;
import com.epay.utils.RestResult;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/role")
public class UcRoleController {

    private final UcRoleService ucRoleService;

    /**
     * 查询角色
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("")
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> query(UcRoleCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(ucRoleService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建角色
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping("")
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcRoleAdd create) {
        create.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        create.setCreator(SecurityUtils.getCurrentLoginUser().getOrgName());
        return new ResponseEntity<>(ucRoleService.create(create), HttpStatus.OK);
    }

    /**
     * 修改角色
     *
     * @param update
     * @return
     */
    @Log
    @PatchMapping("/{id}")
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> update(@RequestBody @Validated UcRoleUpdate update, @PathVariable("id") Integer id) {
        update.setId(id);
        return new ResponseEntity<>(ucRoleService.update(update), HttpStatus.OK);
    }

    /**
     * 删除角色
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
        return new ResponseEntity<>(ucRoleService.delete(id), HttpStatus.OK);
    }

}
