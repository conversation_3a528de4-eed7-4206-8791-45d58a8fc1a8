package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcOrganizationMerchantPossessService;
import com.epay.user.service.dto.UcOrganizationMerchantPossessCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机构和商户关联
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/organization/merchant")
public class UcOrganizationMerchantPossessController {

    private final UcOrganizationMerchantPossessService ucOrganizationMerchantPossessService;

    /**
     * 查询机构和商户关联
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("")
    public ResponseEntity<RestResult> query(UcOrganizationMerchantPossessCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(ucOrganizationMerchantPossessService.queryAll(criteria, pageable), HttpStatus.OK);
    }

//    /**
//     * 创建机构和商户关联
//     *
//     * @param create
//     * @return
//     */
//    @PostMapping("")
//    public ResponseEntity<RestResult> create(@RequestBody @Validated UcOrganizationMerchantPossessAddUpdate create) {
//        return new ResponseEntity<>(ucOrganizationMerchantPossessService.create(create), HttpStatus.OK);
//    }
//
//    /**
//     * 删除机构和商户关联
//     *
//     * @param id
//     * @return
//     */
//    @DeleteMapping("/{id}")
//    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
//        ucOrganizationMerchantPossessService.delete(id);
//        return new ResponseEntity<>(RestUtil.toRest(RestEnum.SUCCESS), HttpStatus.OK);
//    }

}
