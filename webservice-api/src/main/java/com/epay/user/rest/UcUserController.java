package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import com.epay.user.domain.UcUserUpdateStatus;
import com.epay.user.service.UcUserService;
import com.epay.user.service.dto.*;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/user")
public class UcUserController {

    private final UcUserService ucUserService;

    /**
     * 查询用户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> query(UcUserCriteria criteria, Pageable pageable) throws Exception {
        return new ResponseEntity<>(ucUserService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建用户
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcUserAdd create) {
        return new ResponseEntity<>(ucUserService.create(create), HttpStatus.OK);
    }

    /**
     * 更新用户
     *
     * @param update
     * @return
     */
    @Log
    @PatchMapping("/{id}")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> update(@RequestBody @Validated UcUserUpdate update, @PathVariable("id") Integer id) {
        return new ResponseEntity<>(ucUserService.update(update, id), HttpStatus.OK);
    }

    /**
     * 更新用户状态
     *
     * @param update
     * @return
     */
    @Log
    @PatchMapping("/status/{id}")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> status(@RequestBody @Validated UcUserUpdateStatus update, @PathVariable("id") Integer id) {
        return new ResponseEntity<>(ucUserService.status(update, id), HttpStatus.OK);
    }

    /**
     * 重置密码
     * @return
     */
    @Log
    @PutMapping("/resetpasswd")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> resetPassword(@RequestBody UcUserResetPasswd resetPasswd) {
        return new ResponseEntity<>(ucUserService.resetPassword(resetPasswd), HttpStatus.OK);
    }

    /**
     * 重置自己密码
     * @return
     */
    @Log
    @PutMapping("/resetpasswdSelf")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> resetSelfPassword(@RequestBody UcUserResetSelfPasswd resetSelfPasswd) {
        return new ResponseEntity<>(ucUserService.resetSelfPassword(resetSelfPasswd), HttpStatus.OK);
    }

    /**
     * 删除用户
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> delete(@PathVariable("id") Integer id) {
        return new ResponseEntity<>(ucUserService.delete(id), HttpStatus.OK);
    }

}
