package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcUserAuthorityService;
import com.epay.user.service.dto.UcUserAuthorityAddUpdate;
import com.epay.user.service.dto.UcUserAuthorityCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户对商户权限操作功能许可
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/user/authority")
public class UcUserAuthorityController {

    private final UcUserAuthorityService userAuthorityService;

    /**
     * 查询用户对商户权限操作功能许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> query(UcUserAuthorityCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(RestUtil.ok(userAuthorityService.queryAll(criteria, pageable)), HttpStatus.OK);
    }

    /**
     * 创建用户对商户权限操作功能许可
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcUserAuthorityAddUpdate create) {
        create.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(userAuthorityService.create(create), HttpStatus.OK);
    }

    /**
     * 删除用户对商户权限操作功能许可
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020011')")
    public ResponseEntity<RestResult> delete(@PathVariable String id) {
        return new ResponseEntity<>(userAuthorityService.delete(id), HttpStatus.OK);
    }

}
