package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcUserRoleRelationService;
import com.epay.user.service.dto.UcUserRoleRelationAdd;
import com.epay.user.service.dto.UcUserRoleRelationCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/role/relation")
public class UcUserRoleRelationController {

    private final UcUserRoleRelationService ucUserService;

    /**
     * 查询用户角色和权限关系
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> query(UcUserRoleRelationCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(ucUserService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 新建用户角色和权限关系
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcUserRoleRelationAdd create) {
        return new ResponseEntity<>(ucUserService.create(create), HttpStatus.OK);
    }

    /**
     * 新建用户角色和权限关系
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
        return new ResponseEntity<>(ucUserService.delete(id), HttpStatus.OK);
    }

}
