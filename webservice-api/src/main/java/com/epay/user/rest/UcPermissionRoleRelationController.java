package com.epay.user.rest;

import com.epay.logging.annotation.Log;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.UcPermissionRoleRelationService;
import com.epay.user.service.dto.UcPermissionRoleRelationAdd;
import com.epay.user.service.dto.UcPermissionRoleRelationCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 许可角色关联角色关联
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/role/permission")
public class UcPermissionRoleRelationController {

    private final UcPermissionRoleRelationService ucPermissionRoleRelationService;

    /**
     * 查询许可角色关联
     *
     * @param criteria
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping()
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> queryAll(@Validated UcPermissionRoleRelationCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(ucPermissionRoleRelationService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建许可角色关联
     * @param create
     * @return
     */
    @Log
    @PostMapping()
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> create(@RequestBody @Validated UcPermissionRoleRelationAdd create) {
        create.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(ucPermissionRoleRelationService.create(create), HttpStatus.OK);
    }

    /**
     * 删除许可角色关联
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    @PreAuthorize("@el.check('A100020012')")
    public ResponseEntity<RestResult> delete(@PathVariable String id) {
        return new ResponseEntity<>(ucPermissionRoleRelationService.delete(id), HttpStatus.OK);
    }

}
