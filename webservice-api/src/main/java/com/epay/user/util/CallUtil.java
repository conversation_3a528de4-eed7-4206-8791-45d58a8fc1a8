package com.epay.user.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.epay.config.KeyPathConstant;
import com.epay.utils.*;
import com.epay.exception.BadRequestException;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/3
 */
public class CallUtil {

    public static RestResult query(String url, Object criteria, Pageable pageable) {
        try {
            Map<String, Object> paramsMap = BeanUtil.beanToMap(criteria, false, true);
            PageUtil.mapAddPage(paramsMap, pageable);
            paramsMap.put("timestamp", System.currentTimeMillis() / 1000L);
            paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(url + "?" + SignUtil.mapToUrlParamsEncoding(paramsMap));
            HttpResponse responseList = requestList.execute();
            return JSON.parseObject(responseList.body(), RestResult.class);
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public static RestResult create(String url, Object create) {
        try {
            Map<String, Object> paramsMap = BeanUtil.beanToMap(create, false, true);
            paramsMap.put("timestamp", DateTime.now().getTime() / 1000L);
            paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
            // 查询内容
            HttpRequest request = HttpUtil.createPost(url);
            request.body(JSON.toJSONString(paramsMap));
            HttpResponse response = request.execute();
            if (response.getStatus() == HttpStatus.OK.value()) {
                return JSON.parseObject(response.body(), RestResult.class);
            } else {
                return RestUtil.toRest(response.getStatus(), response.body());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public static RestResult update(String url, Object update) {
        try {
            Map<String, Object> paramsMap = BeanUtil.beanToMap(update, false, true);
            paramsMap.put("timestamp", DateTime.now().getTime() / 1000L);
            paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
            // 查询内容
            HttpRequest request = HttpUtil.createRequest(Method.PATCH, url);
            request.body(JSON.toJSONString(paramsMap));
            HttpResponse response = request.execute();
            if (response.getStatus() == HttpStatus.OK.value()) {
                return JSON.parseObject(response.body(), RestResult.class);
            } else {
                return RestUtil.toRest(response.getStatus(), response.body());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public static RestResult put(String url, Object update) {
        try {
            Map<String, Object> paramsMap = BeanUtil.beanToMap(update, false, true);
            paramsMap.put("timestamp", DateTime.now().getTime() / 1000L);
            paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
            // 查询内容
            HttpRequest request = HttpUtil.createRequest(Method.PUT, url);
            request.body(JSON.toJSONString(paramsMap));
            HttpResponse response = request.execute();
            if (response.getStatus() == HttpStatus.OK.value()) {
                return JSON.parseObject(response.body(), RestResult.class);
            } else {
                return RestUtil.toRest(response.getStatus(), response.body());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public static RestResult requestNotParams(Method method, String url, Integer id) {
        try {
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("id", id);
            paramsMap.put("timestamp", DateTime.now().getTime() / 1000L);
            paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
            // 查询内容
            HttpRequest request = HttpUtil.createRequest(method, url);
            request.body(JSON.toJSONString(paramsMap));
            HttpResponse response = request.execute();
            if (response.getStatus() == HttpStatus.OK.value()) {
                return JSON.parseObject(response.body(), RestResult.class);
            } else {
                return RestUtil.toRest(response.getStatus(), response.body());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public static RestResult delete(String url, Object id) {
        try {
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("id", id);
            paramsMap.put("timestamp", DateTime.now().getTime() / 1000L);
            paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
            // 查询内容
            HttpRequest request = HttpUtil.createRequest(Method.DELETE, url);
            request.body(JSON.toJSONString(paramsMap));
            HttpResponse response = request.execute();
            if (response.getStatus() == HttpStatus.OK.value()) {
                return JSON.parseObject(response.body(), RestResult.class);
            } else {
                return RestUtil.toRest(response.getStatus(), response.body());
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }


}
