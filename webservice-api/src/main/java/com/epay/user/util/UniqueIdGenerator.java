package com.epay.user.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2024/4/20
 */
@Slf4j
@Component
public class UniqueIdGenerator {

    private static final String KEY = "unique_manager_id:";

    @Value( "${id-init.manager-id}")
    private Long managerId;

    @Resource
    private RedisTemplate<Object, Object> redisTemplate;


    public UniqueIdGenerator() {
    }

    /**
     * 初始化ID的起始值，如果尚未设置
     */
    @PostConstruct
    private void initializeId() {
        // 使用RedisTemplate检查是否已经有ID设置
        if (Boolean.FALSE.equals(redisTemplate.hasKey(KEY))) {
            // 设置起始ID为10000
            redisTemplate.opsForValue().increment(KEY, managerId);
        }
    }

    /**
     * 生成按顺序的唯一账号ID，从10001开始，满足后自动增加位数
     * @return 唯一的账号ID
     */
    public Long generateUniqueUserId() {
        // 自动递增ID
        return redisTemplate.opsForValue().increment(KEY, 1);
    }
}
