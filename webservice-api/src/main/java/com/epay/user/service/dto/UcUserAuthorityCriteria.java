package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcUserAuthorityCriteria implements Serializable {

	public UcUserAuthorityCriteria() {
	}

	public UcUserAuthorityCriteria(Integer id) {
		this.id = id;
	}

	public UcUserAuthorityCriteria(String userId) {
		this.userId = userId;
	}

	private Integer id;

	private String userId;

	private String authorityId;

	private String orgId;


}
