package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.epay.base.login.UcUserLoginDto;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcUserAuthorityService;
import com.epay.user.service.dto.UcUserAuthorityAddUpdate;
import com.epay.user.service.dto.UcUserAuthorityCriteria;
import com.epay.user.service.dto.UcUserAuthorityDto;
import com.epay.user.service.dto.UcUserCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcUserAuthorityServiceImpl implements UcUserAuthorityService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询用户对商户的操作许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcUserAuthorityCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/user/authority", criteria, pageable);
    }

    /**
     * 创建用户对商户的操作许可
     *
     * @param create
     */
    @Override
    public RestResult create(UcUserAuthorityAddUpdate create) {
        RestResult rest = valid(create.getUserId());
        if (rest != null) {
            return rest;
        }
        return CallUtil.create(dataServerProperties.getUserUrl() + "/user/authority", create);
    }

    /**
     * 删除用户对商户的操作许可
     *
     * @param id
     */
    @Override
    public RestResult delete(String id) {
        RestResult rest = validAuthority(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/user/authority/" + id, id);
    }

    private RestResult validAuthority(String ids) {
        for (String id : ids.split(",")) {
            RestResult userAuthorityList = CallUtil.query(dataServerProperties.getUserUrl() + "/user/authority", new UcUserAuthorityCriteria(Integer.parseInt(id)), PageRequest.of(0, 1));
            if (userAuthorityList.getCode() != 0) {
                return RestUtil.toRest(RestEnum.USER_AUTHORITY_NOT_EXIST);
            }
            if (userAuthorityList.getData() == null) {
                return RestUtil.toRest(RestEnum.USER_AUTHORITY_NOT_EXIST);
            }
            List<UcUserAuthorityDto> userAuthorityDtoList = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userAuthorityList.getData())).getJSONArray("data")), UcUserAuthorityDto.class);
            if (userAuthorityDtoList.isEmpty()) {
                return RestUtil.toRest(RestEnum.USER_AUTHORITY_NOT_EXIST);
            }
            RestResult res = valid(userAuthorityDtoList.get(0).getUserId());
            if (res != null) return res;
        }
        return null;
    }

    private RestResult valid(String userId) {
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", new UcUserCriteria(userId), PageRequest.of(0, 1));
        if (userList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (userList.getData() == null) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
        if (users.isEmpty()) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (!users.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }

}
