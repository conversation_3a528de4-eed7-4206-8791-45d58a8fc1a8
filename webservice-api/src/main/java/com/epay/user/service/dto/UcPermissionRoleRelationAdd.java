package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcPermissionRoleRelationAdd implements Serializable {

	@NotEmpty(message = "roleId not empty")
	private String roleId;

	@NotEmpty(message = "permissionCode not empty")
	private String permissionCode;

	private String orgId;

}
