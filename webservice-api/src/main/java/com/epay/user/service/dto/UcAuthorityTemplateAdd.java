package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Getter
@Setter
public class UcAuthorityTemplateAdd {

    @NotEmpty(message = "模板名称不能为空")
    private String templateName;

    private String orgId;

    private String type;

    @NotEmpty(message = "数据不能为空")
    private String templateData;

    private String creator = "DP";

    private String creatorId;

}
