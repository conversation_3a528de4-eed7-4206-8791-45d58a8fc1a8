package com.epay.user.service.dto;

import com.epay.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class UcUserUpdate extends BaseDto {

    private Integer id;

    @NotEmpty(message = "email not empty")
    private String email;

    @NotEmpty(message = "username not empty")
    private String username;

    private String phoneNumber;

    /**
     * student is 1
     */
    private String roleId;

    /**
     * id card is 1,passport is 2,driver license is 3
     */
    private Integer certType;

    private Integer status;

    private String certId;

    private String orgName;

    private String orgId;

    private Integer gender;

    private String address;

    private String avatarUrl;

    private String country;

    private String provinces;

    private String authorityTemplateId;

}
