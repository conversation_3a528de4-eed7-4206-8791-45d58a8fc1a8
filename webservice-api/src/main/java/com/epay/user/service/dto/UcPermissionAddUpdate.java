package com.epay.user.service.dto;

import com.epay.validated.AddVerify;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 许可
 *
 * <AUTHOR>
 * @date 2024/4/20
 */
@Getter
@Setter
public class UcPermissionAddUpdate {

    @NotEmpty(message = "权限名称不能为空", groups = {AddVerify.class})
    private String permissionName;

    @NotNull(message = "等级不能为空", groups = {AddVerify.class})
    private Integer level = 1;

    private String superCode;

    @NotEmpty(message = "许可权限说明不能为空", groups = {AddVerify.class})
    private String info;

}
