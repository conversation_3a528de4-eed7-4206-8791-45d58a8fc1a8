package com.epay.user.service;


import org.springframework.data.domain.Pageable;
import com.epay.user.domain.UcPermission;
import com.epay.user.service.dto.UcPermissionCriteria;
import com.epay.utils.RestResult;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface UcPermissionService {


    /**
     * 查询许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcPermissionCriteria criteria, Pageable pageable);

    /**
     * 创建许可
     *
     * @param create
     */
    RestResult create(UcPermission create);

    /**
     * 更新许可
     *
     * @param update
     */
    void update(UcPermission update);

    /**
     * 删除许可
     *
     * @param ids
     */
    RestResult delete(Integer ids);
}
