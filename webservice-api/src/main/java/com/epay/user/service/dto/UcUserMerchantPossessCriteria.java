package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcUserMerchantPossessCriteria implements Serializable {

	public UcUserMerchantPossessCriteria() {
	}

	public UcUserMerchantPossessCriteria(Integer id) {
		this.id = id;
	}

	private Integer id;

	private String orgId;

	private String userId;

	private String merCode;

}
