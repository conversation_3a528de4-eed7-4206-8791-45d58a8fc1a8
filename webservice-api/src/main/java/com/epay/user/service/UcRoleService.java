package com.epay.user.service;


import com.epay.user.service.dto.UcRoleAdd;
import com.epay.user.service.dto.UcRoleCriteria;
import com.epay.user.service.dto.UcRoleUpdate;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcRoleService {


    /**
     * 查询角色
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcRoleCriteria criteria, Pageable pageable);

    /**
     * 创建角色
     * @param create
     */
    RestResult create(UcRoleAdd create);

    /**
     * 修改角色
     * @param update
     * @return
     */
    RestResult update(UcRoleUpdate update);

    /**
     * 删除角色
     * @param ids
     */
    RestResult delete(Integer ids);

}
