package com.epay.user.service.impl;

import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcOrganizationService;
import com.epay.user.service.dto.UcOrganizationAddUpdate;
import com.epay.user.service.dto.UcOrganizationCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcOrganizationServiceImpl implements UcOrganizationService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询组织机构
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcOrganizationCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/organization", criteria, pageable);
    }

    /**
     * 创建组织机构
     *
     * @param create
     */
    @Override
    public RestResult create(UcOrganizationAddUpdate create) {
        return CallUtil.create(dataServerProperties.getUserUrl() + "/organization", create);
    }

    /**
     * 更新组织机构
     *
     * @param update
     */
    @Override
    public RestResult update(UcOrganizationAddUpdate update) {
        return CallUtil.update(dataServerProperties.getUserUrl() + "/organization/" + update.getId(), update);
    }

    /**
     * 删除组织机构
     * @param id
     */
    @Override
    public void delete(Integer id) {
        CallUtil.delete(dataServerProperties.getUserUrl() + "/organization/" + id, id);
    }

}
