package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class UcMerchantAuthorityAdd implements Serializable {

    @NotEmpty(message = "orgId not empty")
    private String orgId;

    @Valid
    private List<UcMerchantAuthorityList> relationList;

}
