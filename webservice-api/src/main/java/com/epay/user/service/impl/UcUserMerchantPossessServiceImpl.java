package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.epay.base.login.UcUserLoginDto;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcUserMerchantPossessService;
import com.epay.user.service.dto.UcUserCriteria;
import com.epay.user.service.dto.UcUserMerchantPossessAdd;
import com.epay.user.service.dto.UcUserMerchantPossessCriteria;
import com.epay.user.service.dto.UcUserMerchantPossessDto;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcUserMerchantPossessServiceImpl implements UcUserMerchantPossessService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询用户和商户关联
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcUserMerchantPossessCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/user/merchant", criteria, pageable);
    }

    /**
     * 创建用户和商户关联
     *
     * @param create
     */
    @Override
    public RestResult create(UcUserMerchantPossessAdd create) {
        RestResult rest = valid(create.getUserId());
        if (rest != null) {
            return rest;
        }
        return CallUtil.create(dataServerProperties.getUserUrl() + "/user/merchant", create);
    }

    /**
     * 删除用户和商户关联
     *
     * @param id
     */
    @Override
    public RestResult delete(String id) {
        RestResult rest = validMerchant(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/user/merchant/" + id, id);
    }

    private RestResult validMerchant(String ids) {
        for (String id : ids.split(",")) {
            RestResult userMerchantList = CallUtil.query(dataServerProperties.getUserUrl() + "/user/merchant", new UcUserMerchantPossessCriteria(Integer.parseInt(id)), PageRequest.of(0, 1));
            if (userMerchantList.getCode() != 0) {
                return RestUtil.toRest(RestEnum.USER_MERCHANT_POSSESS_NOT_EXIST);
            }
            if (userMerchantList.getData() == null) {
                return RestUtil.toRest(RestEnum.USER_MERCHANT_POSSESS_NOT_EXIST);
            }
            List<UcUserMerchantPossessDto> userMerchantPossessDtoList = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userMerchantList.getData())).getJSONArray("data")), UcUserMerchantPossessDto.class);
            if (userMerchantPossessDtoList.isEmpty()) {
                return RestUtil.toRest(RestEnum.USER_MERCHANT_POSSESS_NOT_EXIST);
            }
            RestResult res = valid(userMerchantPossessDtoList.get(0).getUserId());
            if (res != null) {
                return res;
            }
        }
        return null;
    }

    private RestResult valid(String userId) {
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", new UcUserCriteria(userId), PageRequest.of(0, 1));
        if (userList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (userList.getData() == null) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
        if (users.isEmpty()) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (!users.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }

}
