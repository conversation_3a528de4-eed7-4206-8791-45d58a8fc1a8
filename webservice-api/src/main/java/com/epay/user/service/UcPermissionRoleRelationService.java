package com.epay.user.service;


import org.springframework.data.domain.Pageable;
import com.epay.user.service.dto.UcPermissionRoleRelationAdd;
import com.epay.user.service.dto.UcPermissionRoleRelationCriteria;
import com.epay.utils.RestResult;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcPermissionRoleRelationService {


    /**
     * 查询角色和许可关系
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcPermissionRoleRelationCriteria criteria, Pageable pageable);


    /**
     * 创建许可角色关联
     * @param create
     */
    RestResult create(UcPermissionRoleRelationAdd create);

    /**
     * 删除许可角色关联
     * @param id
     */
    RestResult delete(String id);
}
