package com.epay.user.service;


import com.epay.user.service.dto.UcOrganizationAddUpdate;
import com.epay.user.service.dto.UcOrganizationCriteria;
import com.epay.user.service.dto.UcOrganizationDto;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcOrganizationService {


    /**
     * 查询组织机构
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcOrganizationCriteria criteria, Pageable pageable);

    /**
     * 创建组织机构
     * @param create
     */
    RestResult create(UcOrganizationAddUpdate create);

    /**
     * 更新组织机构
     *
     * @param update
     */
    RestResult update(UcOrganizationAddUpdate update);

    /**
     * 删除组织机构
     * @param id
     */
    void delete(Integer id);
}
