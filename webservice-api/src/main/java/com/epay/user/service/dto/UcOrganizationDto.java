package com.epay.user.service.dto;

import com.epay.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcOrganizationDto extends BaseDto implements Serializable {

	private Integer id;

	private String orgName;

	private String orgId;

	private String phoneNumber;

	private Integer merchantNum;

	private String email;

	private String contact;

	private String address;

	private String country;

	private String registration;

	private Integer status;

}
