package com.epay.user.service.impl;

import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.domain.UcOrganizationMerchantPossess;
import com.epay.user.service.UcOrganizationMerchantPossessService;
import com.epay.user.service.dto.UcOrganizationMerchantPossessAddUpdate;
import com.epay.user.service.dto.UcOrganizationMerchantPossessCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcOrganizationMerchantPossessServiceImpl implements UcOrganizationMerchantPossessService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcOrganizationMerchantPossessCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/organization/merchant", criteria, pageable);
    }

    /**
     * 创建许可
     *
     * @param create
     */
    @Override
    public RestResult create(UcOrganizationMerchantPossessAddUpdate create) {
        return CallUtil.create(dataServerProperties.getUserUrl() + "/organization/merchant", create);
    }


    /**
     * 更新许可
     * @param update
     */
    @Override
    public void update(UcOrganizationMerchantPossess update) {
        CallUtil.update(dataServerProperties.getUserUrl() + "/organization/merchant", update);
    }

    /**
     * 删除许可
     * @param id
     */
    @Override
    public void delete(Integer id) {
        CallUtil.delete(dataServerProperties.getUserUrl() + "/organization/merchant/" + id, id);
    }

}
