package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcPermissionRoleRelationService;
import com.epay.user.service.dto.*;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcPermissionRoleRelationServiceImpl implements UcPermissionRoleRelationService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询角色和许可关系
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcPermissionRoleRelationCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/role/permission", criteria, pageable);
    }

    /**
     * 创建许可角色关联
     *
     * @param create
     */
    @Override
    public RestResult create(UcPermissionRoleRelationAdd create) {
//        RestResult rest = valid(create.getRoleId());
//        if (rest != null) {
//            return rest;
//        }
        return CallUtil.create(dataServerProperties.getUserUrl() + "/role/permission", create);
    }

    /**
     * 删除许可角色关联
     *
     * @param id
     */
    @Override
    public RestResult delete(String id) {
//        RestResult rest = valid(id);
//        if (rest != null) {
//            return rest;
//        }
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/role/permission/" + id, id);
    }

    private RestResult valid(Integer id) {
        RestResult permissionRoleRelationList = CallUtil.query(dataServerProperties.getUserUrl() + "/role/permission", new UcPermissionRoleRelationCriteria(id), PageRequest.of(0, 10));
        if (permissionRoleRelationList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.ROLE_PERMISSION_RELATION_NOT_EXIST);
        }
        if (permissionRoleRelationList.getData() == null) {
            return RestUtil.toRest(RestEnum.ROLE_PERMISSION_RELATION_NOT_EXIST);
        }
        List<UcPermissionRoleRelationDto> permissionRoleRelationDtoList = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(permissionRoleRelationList.getData())).getJSONArray("data")), UcPermissionRoleRelationDto.class);
        if (permissionRoleRelationDtoList.isEmpty()) {
            return RestUtil.toRest(RestEnum.ROLE_PERMISSION_RELATION_NOT_EXIST);
        }
        RestResult rest = valid(permissionRoleRelationDtoList.get(0).getRoleId());
        if (rest != null) {
            return rest;
        }
        return null;
    }

    private RestResult valid(String roleIds) {
        for (String roleId : roleIds.split(",")) {
            RestResult roleList = CallUtil.query(dataServerProperties.getUserUrl() + "/role", new UcRoleCriteria(roleId), PageRequest.of(0, 1));
            if (roleList.getCode() != 0) {
                return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
            }
            if (roleList.getData() == null) {
                return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
            }
            List<UcRoleDto> roles = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(roleList.getData())).getJSONArray("data")), UcRoleDto.class);
            if (roles.isEmpty()) {
                return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
            }
            if (!roles.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
                return RestUtil.toRest(RestEnum.NOT_POWER);
            }
        }
        return null;
    }

}
