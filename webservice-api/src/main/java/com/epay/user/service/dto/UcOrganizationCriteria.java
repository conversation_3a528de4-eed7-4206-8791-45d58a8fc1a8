package com.epay.user.service.dto;

import com.epay.annotation.Query;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Data
public class UcOrganizationCriteria {

    public UcOrganizationCriteria() {

    }

    public UcOrganizationCriteria(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    @Query(type = Query.Type.INNER_LIKE)
    private String email;

    @Query(type = Query.Type.INNER_LIKE)
    private String orgId;

    @Query(type = Query.Type.IN, propName = "orgId")
    private List<String> orgIds;

    @Query(type = Query.Type.INNER_LIKE)
    private String orgName;

    private String merCode;

    @Query(type = Query.Type.INNER_LIKE)
    private String country;

    @Query(type = Query.Type.EQUAL)
    private String status;

    @Query(type = Query.Type.EQUAL)
    private String valid;

    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    private Timestamp startTime;

    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    private Timestamp endTime;

}
