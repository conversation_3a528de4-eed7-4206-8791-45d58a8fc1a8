package com.epay.user.service;


import org.springframework.data.domain.Pageable;
import com.epay.user.domain.UcOrganizationMerchantPossess;
import com.epay.user.service.dto.UcOrganizationMerchantPossessAddUpdate;
import com.epay.user.service.dto.UcOrganizationMerchantPossessCriteria;
import com.epay.utils.RestResult;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcOrganizationMerchantPossessService {


    /**
     * 查询许可
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcOrganizationMerchantPossessCriteria criteria, Pageable pageable);

    /**
     * 创建许可
     * @param create
     */
    RestResult create(UcOrganizationMerchantPossessAddUpdate create);

    /**
     * 更新许可
     * @param update
     */
    void update(UcOrganizationMerchantPossess update);

    /**
     * 删除许可
     * @param id
     */
    void delete(Integer id);
}
