package com.epay.user.service;


import org.springframework.data.domain.Pageable;
import com.epay.user.service.dto.UcUserAuthorityAddUpdate;
import com.epay.user.service.dto.UcUserAuthorityCriteria;
import com.epay.utils.RestResult;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcUserAuthorityService {


    /**
     * 查询用户对商户权限操作功能许可
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcUserAuthorityCriteria criteria, Pageable pageable);

    /**
     * 创建用户对商户权限操作功能许可
     * @param create
     */
    RestResult create(UcUserAuthorityAddUpdate create);

    /**
     * 删除用户对商户权限操作功能许可
     * @param id
     */
    RestResult delete(String id);
}
