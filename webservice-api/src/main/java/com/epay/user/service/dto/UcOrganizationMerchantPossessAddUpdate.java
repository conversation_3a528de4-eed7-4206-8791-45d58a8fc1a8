package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcOrganizationMerchantPossessAddUpdate implements Serializable {

	@NotEmpty(message = "orgId not empty")
	private String orgId;

	@NotEmpty(message = "merCode not empty")
	private String merCode;


}
