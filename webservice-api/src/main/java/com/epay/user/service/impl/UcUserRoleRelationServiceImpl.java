package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.epay.base.login.UcUserLoginDto;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.domain.UcUserRoleRelation;
import com.epay.user.service.UcUserRoleRelationService;
import com.epay.user.service.dto.*;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcUserRoleRelationServiceImpl implements UcUserRoleRelationService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询用户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcUserRoleRelationCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/role/relation", criteria, pageable);
    }

    /**
     * 创建用户角色和权限关系
     *
     * @param create
     * @return
     */
    @Override
    public RestResult create(UcUserRoleRelationAdd create) {
        RestResult rest = valid(create.getRoleId(), create.getUserId());
        if (rest != null) {
            return rest;
        }
        return CallUtil.create(dataServerProperties.getUserUrl() + "/role/relation", create);
    }

    /**
     * 删除用户角色和权限关系
     *
     * @param id
     * @return
     */
    @Override
    public RestResult delete(Integer id) {
        RestResult rest = valid(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/role/relation/" + id, id);
    }

    private RestResult valid(Integer id) {
        RestResult roleList = CallUtil.query(dataServerProperties.getUserUrl() + "/role/relation/", new UcUserRoleRelationCriteria(id), PageRequest.of(0, 1));
        if (roleList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.USER_ROLE_RELATION_NOT_EXIST);
        }
        if (roleList.getData() == null) {
            return RestUtil.toRest(RestEnum.USER_ROLE_RELATION_NOT_EXIST);
        }
        List<UcUserRoleRelation> userRoleRelations = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(roleList.getData())).getJSONArray("data")), UcUserRoleRelation.class);
        if (userRoleRelations.isEmpty()) {
            return RestUtil.toRest(RestEnum.USER_ROLE_RELATION_NOT_EXIST);
        }
        if (!userRoleRelations.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }


    private RestResult valid(String roleId, String userId) {
        RestResult roleList = CallUtil.query(dataServerProperties.getUserUrl() + "/role", new UcRoleCriteria(roleId), PageRequest.of(0, 1));
        if (roleList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        if (roleList.getData() == null) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        List<UcRoleDto> roles = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(roleList.getData())).getJSONArray("data")), UcRoleDto.class);
        if (roles.isEmpty()) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        if (!roles.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        if (!roles.get(0).getCreator().equals("DP")) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", new UcUserCriteria(userId), PageRequest.of(0, 1));
        if (userList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (userList.getData() == null) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
        if (users.isEmpty()) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (!users.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }


}
