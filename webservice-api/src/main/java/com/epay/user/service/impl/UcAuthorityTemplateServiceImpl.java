package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcAuthorityTemplateService;
import com.epay.user.service.dto.UcAuthorityTemplateAdd;
import com.epay.user.service.dto.UcAuthorityTemplateCriteria;
import com.epay.user.service.dto.UcAuthorityTemplateDto;
import com.epay.user.service.dto.UcAuthorityTemplateUpdate;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcAuthorityTemplateServiceImpl implements UcAuthorityTemplateService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询用户对商户的操作许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcAuthorityTemplateCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/authority/template", criteria, pageable);
    }

    /**
     * 创建用户对商户的操作许可
     *
     * @param create
     */
    @Override
    public RestResult create(UcAuthorityTemplateAdd create) {
        create.setCreatorId(SecurityUtils.getCurrentLoginUser().getUserId());
        return CallUtil.create(dataServerProperties.getUserUrl() + "/authority/template", create);
    }

    /**
     * 更新用户对商户的操作许可
     *
     * @param update
     * @return
     */
    @Override
    public RestResult update(UcAuthorityTemplateUpdate update) {
        RestResult res = getRestResult(update.getId());
        if (res != null) return res;
        return CallUtil.update(dataServerProperties.getUserUrl() + "/authority/template/" + update.getId(), update);
    }

    /**
     * 删除用户对商户的操作许可
     *
     * @param id
     */
    @Override
    public RestResult delete(Integer id) {
        RestResult res = getRestResult(id);
        if (res != null) return res;
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/authority/template/" + id, id);
    }

    private RestResult getRestResult(Integer id) {
        RestResult res = CallUtil.query(dataServerProperties.getUserUrl() + "/authority/template", new UcAuthorityTemplateCriteria(id), PageRequest.of(0, 5));
        if (res.getCode() != 0) {
            return res;
        }
        List<UcAuthorityTemplateDto> authorityTemplates = JSON.parseArray(JSON.parseObject(JSON.toJSONString(res.getData())).getString("data"), UcAuthorityTemplateDto.class);
        if (authorityTemplates.isEmpty()) {
            return RestUtil.toRest(RestEnum.TEMPLATE_NOT_EXIST);
        }
        if (!authorityTemplates.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }

}
