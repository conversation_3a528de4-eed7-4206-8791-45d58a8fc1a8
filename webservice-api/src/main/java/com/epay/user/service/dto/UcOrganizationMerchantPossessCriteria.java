package com.epay.user.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcOrganizationMerchantPossessCriteria implements Serializable {


	@Query(type = Query.Type.INNER_LIKE)
	private String orgId;

	@Query(type = Query.Type.IN, propName = "orgId")
	private List<String> orgIds;

	@Query(type = Query.Type.INNER_LIKE)
	private String merCode;


}
