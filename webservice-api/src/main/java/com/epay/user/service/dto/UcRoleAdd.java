package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * 角色
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcRoleAdd {

    @NotEmpty(message = "角色名称不能为空")
    private String roleName;

    private String orgId;

    private Integer level = 2;

    private String info;

    private String type = "private";

    private String creator = "DP";

    private String creatorId;

}
