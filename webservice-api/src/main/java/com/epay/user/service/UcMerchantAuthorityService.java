package com.epay.user.service;


import org.springframework.data.domain.Pageable;
import com.epay.user.service.dto.UcMerchantAuthorityAdd;
import com.epay.user.service.dto.UcMerchantAuthorityCriteria;
import com.epay.utils.RestResult;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface UcMerchantAuthorityService {


    /**
     * 查询机构内的商户和许可关系的关联
     *
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcMerchantAuthorityCriteria criteria, Pageable pageable);

    /**
     * 建立机构内的商户和机构内的商户和许可关系的关联关系的关联
     *
     * @param create
     */
    RestResult create(UcMerchantAuthorityAdd create);

    /**
     * 删除机构内的商户和许可关系的关联
     *
     * @param id
     */
    RestResult delete(Integer id);
}
