package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class UcPermissionRoleRelationCriteria implements Serializable {

    public UcPermissionRoleRelationCriteria() {
    }

    public UcPermissionRoleRelationCriteria(Integer id) {
        this.id = id;
    }

    private Integer id;

    private String roleId;

    private String permissionCode;

    private String orgId;

}
