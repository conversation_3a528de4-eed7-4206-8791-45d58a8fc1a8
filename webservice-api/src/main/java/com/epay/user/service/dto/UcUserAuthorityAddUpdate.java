package com.epay.user.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcUserAuthorityAddUpdate implements Serializable {

	@NotEmpty(message = "userId not null")
	private String userId;

	@NotEmpty(message = "authorityIds not empty")
	private String authorityIds;

	@NotEmpty(message = "orgId not empty")
	private String orgId;

}
