package com.epay.user.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class UcMerchantAuthorityCriteria implements Serializable {

    public UcMerchantAuthorityCriteria() {
    }

    public UcMerchantAuthorityCriteria(List<String> authorityIds) {
        this.authorityIds = authorityIds;
    }

    @Query(type = Query.Type.EQUAL)
    private Integer orgId;

    @Query(type = Query.Type.EQUAL)
    private String merCode;

    @Query(type = Query.Type.EQUAL)
    private String permissionCode;

    private String permissionName;

    private String superCode;

    private String userId;

    @Query(type = Query.Type.EQUAL)
    private String authorityId;

    private List<String> authorityIds;

    @Query(type = Query.Type.INNER_LIKE)
    private String authorityName;


}
