package com.epay.user.service.dto;

import com.epay.annotation.Query;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Data
public class UcPermissionCriteria {


    @NotEmpty(message = "权限ID不能为空")
    @Query(type = Query.Type.INNER_LIKE)
    private String permissionCode;

    @NotEmpty(message = "权限名称不能为空")
    @Query(type = Query.Type.INNER_LIKE)
    private String permissionName;

    @NotEmpty(message = "角色ID不能为空")
    private String roleId;

    @Query(type = Query.Type.EQUAL)
    private Integer level;

    @Query(type = Query.Type.EQUAL)
    private Integer superCode;

}
