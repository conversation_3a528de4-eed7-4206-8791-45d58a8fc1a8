package com.epay.user.service.dto;

import lombok.Data;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
public class UcRoleCriteria {

	public UcRoleCriteria() {
	}

	public UcRoleCriteria(Integer id) {
		this.id = id;
	}

	public UcRoleCriteria(String roleId) {
		this.roleId = roleId;
	}

	private Integer id;

	private String roleId;

	private String orgId;

	private String roleName;

	private String userId;

	private String creator;

	private List<String> roleIds;

}
