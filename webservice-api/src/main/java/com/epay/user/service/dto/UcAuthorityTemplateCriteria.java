package com.epay.user.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcAuthorityTemplateCriteria implements Serializable {

	public UcAuthorityTemplateCriteria() {
	}

	public UcAuthorityTemplateCriteria(Integer id) {
		this.id = id;
	}

	private Integer id;

	@Query(type = Query.Type.INNER_LIKE)
	private String templateName;

	@Query(type = Query.Type.EQUAL)
	private String orgId;

	private String roleId;

}
