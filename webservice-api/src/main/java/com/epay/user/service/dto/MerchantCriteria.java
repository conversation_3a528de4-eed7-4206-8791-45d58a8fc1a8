package com.epay.user.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
@AllArgsConstructor
public class MerchantCriteria {

	public MerchantCriteria() {
	}

	public MerchantCriteria(String merCode) {
		this.merCode = merCode;
	}

	private String insCode;
	private String merCode;
	private String merName;
	private String orgId;
	private String orgName;
	private String merStatus;
	private String type;
	private String startTime;
	private String endTime;

}
