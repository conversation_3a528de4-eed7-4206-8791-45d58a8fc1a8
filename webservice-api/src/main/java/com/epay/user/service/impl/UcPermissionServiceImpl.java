package com.epay.user.service.impl;

import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.domain.UcPermission;
import com.epay.user.service.UcPermissionService;
import com.epay.user.service.dto.UcPermissionCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcPermissionServiceImpl implements UcPermissionService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcPermissionCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/permission", criteria, pageable);
    }

    /**
     * 创建许可
     *
     * @param create
     */
    @Override
    public RestResult create(UcPermission create) {
        return CallUtil.create(dataServerProperties.getUserUrl() + "/permission", create);
    }


    /**
     * 更新许可
     *
     * @param update
     */
    @Override
    public void update(UcPermission update) {
        CallUtil.update(dataServerProperties.getUserUrl() + "/permission", update);
    }

    /**
     * 删除许可
     *
     * @param id
     */
    @Override
    public RestResult delete(Integer id) {
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/permission/" + id, id);
    }

}
