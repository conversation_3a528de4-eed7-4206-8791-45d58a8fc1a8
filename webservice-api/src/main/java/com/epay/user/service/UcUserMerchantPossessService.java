package com.epay.user.service;


import org.springframework.data.domain.Pageable;
import com.epay.user.service.dto.UcUserMerchantPossessAdd;
import com.epay.user.service.dto.UcUserMerchantPossessCriteria;
import com.epay.utils.RestResult;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcUserMerchantPossessService {


    /**
     * 查询用户和商户关联
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcUserMerchantPossessCriteria criteria, Pageable pageable);

    /**
     * 创建用户和商户关联
     * @param create
     */
    RestResult create(UcUserMerchantPossessAdd create);

    /**
     * 删除用户和商户关联
     * @param id
     */
    RestResult delete(String id);
}
