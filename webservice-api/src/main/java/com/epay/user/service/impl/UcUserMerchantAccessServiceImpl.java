package com.epay.user.service.impl;

import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.domain.UcUserMerchantAccess;
import com.epay.user.service.UcUserMerchantAccessService;
import com.epay.user.service.dto.UcUserMerchantAccessCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcUserMerchantAccessServiceImpl implements UcUserMerchantAccessService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询用户对商户的操作许可
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcUserMerchantAccessCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/merchant/access", criteria, pageable);
    }

    /**
     * 创建用户对商户的操作许可
     *
     * @param create
     */
    @Override
    public RestResult create(UcUserMerchantAccess create) {
        return CallUtil.create(dataServerProperties.getUserUrl() + "/merchant/access", create);
    }

    /**
     * 删除用户对商户的操作许可
     *
     * @param id
     */
    @Override
    public RestResult delete(Integer id) {
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/merchant/access/" + id, id);
    }

}
