package com.epay.user.service;


import com.epay.user.domain.UcUserUpdateStatus;
import com.epay.user.service.dto.*;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface UcUserService {


    /**
     * 查询用户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcUserCriteria criteria, Pageable pageable) throws Exception;

    /**
     * 创建用户
     *
     * @param create
     */
    RestResult create(UcUserAdd create);


    /**
     * 更新用户
     *
     * @param update
     */
    RestResult update(UcUserUpdate update, Integer id);

    /**
     * 重置密码
     * @param resetPasswd
     * @return
     */
    RestResult resetPassword(UcUserResetPasswd resetPasswd);

    /**
     * 重置自己密码
     * @param resetSelfPasswd
     * @return
     */
    RestResult resetSelfPassword(UcUserResetSelfPasswd resetSelfPasswd);

    /**
     * 删除用户
     *
     * @param id
     */
    RestResult delete(Integer id);

    /**
     * 用户状态更新
     * @param update
     * @param id
     * @return
     */
    RestResult status(UcUserUpdateStatus update, Integer id);
}
