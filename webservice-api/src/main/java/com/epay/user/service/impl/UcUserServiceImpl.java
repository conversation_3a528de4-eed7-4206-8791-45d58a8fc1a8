package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.epay.base.login.UcUserLoginDto;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.domain.UcUserUpdateStatus;
import com.epay.user.service.UcUserService;
import com.epay.user.service.dto.*;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcUserServiceImpl implements UcUserService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询用户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcUserCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        List<String> types = new ArrayList<>();
        types.add("RoleUser");
        types.add("PrimaryUser");
        criteria.setType(types);
        return CallUtil.query(dataServerProperties.getUserUrl() + "/user", criteria, pageable);
    }

    /**
     * 创建用户
     *
     * @param create
     */
    @Override
    public RestResult create(UcUserAdd create) {
        create.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return CallUtil.create(dataServerProperties.getUserUrl() + "/user", create);
    }

    /**
     * 更新用户
     *
     * @param update
     */
    @Override
    public RestResult update(UcUserUpdate update, Integer id) {
        RestResult rest = valid(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.update(dataServerProperties.getUserUrl() + "/user/" + id, update);
    }

    /**
     * 重置密码
     *
     * @param resetPasswd
     */
    @Override
    public RestResult resetPassword(UcUserResetPasswd resetPasswd) {
        resetPasswd.setOldPassword(null);
        RestResult rest = valid(resetPasswd.getUserId());
        if (rest != null) {
            return rest;
        }
        return CallUtil.put(dataServerProperties.getUserUrl() + "/user/resetpasswd", resetPasswd);
    }

    /**
     * 重置自己的密码
     * @param resetSelfPasswd
     * @return
     */
    @Override
    public RestResult resetSelfPassword(UcUserResetSelfPasswd resetSelfPasswd) {
        UcUserResetPasswd resetPasswd = new UcUserResetPasswd();
        resetPasswd.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
        resetPasswd.setPassword(resetSelfPasswd.getPassword());
        resetPasswd.setOldPassword(resetSelfPasswd.getOldPassword());
        return CallUtil.put(dataServerProperties.getUserUrl() + "/user/resetpasswd", resetPasswd);
    }

    /**
     * 删除用户
     *
     * @param id
     */
    @Override
    public RestResult delete(Integer id) {
        RestResult rest = valid(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/user/" + id, id);
    }

    /**
     * 用户状态更新
     *
     * @param update
     * @param id
     * @return
     */
    @Override
    public RestResult status(UcUserUpdateStatus update, Integer id) {
        RestResult rest = valid(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.update(dataServerProperties.getUserUrl() + "/user/status/" + id, update);
    }

    private RestResult valid(Integer id) {
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", new UcUserCriteria(id), PageRequest.of(0,10));
        return valid(userList);
    }

    private RestResult valid(String userId) {
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", new UcUserCriteria(userId), PageRequest.of(0,10));
        return valid(userList);
    }

    private RestResult valid(RestResult userList) {
        if (userList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (userList.getData() == null) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
        if (users.isEmpty()) {
            return RestUtil.toRest(RestEnum.USER_NOT_EXIST);
        }
        if (!users.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }

}
