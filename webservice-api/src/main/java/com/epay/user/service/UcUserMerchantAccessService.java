package com.epay.user.service;


import com.epay.user.domain.UcUserMerchantAccess;
import com.epay.user.service.dto.UcUserMerchantAccessCriteria;
import com.epay.user.service.dto.UcUserMerchantAccessDto;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcUserMerchantAccessService {


    /**
     * 查询用户对商户的操作许可
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcUserMerchantAccessCriteria criteria, Pageable pageable);

    /**
     * 创建用户对商户的操作许可
     * @param create
     */
    RestResult create(UcUserMerchantAccess create);
    /**
     * 删除用户对商户的操作许可
     * @param id
     */
    RestResult delete(Integer id);
}
