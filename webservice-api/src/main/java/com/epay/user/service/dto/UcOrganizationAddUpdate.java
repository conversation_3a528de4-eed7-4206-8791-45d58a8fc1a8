package com.epay.user.service.dto;

import com.epay.validated.AddVerify;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 组织
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class UcOrganizationAddUpdate {


    private Integer id;

    @NotEmpty(message = "机构名称不能为空", groups = {AddVerify.class})
    private String orgName;

    private String email;

    private String phoneNumber;

    private Integer status;

    private String contact;

    private String address;

    private String country;

    private String registration;

    private String merCode;

    @Valid
    private List<UcUserAdd> users;

}
