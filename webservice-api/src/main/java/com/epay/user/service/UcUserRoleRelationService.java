package com.epay.user.service;


import com.epay.user.service.dto.UcUserRoleRelationAdd;
import com.epay.user.service.dto.UcUserRoleRelationCriteria;
import com.epay.user.service.dto.UcUserRoleRelationDto;
import com.epay.utils.PageResult;
import org.springframework.data.domain.Pageable;
import com.epay.utils.RestResult;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface UcUserRoleRelationService {


    /**
     * 查询用户角色和权限关系
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcUserRoleRelationCriteria criteria, Pageable pageable);

    /**
     * 创建用户角色和权限关系
     * @param create
     * @return
     */
    RestResult create(UcUserRoleRelationAdd create);

    /**
     * 删除用户角色和权限关系
     * @param id
     * @return
     */
    RestResult delete(Integer id);
}
