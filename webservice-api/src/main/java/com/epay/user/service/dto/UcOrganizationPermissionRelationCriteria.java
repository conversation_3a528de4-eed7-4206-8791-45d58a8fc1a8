package com.epay.user.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class UcOrganizationPermissionRelationCriteria implements Serializable {


    public UcOrganizationPermissionRelationCriteria() {

    }

    public UcOrganizationPermissionRelationCriteria(String orgId) {
        this.orgId = orgId;
    }

    @Query(type = Query.Type.EQUAL)
    private String orgId;


}
