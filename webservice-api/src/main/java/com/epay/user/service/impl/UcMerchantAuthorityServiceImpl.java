package com.epay.user.service.impl;

import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcMerchantAuthorityService;
import com.epay.user.service.dto.UcMerchantAuthorityAdd;
import com.epay.user.service.dto.UcMerchantAuthorityCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcMerchantAuthorityServiceImpl implements UcMerchantAuthorityService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询机构内的商户和许可关系的关联
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcMerchantAuthorityCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/authority", criteria, pageable);
    }

    /**
     * 建立机构内的商户和许可关系的关联
     *
     * @param create
     */
    @Override
    public RestResult create(UcMerchantAuthorityAdd create) {
        return CallUtil.create(dataServerProperties.getUserUrl() + "/authority", create);
    }

    /**
     * 删除机构内的商户和许可关系的关联
     *
     * @param id
     */
    @Override
    public RestResult delete(Integer id) {
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/authority/" + id, id);
    }

}
