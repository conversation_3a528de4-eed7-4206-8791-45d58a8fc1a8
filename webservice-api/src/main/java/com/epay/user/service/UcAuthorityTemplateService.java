package com.epay.user.service;


import com.epay.user.service.dto.UcAuthorityTemplateAdd;
import com.epay.user.service.dto.UcAuthorityTemplateCriteria;
import com.epay.user.service.dto.UcAuthorityTemplateUpdate;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface UcAuthorityTemplateService {


    /**
     * 查询用户的权限模板
     *
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult queryAll(UcAuthorityTemplateCriteria criteria, Pageable pageable);

    /**
     * 创建用户的权限模板
     *
     * @param create
     */
    RestResult create(UcAuthorityTemplateAdd create);

    /**
     * 更新用户的权限模板
     * @param update
     * @return
     */
    RestResult update(UcAuthorityTemplateUpdate update);

    /**
     * 删除用户的权限模板
     *
     * @param id
     */
    RestResult delete(Integer id);

}
