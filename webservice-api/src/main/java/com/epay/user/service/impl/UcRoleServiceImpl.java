package com.epay.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.epay.base.login.UcUserLoginDto;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.epay.config.bean.DataServerProperties;
import com.epay.user.service.UcRoleService;
import com.epay.user.service.dto.*;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class UcRoleServiceImpl implements UcRoleService {

    private final DataServerProperties dataServerProperties;

    /**
     * 查询角色
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public RestResult queryAll(UcRoleCriteria criteria, Pageable pageable) {
        return CallUtil.query(dataServerProperties.getUserUrl() + "/role", criteria, pageable);
    }

    /**
     * 创建角色
     *
     * @param create
     */
    @Override
    public RestResult create(UcRoleAdd create) {
        create.setCreatorId(SecurityUtils.getCurrentLoginUser().getUserId());
        return CallUtil.create(dataServerProperties.getUserUrl() + "/role", create);
    }

    /**
     * 修改角色
     *
     * @param update
     * @return
     */
    @Override
    public RestResult update(UcRoleUpdate update) {
        RestResult rest = valid(update.getId());
        if (rest != null) {
            return rest;
        }
        return CallUtil.update(dataServerProperties.getUserUrl() + "/role/" + update.getId(), update);
    }

    /**
     * 删除角色
     *
     * @param id
     */
    @Override
    public RestResult delete(Integer id) {
        RestResult rest = valid(id);
        if (rest != null) {
            return rest;
        }
        return CallUtil.delete(dataServerProperties.getUserUrl() + "/role/" + id, id);
    }

    private RestResult valid(Integer id) {
        RestResult roleList = CallUtil.query(dataServerProperties.getUserUrl() + "/role", new UcRoleCriteria(id), PageRequest.of(0, 1));
        if (roleList.getCode() != 0) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        if (roleList.getData() == null) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        List<UcRoleDto> roles = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(roleList.getData())).getJSONArray("data")), UcRoleDto.class);
        if (roles.isEmpty()) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        if (!roles.get(0).getOrgId().equals(SecurityUtils.getCurrentLoginUser().getOrgId())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        if (roles.get(0).getCreator().equals("DP")) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }
        return null;
    }


}
