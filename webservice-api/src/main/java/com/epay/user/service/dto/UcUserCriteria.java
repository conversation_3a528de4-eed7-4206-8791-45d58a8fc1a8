package com.epay.user.service.dto;

import lombok.Data;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
public class UcUserCriteria {

	public UcUserCriteria() {
	}

	public UcUserCriteria(Integer id) {
		this.id = id;
	}

	public UcUserCriteria(String userId) {
		this.userId = userId;
	}

	public UcUserCriteria(List<String> userIds) {
		this.userIds = userIds;
	}

	private String searchKeyword;

	private Integer id;

	private String email;

	private String userId;

	private List<String> userIds;

	private String orgId;
//
//	private String orgName;

	private String merCode;

	private String username;

//	private Integer status;

	private String roleName;

	private List<String> type;

//	private List<Timestamp> createTime;

}
