package com.epay.user.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 商户信息
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class TblInsCfg implements Serializable {

    private String insCode;

    private String insCountry;

    private String insStatus;

    private String appId;

    private String amlStatus;

    private String insProp;

    private String insName;

    private String comQueueId;

    private String createTime;

    private String updateTime;

    @JSONField(name = "INS_CODE")
    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    @JSONField(name = "INS_COUNTRY")
    public void setInsCountry(String insCountry) {
        this.insCountry = insCountry;
    }

    @JSONField(name = "INS_STATUS")
    public void setInsStatus(String insStatus) {
        this.insStatus = insStatus;
    }

    @JSONField(name = "APP_ID")
    public void setAppId(String appId) {
        this.appId = appId;
    }

    @JSONField(name = "AML_STATUS")
    public void setAmlStatus(String amlStatus) {
        this.amlStatus = amlStatus;
    }

    @JSONField(name = "INS_PROP")
    public void setInsProp(String insProp) {
        this.insProp = insProp;
    }

    @JSONField(name = "INS_ENG_NAME")
    public void setInsName(String insName) {
        this.insName = insName;
    }

    @JSONField(name = "COM_QUEUE_ID")
    public void setComQueueId(String comQueueId) {
        this.comQueueId = comQueueId;
    }

    @JSONField(name = "COM_QUEUE_ID")
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @JSONField(name = "COM_QUEUE_ID")
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
