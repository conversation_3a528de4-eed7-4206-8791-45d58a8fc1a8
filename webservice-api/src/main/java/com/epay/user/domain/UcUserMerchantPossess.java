package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 用户和商户关联
 * <AUTHOR>
 * @date 2024/4/20
 */
@Entity
@Getter
@Setter
@Table(name="uc_user_merchant_possess")
public class UcUserMerchantPossess extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "用户ID不能为空")
    private String userId;

    private String merName;

    @NotNull(message = "商户代码不能为空")
    private String merCode;

    private Integer isUpopMerchant;

    private String innCode;
}
