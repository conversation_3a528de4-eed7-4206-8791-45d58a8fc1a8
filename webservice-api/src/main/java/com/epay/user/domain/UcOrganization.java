package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

/**
 * 组织
* <AUTHOR>
* @date 2024-04-14
*/
@Entity
@Getter
@Setter
@Table(name="uc_organization")
public class UcOrganization extends BaseEntity {

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String orgName;

    private String orgId;

    private String phoneNumber;

    private Integer merchant_num;

    private String email;

    private String contact;

    private String address;

    private String country;

    private String registration;

    private Integer status;




}
