package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Entity
@Getter
@Setter
@Table(name="mps_inn_acquirer_type")
@Where(clause = "valid = 1")
public class MpsInnAcquirerType extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String location;

    private String type;

    private String acquirer;

    private String isPos;

    private String isOnline;

    private String isBillpay;

}
