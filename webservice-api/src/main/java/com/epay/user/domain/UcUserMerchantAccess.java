package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 用户和商户访问关联
 * <AUTHOR>
 * @date 2024/4/20
 */
@Entity
@Getter
@Setter
@Table(name="uc_user_merchant_access")
public class UcUserMerchantAccess extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "用户ID不能为空")
    private String userId;

    @NotNull(message = "商户ID不能为空")
    private String merCode;

    @NotNull(message = "权限ID不能为空")
    private String permissionCode;

    @NotEmpty(message = "角色不能为空")
    private String role;

    @NotNull(message = "功能不能为空")
    private String operation;
}
