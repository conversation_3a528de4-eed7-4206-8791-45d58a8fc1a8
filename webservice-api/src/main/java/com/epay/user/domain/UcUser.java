package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户
* <AUTHOR>
* @date 2024-04-14
*/
@Entity
@Getter
@Setter
@Table(name="uc_user")
public class UcUser extends BaseEntity {

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "邮箱不能为空")
    private String email;

    private String username;

    private String userId;

    private String password;

    private String phoneNumber;

	/**
	 * student is 1
	 */
    @NotNull(message = "角色不能为空")
    private String role;

    private Integer status;

	/**
	 * id card is 1,passport is 2,driver license is 3
	 */
    private Integer certType;

    private String certId;

    private String orgName;

    @NotEmpty(message = "组织机构ID不能为空")
    private String orgId;

    private Integer gender;

    private String address;

    private String avatarUrl;

    private String country;

    private String provinces;


}
