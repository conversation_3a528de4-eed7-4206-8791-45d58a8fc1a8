package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * 角色
* <AUTHOR>
* @date 2024-04-14
*/
@Entity
@Getter
@Setter
@Table(name="uc_role")
public class UcRole extends BaseEntity{

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "角色名称不能为空")
    private String role;

    @NotEmpty(message = "角色说明不能为空")
    private String info;



}
