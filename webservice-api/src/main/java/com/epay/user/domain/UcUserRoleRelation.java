package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户角色关联
* <AUTHOR>
* @date 2024-04-14
*/
@Entity
@Getter
@Setter
@Table(name="uc_user_role_relation")
public class UcUserRoleRelation extends BaseEntity {

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String roleId;

    private String userId;

    private String orgId;

    private String info;

    private String type = "private";



}
