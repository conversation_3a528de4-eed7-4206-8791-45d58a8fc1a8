package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 组织和商户关联
 * <AUTHOR>
 * @date 2024/4/20
 */
@Entity
@Getter
@Setter
@Table(name="uc_organization_merchant_possess")
public class UcOrganizationMerchantPossess extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String orgId;

    private String merName;

    private String merCode;

    private Integer isUpopMerchant;

    private String insCode;

}
