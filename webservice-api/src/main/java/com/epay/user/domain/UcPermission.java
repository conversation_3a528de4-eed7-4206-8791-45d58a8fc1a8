package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;

/**
 * 许可
 * <AUTHOR>
 * @date 2024/4/20
 */
@Entity
@Getter
@Setter
@Table(name="uc_permission")
public class UcPermission extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String permissionCode;

    @NotEmpty(message = "权限的内容不能为空")
    private String target;

    @NotEmpty(message = "权限的模式不能为空")
    private String mode;

    @NotEmpty(message = "许可权限说明不能为空")
    private String info;

}
