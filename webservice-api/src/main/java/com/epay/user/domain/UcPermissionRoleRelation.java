package com.epay.user.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;

/**
 * 角色和许可联系
 * <AUTHOR>
 * @date 2024/4/20
 */
@Entity
@Getter
@Setter
@Table(name="uc_permission_role_relation")
public class UcPermissionRoleRelation extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "许可代码不能为空")
    private String permissionCode;

    @NotEmpty(message = "许可角色不能为空")
    private String role;

}
