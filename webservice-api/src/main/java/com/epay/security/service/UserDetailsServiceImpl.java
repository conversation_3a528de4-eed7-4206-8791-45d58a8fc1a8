package com.epay.security.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epay.base.login.AuthorityDto;
import com.epay.base.login.JwtUserDto;
import com.epay.base.login.UcUserLoginDto;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.BadRequestException;
import com.epay.management.domain.MerchantInfoDetail;
import com.epay.management.enums.GatewayEnum;
import com.epay.management.service.MerchantService;
import com.epay.user.service.dto.UcMerchantAuthorityDto;
import com.epay.user.service.dto.UcUserCriteria;
import com.epay.user.service.dto.UcUserLoginReq;
import com.epay.user.service.dto.UcUserMerchantPossessCriteria;
import com.epay.user.service.dto.UcUserMerchantPossessDto;
import com.epay.user.service.UcUserMerchantPossessService;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Slf4j
@RequiredArgsConstructor
@Service("userDetailsService")
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserCacheManager userCacheManager;
    private final DataServerProperties dataServerProperties;
    private final UcUserMerchantPossessService ucUserMerchantPossessService;
    private final MerchantService merchantService;

    @Override
    public JwtUserDto loadUserByUsername(String account) {
        JwtUserDto jwtUserDto = userCacheManager.getUserCache(account);
        if (jwtUserDto == null) {
            UcUserLoginDto user = null;
            List<String> permissions;
            List<String> orgPermissions;
            Map<String, List<String>> authMap;
            List<String> merchantIds;
            Map<String, List<String>> merchantPaymentMethods;
            try {
                UcUserCriteria userCriteria = new UcUserCriteria();
                userCriteria.setEmail(account);
                RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", userCriteria, PageRequest.of(0, 1));
                if (userList.getCode() != 0) {
                    throw new UsernameNotFoundException("");
                }
                List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
                if (users.isEmpty()) {
                    throw new UsernameNotFoundException("");
                }
                user = users.get(0);      
                UcUserLoginReq loginReq = new UcUserLoginReq();
                loginReq.setEmail(account);
                loginReq.setLoginType(2);
                RestResult loginRes = CallUtil.create(dataServerProperties.getUserUrl() + "/auth/login", loginReq);
                if (loginRes.getCode() != 0) {
                    throw new BadRequestException(loginRes.getMessage());
                }
                JSONObject jsonObj = JSON.parseObject(JSON.toJSONString(loginRes.getData()));
                permissions = JSON.parseArray(JSON.toJSONString(jsonObj.getJSONArray("permission")),String.class);
                orgPermissions = JSON.parseArray(JSON.toJSONString(jsonObj.getJSONArray("orgPermissions")),String.class);
                authMap = JSON.parseArray(JSON.toJSONString(jsonObj.getJSONArray("merchantAuthorityList")), UcMerchantAuthorityDto.class).stream().collect(Collectors.groupingBy(UcMerchantAuthorityDto::getMerCode,
                        Collectors.mapping(UcMerchantAuthorityDto::getPermissionCode, Collectors.toList())));
                user.setOrgName(jsonObj.getString("orgName"));
                
                // 查询用户的商户ID列表
                merchantIds = getUserMerchantIds(user.getUserId());
                
                // 查询商户对应的支付方式
                merchantPaymentMethods = getMerchantPaymentMethods(merchantIds);
                
            } catch (EntityNotFoundException e) {
                throw new UsernameNotFoundException(account, e);
            }
            if (user == null) {
                throw new UsernameNotFoundException("");
            } else {
                jwtUserDto = new JwtUserDto(
                        user,
                        permissions,
                        orgPermissions,
                        authMap,
                        mapToGrantedAuthorities(user),
                        merchantIds,
                        merchantPaymentMethods
                );
                // 添加缓存数据
                userCacheManager.addUserCache(account, jwtUserDto);
            }
        }
        return jwtUserDto;
    }

    /**
     * 获取用户关联的商户ID列表
     * @param userId 用户ID
     * @return 商户ID列表
     */
    private List<String> getUserMerchantIds(String userId) {
        try {
            UcUserMerchantPossessCriteria userMerchantPossessCriteria = new UcUserMerchantPossessCriteria();
            userMerchantPossessCriteria.setUserId(userId);
            RestResult userMerchantPossess = ucUserMerchantPossessService.queryAll(userMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
            if (userMerchantPossess.getCode() == 0) {
                JSONObject organization = JSONObject.parseObject(JSON.toJSONString(userMerchantPossess.getData()));
                Integer total = organization.getInteger("total");
                if (total > 0) {
                    List<UcUserMerchantPossessDto> merchantList = JSON.parseArray(organization.getString("data"), UcUserMerchantPossessDto.class);
                    return merchantList.stream().map(UcUserMerchantPossessDto::getMerCode).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("查询用户商户关联信息失败: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 获取商户对应的支付方式信息
     * @param merchantIds 商户ID列表
     * @return 商户支付方式映射，key为商户ID，value为支付方式列表
     */
    private Map<String, List<String>> getMerchantPaymentMethods(List<String> merchantIds) {
        Map<String, List<String>> merchantPaymentMethods = new HashMap<>();
        if (merchantIds == null || merchantIds.isEmpty()) {
            return merchantPaymentMethods;
        }
        
        try {
            // 查询商户详细信息
            List<MerchantInfoDetail> merchantDetails = merchantService.merchantDetailList(merchantIds);
            if (merchantDetails != null && !merchantDetails.isEmpty()) {
                for (MerchantInfoDetail merchant : merchantDetails) {
                    String merCode = merchant.getMerCode();
                    String gateway = merchant.getGateway();
                    if (merCode != null && gateway != null) {
                        String paymentMethod = GatewayEnum.getDescByName(gateway);
                        if (paymentMethod != null) {
                            merchantPaymentMethods.put(merCode, Collections.singletonList(paymentMethod));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询商户支付方式信息失败: {}", e.getMessage());
        }
        
        return merchantPaymentMethods;
    }

    public List<AuthorityDto> mapToGrantedAuthorities(UcUserLoginDto managerDto) {
        Set<String> permissions = new HashSet<>();
        permissions.add(managerDto.getUsername());
        return permissions.stream().map(AuthorityDto::new)
                .collect(Collectors.toList());
    }
}
