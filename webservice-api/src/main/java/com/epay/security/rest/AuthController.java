package com.epay.security.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epay.annotation.AnonymousAccess;
import com.epay.base.login.JwtUserDto;
import com.epay.base.login.UcUserLoginDto;
import com.epay.config.BlowfishProperties;
import com.epay.config.bean.DataServerProperties;
import com.epay.logging.annotation.Log;
import com.epay.security.bean.LoginProperties;
import com.epay.security.bean.SecurityProperties;
import com.epay.security.bean.req.ResetPassReq;
import com.epay.security.bean.req.ValidMailCodeReq;
import com.epay.security.security.TokenProvider;
import com.epay.security.service.OnlineUserService;
import com.epay.security.service.dto.UcUserUpdatePrimary;
import com.epay.user.domain.UcUser;
import com.epay.user.service.dto.UcUserCriteria;
import com.epay.user.service.dto.UcUserLogin;
import com.epay.user.service.dto.UcUserLoginReq;
import com.epay.user.service.dto.UcUserResetPasswd;
import com.epay.user.util.CallUtil;
import com.epay.utils.*;
import com.epay.utils.constants.RedisConstants;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Email;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 授权
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/auth")
public class AuthController {

    private final SecurityProperties properties;
    private final OnlineUserService onlineUserService;
    private final TokenProvider tokenProvider;
    private final AuthenticationManagerBuilder authenticationManagerBuilder;
    private final PasswordEncoder passwordEncoder;
    private final DataServerProperties dataServerProperties;
    private final EmailSender emailSender;
    private final RedisUtils redisUtils;

    @Resource
    private LoginProperties loginProperties;

    /**
     * 登录
     *
     * @param login
     * @param request
     * @return
     */
    @Log
    @AnonymousAccess
    @PostMapping("/login")
    public ResponseEntity<RestResult> login(@RequestBody UcUserLogin login, HttpServletRequest request) {
        // 根据邮箱查询用户信息
        UcUserCriteria criteria = new UcUserCriteria();
        criteria.setEmail(login.getAccount().toLowerCase());
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", criteria, PageRequest.of(0, 1));
        if (userList.getCode() != 0) {
            return new ResponseEntity<>(RestUtil.toRest(RestEnum.USER_NOT_EXIST), HttpStatus.BAD_REQUEST);
        }
        List<UcUser> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUser.class);
        if (users.isEmpty()) {
            return new ResponseEntity<>(RestUtil.toRest(RestEnum.USER_NOT_EXIST), HttpStatus.BAD_REQUEST);
        }
        UcUser user = users.get(0);
        // 解密密码
        String pass = BlowfishUtil.encrypt(BlowfishProperties.key, login.getPassword());
        String dePass = BlowfishUtil.decrypt(BlowfishProperties.key, pass);
        // 密码校验
        if (!passwordEncoder.matches(dePass, user.getPassword())) {
            return new ResponseEntity<>(RestUtil.toRest(RestEnum.PASSWORD_ERR), HttpStatus.BAD_REQUEST);
        }
        UcUserUpdatePrimary userUpdatePrimary = new UcUserUpdatePrimary();
        userUpdatePrimary.setId(user.getId());
        RestResult primaryUserRes = CallUtil.create(dataServerProperties.getUserUrl() + "/user/updatePrimary", userUpdatePrimary);
        if (primaryUserRes.getCode() != 0) {
            return new ResponseEntity<>(RestUtil.toRest(RestEnum.PRIMARY_CALIBRATION_FAIL), HttpStatus.BAD_REQUEST);
        }
        UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(login.getAccount(), dePass);
        Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        String token = tokenProvider.createToken(authentication);
        final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", properties.getTokenStartWith() + token);
            put("roleName", jwtUserDto.getUserLoginDto().getRoleName());
            put("permission", String.join(",", jwtUserDto.getPermissions()));
            put("orgPermission", String.join(",", jwtUserDto.getOrgPermissions()));
            put("userId", jwtUserDto.getUserLoginDto().getUserId());
            put("username", jwtUserDto.getUserLoginDto().getUsername());
            put("email", jwtUserDto.getUserLoginDto().getEmail());
            put("orgId", jwtUserDto.getUserLoginDto().getOrgId());
            put("orgName", jwtUserDto.getUserLoginDto().getOrgName());
            put("type", jwtUserDto.getUserLoginDto().getType());
            put("avatarUrl", jwtUserDto.getUserLoginDto().getAvatarUrl());
            put("createTime", jwtUserDto.getUserLoginDto().getCreateTime());
            put("merchantIds", jwtUserDto.getMerchantIds());
        }};
        if (loginProperties.isSingleLogin()) {
            // 踢掉之前已经登录的token
            onlineUserService.kickOutForUsername(login.getAccount());
        }
        // 保存在线信息
        onlineUserService.save(jwtUserDto.getUserLoginDto(), token, request);
        return ResponseEntity.ok(RestUtil.ok(authInfo));
    }

    /**
     * 登出
     *
     * @param request
     * @return
     */
    @PostMapping("/logout")
    public ResponseEntity<RestResult> logout(HttpServletRequest request) {
        onlineUserService.logout(tokenProvider.getToken(request));
        return ResponseEntity.ok(RestUtil.toRest(RestEnum.SUCCESS));
    }

    /**
     * 发送邮箱验证码
     *
     * @param email
     * @return
     */
    @AnonymousAccess
    @GetMapping("/sendEmailCode")
    public ResponseEntity<RestResult> sendMailCode(@Email String email) {
        // 通过邮箱获取用户
        UcUserCriteria criteria = new UcUserCriteria();
        criteria.setEmail(email);
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", criteria, PageRequest.of(0, 1));
        if (userList.getCode() != 0) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.CALL_FAIL));
        }
        List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
        if (users.isEmpty()) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.USER_NOT_EXIST));
        }
        // 生成10位验证码
        String code = IdGenerator.generateUniqueCode();
        redisUtils.set(RedisConstants.MAIL_CODE + email, code, 180, TimeUnit.SECONDS);
        String frontEndBaseUrl = "www.xxx.com";
        String title = "Dynamic Payment - Password Reset Code";
        String content = "Code : " + code + " \n\n" + "Please enter the code to reset your password.";
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    emailSender.sendEmail(email, title, content);
                } finally {
                    Thread.currentThread().interrupt();
                }
            }
        });
        thread.start();
        return ResponseEntity.ok(RestUtil.ok());
    }

    /**
     * 验证邮箱验证码
     *
     * @param validMailCodeReq
     * @return
     */
    @AnonymousAccess
    @PostMapping("/validEmailCode")
    public ResponseEntity<RestResult> validMailCode(@RequestBody ValidMailCodeReq validMailCodeReq) {
        // 生成10位验证码
        Object code = redisUtils.get(RedisConstants.MAIL_CODE + validMailCodeReq.getEmail());
        if (code == null) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.CODE_EXPIRE));
        }
        if (!String.valueOf(code).equals(validMailCodeReq.getCode())) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.CODE_ERROR));
        }
        String uuid = UUID.randomUUID().toString();
        redisUtils.set(RedisConstants.MAIL_CODE_VALID + validMailCodeReq.getEmail(), uuid, 1, TimeUnit.HOURS);
        return ResponseEntity.ok(RestUtil.ok(new JSONObject().fluentPut("uuid", uuid)));
    }

    /**
     * 重置密码
     *
     * @param resetPassReq
     * @return
     */
    @AnonymousAccess
    @PostMapping("/resetPass")
    public ResponseEntity<RestResult> resetPass(@RequestBody ResetPassReq resetPassReq) {
        // 生成10位验证码
        Object uuid = redisUtils.get(RedisConstants.MAIL_CODE_VALID + resetPassReq.getEmail());
        if (uuid == null) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.ERR_PLEASE_REAPPLY));
        }
        if (!String.valueOf(uuid).equals(resetPassReq.getUuid())) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.ERR_PLEASE_REAPPLY));
        }
        // 通过邮箱获取用户
        UcUserCriteria criteria = new UcUserCriteria();
        criteria.setEmail(resetPassReq.getEmail());
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", criteria, PageRequest.of(0, 1));
        if (userList.getCode() != 0) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.CALL_FAIL));
        }
        List<UcUserLoginDto> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUserLoginDto.class);
        if (users.isEmpty()) {
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.USER_NOT_EXIST));
        }
        UcUserResetPasswd userResetPasswd = new UcUserResetPasswd();
        userResetPasswd.setUserId(users.get(0).getUserId());
        userResetPasswd.setPassword(resetPassReq.getNewPassword());
        return ResponseEntity.ok(CallUtil.put(dataServerProperties.getUserUrl() + "/user/resetpasswd", userResetPasswd));
    }

}
