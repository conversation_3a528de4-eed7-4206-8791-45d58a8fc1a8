package com.epay.security.security;

import org.springframework.security.web.access.AccessDeniedHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 * <AUTHOR>
 * @date 2024/5/29
 */
public class JwtAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, org.springframework.security.access.AccessDeniedException accessDeniedException) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");

        int errorCode = 403; // 自定义错误码
        String errorMessage = "Access Denied: Forbidden";

        String json = String.format("{\"code\": %d, \"message\": \"%s\"}", errorCode, errorMessage);
        response.getWriter().write(json);
    }
}
