package com.epay.security.security;

import cn.hutool.core.util.StrUtil;
import com.epay.exception.BadRequestException;
import com.epay.exception.CustomException;
import com.epay.security.bean.SecurityProperties;
import com.epay.security.service.OnlineUserService;
import com.epay.security.service.UserCacheManager;
import com.epay.security.service.dto.OnlineUserDto;
import com.epay.utils.RedisUtils;
import io.jsonwebtoken.ExpiredJwtException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public class TokenFilter extends GenericFilterBean {

    private static final Logger log = LoggerFactory.getLogger(TokenFilter.class);


    private final TokenProvider tokenProvider;
    private final SecurityProperties properties;
    private final OnlineUserService onlineUserService;
    private final UserCacheManager userCacheManager;

    /**
     * @param tokenProvider     Token
     * @param properties        JWT
     * @param onlineUserService 用户在线
     * @param userCacheManager  用户缓存工具
     */
    public TokenFilter(TokenProvider tokenProvider, SecurityProperties properties, OnlineUserService onlineUserService, UserCacheManager userCacheManager) {
        this.properties = properties;
        this.onlineUserService = onlineUserService;
        this.tokenProvider = tokenProvider;
        this.userCacheManager = userCacheManager;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String token = resolveToken(httpServletRequest);
        // 对于 Token 为空的不需要去查 Redis
        if (StrUtil.isNotBlank(token)) {
            // 根据路径区分是否验证Token
            if (httpServletRequest.getRequestURI().startsWith("/api/bill/payment") || httpServletRequest.getRequestURI().startsWith("/api/bill/payByLinkTemporary") || httpServletRequest.getRequestURI().startsWith("/api/paymentTypeTemporary")) {
                String temp = onlineUserService.getTempValue(tokenProvider.tempKey(token));
                if (!com.epay.utils.StringUtils.isEmpty(temp)) {
                    allowAccess(temp);
                }
            } else {
                OnlineUserDto onlineUserDto = null;
                boolean cleanUserCache = false;
                try {
                    String loginKey = tokenProvider.loginKey(token);
                    onlineUserDto = onlineUserService.getOne(loginKey);
                } catch (ExpiredJwtException e) {
                    log.error(e.getMessage());
                    cleanUserCache = true;
                } finally {
                    if (cleanUserCache || Objects.isNull(onlineUserDto)) {
                        userCacheManager.cleanUserCache(String.valueOf(tokenProvider.getClaims(token).get(TokenProvider.AUTHORITIES_KEY)));
                    }
                }
                if (onlineUserDto != null && StringUtils.hasText(token)) {
                    Authentication authentication = tokenProvider.getAuthentication(token);
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    // Token 续期
                    tokenProvider.checkRenewal(token);
                }
            }
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    /**
     * 允许访问
     */
    private void allowAccess(String orderNum) {
        SecurityContextHolder.getContext().setAuthentication(new UsernamePasswordAuthenticationToken(orderNum, null,
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_ANONYMOUS"))));
    }

    /**
     * 初步检测Token
     *
     * @param request /
     * @return /
     */
    private String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(properties.getHeader());
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(properties.getTokenStartWith())) {
            // 去掉令牌前缀
            return bearerToken.replace(properties.getTokenStartWith(), "");
        } else {
            log.debug("非法Token：{}", bearerToken);
        }
        return null;
    }

    /**
     * 获取请求源
     *
     * @param request /
     * @return /
     */
    private Integer getLoginType(HttpServletRequest request) {
        String loginType = request.getHeader(properties.getLoginType());
        if (loginType != null && !loginType.isEmpty()) {
            return Integer.parseInt(loginType);
        } else {
            log.debug("未定义登录类型：{}", loginType);
        }
        return null;
    }
}
