package com.epay.security.security;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 * <AUTHOR>
 * @date 2024/5/29
 */
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");

        int errorCode = 401; // 默认错误码
        String errorMessage = "Access Denied: Unauthorized";

        String json = String.format("{\"code\": %d, \"message\": \"%s\"}", errorCode, errorMessage);
        response.getWriter().write(json);
    }
}