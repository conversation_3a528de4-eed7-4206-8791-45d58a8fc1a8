package com.epay.pay.mq;

import com.epay.config.RedisDelayQueueHandle;
import com.epay.pay.domain.EpayBill;
import com.epay.pay.domain.EpayTransaction;
import com.epay.pay.repository.EpayBillRepository;
import com.epay.pay.repository.EpayTransactionRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 账单支付超时处理类
 */
@Component
@Slf4j
@AllArgsConstructor
public class BillPaymentTimeout implements RedisDelayQueueHandle<Map> {

    private final EpayBillRepository epayBillRepository;
    private final EpayTransactionRepository epayTransactionRepository;

    @Override
    @Transactional
    public void execute(Map map) {
        log.info("收到订单支付超时延迟消息 {}", map);
        Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNum"), map.get("orderNum")));
        if (!billOptional.isPresent()) {
            log.info("订单不存在 {}", map);
            return;
        }
        EpayBill bill = billOptional.get();
        if (bill.getStatus() == 2) {
            log.info("订单已支付无需处理 {}", map);
            return;
        }
        bill.setStatus(5);
        epayBillRepository.save(bill);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(0);
        statusList.add(1);
        List<EpayTransaction> transactions = epayTransactionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(criteriaBuilder.equal(root.get("orderNum"), map.get("orderNum")),
                root.get("status").in(statusList)));
        for (EpayTransaction transaction : transactions) {
            transaction.setStatus(2);
        }
        if (!transactions.isEmpty()) {
            epayTransactionRepository.saveAll(transactions);
        }
        log.info("超时处理完成 {}", map);
    }
}
