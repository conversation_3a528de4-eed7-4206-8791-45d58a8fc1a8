package com.epay.pay.rest;

import com.epay.logging.annotation.Log;
import com.epay.pay.service.BillTagService;
import com.epay.pay.service.dto.EpayTagCreate;
import com.epay.pay.service.dto.EpayTagCriteria;
import com.epay.pay.service.dto.EpayTagUpdate;
import com.epay.utils.RestResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping("/bill/tag")
public class BillTagController {

    private final BillTagService billTagService;

    /**
     * 获取标签列表
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log
    @GetMapping("")
    public ResponseEntity<RestResult> page(EpayTagCriteria criteria, Pageable pageable) {
        if (pageable.getSort().isUnsorted()) {
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
        }
        return new ResponseEntity<>(billTagService.page(criteria, pageable), HttpStatus.OK);
    }

    /**
     * 创建标签列表
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping("")
    public ResponseEntity<RestResult> create(@RequestBody @Valid EpayTagCreate create) {
        return new ResponseEntity<>(billTagService.create(create), HttpStatus.OK);
    }

    /**
     * 更新标签列表
     *
     * @param update
     * @return
     */
    @Log
    @PatchMapping("/{id}")
    public ResponseEntity<RestResult> update(@RequestBody @Valid EpayTagUpdate update, @PathVariable Integer id) {
        return new ResponseEntity<>(billTagService.update(update, id), HttpStatus.OK);
    }

    /**
     * 删除标签列表
     *
     * @param id
     * @return
     */
    @Log
    @DeleteMapping("/{id}")
    public ResponseEntity<RestResult> delete(@PathVariable("id") Integer id) {
        return new ResponseEntity<>(billTagService.delete(id), HttpStatus.OK);
    }

}
