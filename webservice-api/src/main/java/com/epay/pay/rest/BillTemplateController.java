package com.epay.pay.rest;

import com.epay.logging.annotation.Log;
import com.epay.pay.service.BillTemplateService;
import com.epay.pay.service.dto.*;
import com.epay.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/bill/template")
public class BillTemplateController {

    private final BillTemplateService billTemplateService;

    /**
     * 获取账单分页
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A300020015')")
    @GetMapping("")
    public ResponseEntity<RestResult> page(EpayTemplateCriteria criteria, Pageable pageable) {
        try {
            if (pageable.getSort().isUnsorted()) {
                pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
            }
            String[] queryArr = new String[]{"templateName"};
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                for (String query : queryArr) {
                    EpayTemplateCriteria keyCriteria = ReflectionUtil.setPropertyValue(EpayTemplateCriteria.class, query, criteria.getSearchKeyword());
                    PageResult<EpayTemplateDto> result = billTemplateService.page(keyCriteria, pageable);
                    if (result.getTotal() > 0) {
                        return new ResponseEntity<>(RestUtil.ok(result), HttpStatus.OK);
                    }
                }
                return new ResponseEntity<>(RestUtil.ok(PageUtil.noData()), HttpStatus.OK);
            }
            return new ResponseEntity<>(RestUtil.ok(billTemplateService.page(criteria, pageable)), HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建账单模板
     *
     * @param create
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A300020015')")
    @PostMapping("")
    public ResponseEntity<RestResult> create(@RequestBody @Validated EpayTemplateCreate create) {
        return new ResponseEntity<>(billTemplateService.create(create), HttpStatus.OK);
    }

    /**
     * 删除账单模板
     *
     * @param id
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A300020015')")
    @DeleteMapping("/{id}")
    public ResponseEntity<RestResult> delete(@PathVariable("id") Integer id) {
        return new ResponseEntity<>(billTemplateService.delete(id), HttpStatus.OK);
    }

    /**
     * 更新账单模板
     *
     * @param update
     * @param id
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A300020015')")
    @PatchMapping("/{id}")
    public ResponseEntity<RestResult> update(@RequestBody @Validated EpayTemplateUpdate update, @PathVariable("id") Integer id) {
        return new ResponseEntity<>(billTemplateService.update(update, id), HttpStatus.OK);
    }

}
