package com.epay.pay.rest;

import com.epay.annotation.AnonymousAccess;
import com.epay.exception.CustomException;
import com.epay.logging.annotation.Log;
import com.epay.pay.service.BillPayOrderService;
import com.epay.pay.service.dto.*;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@RequiredArgsConstructor
@RequestMapping("/bill")
public class BillPayOrderController {

    private static final Logger log = LoggerFactory.getLogger(BillPayOrderController.class);
    @Resource
    private BillPayOrderService billPayOrderService;

    /**
     * 建立一笔支付订单数据
     * 字段说明：
     * - amount: 商品金额
     * - commission: 手续费
     * - totalAmount: 总金额（必须等于商品金额+手续费）
     *
     * @param create 创建订单参数
     * @return
     */
    @Log(isSaveResponseData = false)
    @PreAuthorize("@el.check('A300020014')")
    @PostMapping("/order")
    public ResponseEntity<RestResult> create(@RequestBody @Valid EpayBillCreate create) {
        return new ResponseEntity<>(billPayOrderService.create(create), HttpStatus.OK);
    }

    /**
     * 查询账单数据
     *
     * @param criteria
     * @return
     */
    @Log(isSaveResponseData = false)
    @PreAuthorize("@el.check('A300020014')")
    @GetMapping("/order")
    public ResponseEntity<RestResult> page(EpayBillCriteria criteria, Pageable pageable) {
        try {
            if (pageable.getSort().isUnsorted()) {
                pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
            }
            criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
            String[] queryArr = new String[]{"userId", "orderNum", "orderName", "merCode"};
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                for (String query : queryArr) {
                    EpayBillCriteria keyCriteria = ReflectionUtil.setPropertyValue(EpayBillCriteria.class, query, criteria.getSearchKeyword());
                    keyCriteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
                    PageResult<EpayBillDto> result = billPayOrderService.page(keyCriteria, pageable);
                    if (result.getTotal() > 0) {
                        return new ResponseEntity<>(RestUtil.ok(result), HttpStatus.OK);
                    }
                }
                return new ResponseEntity<>(RestUtil.ok(PageUtil.noData()), HttpStatus.OK);
            }
            return new ResponseEntity<>(RestUtil.ok(billPayOrderService.page(criteria, pageable)), HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 支付账单数据的更改
     * 字段说明：
     * - commission: 手续费（若更新，totalAmount将自动调整）
     * - totalAmount: 总金额（必须等于商品金额+手续费）
     *
     * @param update 更新参数
     * @param id     账单ID
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A300020014')")
    @PatchMapping("/order/{id}")
    public ResponseEntity<RestResult> update(@RequestBody EpayBillUpdate update, @PathVariable("id") Integer id) {
        // 当valid=0（删除操作）时，检查是否存在交易数据
        if (update.getValid() != null && update.getValid() == 0) {
            // 检查该账单是否存在交易数据
            if (billPayOrderService.existsTransaction(id)) {
                return new ResponseEntity<>(RestUtil.toRest(RestEnum.BILL_HAS_TRANSACTION_CANNOT_DELETE), HttpStatus.OK);
            }
        }
        
        return new ResponseEntity<>(billPayOrderService.update(update, id), HttpStatus.OK);
    }

    /**
     * 获取支付链接
     *
     * @param id
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A400020014')")
    @GetMapping("/payByLink/{id}")
    public ResponseEntity<RestResult> payByLink(@PathVariable("id") Integer id) {
        return new ResponseEntity<>(billPayOrderService.payByLink(id, null), HttpStatus.OK);
    }

    /**
     * 获取二维码链接
     *
     * @param id
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A400020014')")
    @GetMapping("/payByLinkQrCode/{id}")
    public ResponseEntity<RestResult> payByLinkQrCode(@PathVariable("id") Integer id) {
        return new ResponseEntity<>(billPayOrderService.payByLinkQrCode(id), HttpStatus.OK);
    }

    /**
     * 账单支付的退款
     *
     * @param refund
     * @return
     */
    @Log
    @PreAuthorize("@el.check('A300020013')")
    @PostMapping("/refund")
    public ResponseEntity<RestResult> refund(@RequestBody EpayBillRefund refund) {
        return new ResponseEntity<>(billPayOrderService.refund(refund), HttpStatus.OK);
    }

    /**
     * 初始化交易数据
     *
     * @param login
     * @return
     */
    @Log
    @AnonymousAccess
    @PostMapping("/login")
    public ResponseEntity<RestResult> login(@RequestBody EpayBillLogin login) throws Exception {
        return new ResponseEntity<>(billPayOrderService.login(login), HttpStatus.OK);
    }

    /**
     * 提交一笔支付交易
     *
     * @param payment
     * @return
     */
    @Log
    @PostMapping("/payment")
    public ResponseEntity<RestResult> payment(@RequestBody @Valid EpayBillPayment payment) throws Exception {
        return new ResponseEntity<>(billPayOrderService.payment(payment), HttpStatus.OK);
    }

    /**
     * 获取支付链接
     *
     * @param id
     * @return
     */
    @Log
    @GetMapping("/payByLinkTemporary/{id}")
    public ResponseEntity<RestResult> payByLinkTemporary(@Valid EpayTransactionUpdateCustomInfo customInfo, @PathVariable("id") Integer id) {
        return new ResponseEntity<>(billPayOrderService.payByLinkTemporary(customInfo, id), HttpStatus.OK);
    }

    /**
     * 获取支付结束数据
     *
     * @param login
     * @return
     */
    @Log
    @AnonymousAccess
    @PostMapping("/paymentEnd")
    public ResponseEntity<RestResult> paymentEnd(@RequestBody EpayBillLogin login) throws Exception {
        return new ResponseEntity<>(billPayOrderService.paymentEnd(login), HttpStatus.OK);
    }

    /**
     * 支付回调接口
     *
     * @param callback 回调参数
     * @return 直接返回带有跳转脚本的HTML页面
     */
    @Log
    @AnonymousAccess
    @PostMapping(value = "/paymentCallback")
    public void paymentCallback(@ModelAttribute EpayPaymentCallback callback, HttpServletResponse response) {
        try {
            String redirectUrl = billPayOrderService.handlePaymentCallback(callback);
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            log.error("paymentCallback error {}", e.getMessage());
            throw new CustomException(500, e.getMessage());
        }
    }
}
