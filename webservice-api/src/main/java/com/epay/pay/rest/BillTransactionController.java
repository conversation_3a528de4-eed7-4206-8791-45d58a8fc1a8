package com.epay.pay.rest;

import com.epay.logging.annotation.Log;
import com.epay.pay.service.BillTransactionService;
import com.epay.pay.service.dto.EpayTransactionCriteria;
import com.epay.pay.service.dto.EpayTransactionDto;
import com.epay.pay.service.dto.EpayTransactionTagUpdate;
import com.epay.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping("/bill")
public class BillTransactionController {

    private final BillTransactionService billTransactionService;

    /**
     * 查询交易数据
     *
     * @param criteria
     * @return
     */
    @Log(isSaveResponseData = false)
    @PreAuthorize("@el.check('A300020011')")
    @GetMapping("/transaction")
    public ResponseEntity<RestResult> page(EpayTransactionCriteria criteria, Pageable pageable) {
        try {
            if (pageable.getSort().isUnsorted()) {
                pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
            }
            criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
            String[] queryArr = new String[]{"insCode", "merCode"};
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                for (String query : queryArr) {
                    EpayTransactionCriteria keyCriteria = ReflectionUtil.setPropertyValue(EpayTransactionCriteria.class, query, criteria.getSearchKeyword());
                    keyCriteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
                    PageResult<EpayTransactionDto> result = billTransactionService.page(keyCriteria, pageable);
                    if (result.getTotal() > 0) {
                        return new ResponseEntity<>(RestUtil.ok(result), HttpStatus.OK);
                    }
                }
                return new ResponseEntity<>(RestUtil.ok(PageUtil.noData()), HttpStatus.OK);
            }
            return new ResponseEntity<>(RestUtil.ok(billTransactionService.page(criteria, pageable)), HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新交易单的标签
     *
     * @param update
     * @return
     */
    @Log(isSaveResponseData = false)
    @PutMapping("/transaction/tag")
    public ResponseEntity<RestResult> updateTag(@RequestBody @Valid EpayTransactionTagUpdate update) {
        return new ResponseEntity<>(billTransactionService.updateTag(update), HttpStatus.OK);
    }

}
