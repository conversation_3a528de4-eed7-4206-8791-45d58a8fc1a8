package com.epay.pay.rest;

import com.epay.annotation.AnonymousAccess;
import com.epay.logging.annotation.Log;
import com.epay.management.domain.Merchant;
import com.epay.management.enums.IsUpopMerchantEnum;
import com.epay.management.service.MerchantService;
import com.epay.pay.domain.EpayBill;
import com.epay.pay.domain.EpayBillCallbackReq;
import com.epay.pay.domain.EpayBillCallbackRes;
import com.epay.pay.domain.EpayTransaction;
import com.epay.pay.repository.EpayBillRepository;
import com.epay.pay.repository.EpayTransactionRepository;
import com.epay.pay.utils.CallbackSignUtil;
import com.epay.pay.utils.PayUtil;
import com.epay.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/callback")
public class CallbackController {

    private final EpayBillRepository epayBillRepository;
    private final EpayTransactionRepository epayTransactionRepository;
    private final MerchantService merchantService;

    /**
     * 支付成功处理
     *
     * @return
     */
    @Log
    @PostMapping("/pay")
    @AnonymousAccess
    @Transactional
    public ResponseEntity<EpayBillCallbackRes> pay(@RequestBody Map<String, Object> map) {
        log.info("CallbackController pay map:{}", map);
        // 校验签名
        EpayBillCallbackRes billCallbackRes = new EpayBillCallbackRes();
        Optional<EpayTransaction> transactionOptional = epayTransactionRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("outTradeNo"), map.get("out_trade_no").toString()));
        if (!transactionOptional.isPresent()) {
            log.error("未找到对应的交易记录：{}", map.get("out_trade_no"));
            billCallbackRes.setResult_code("FAIL");
            billCallbackRes.setReturn_msg("交易记录不存在");
            return new ResponseEntity<>(billCallbackRes, HttpStatus.OK);
        }

        EpayTransaction transaction = transactionOptional.get();
        // 获取商户信息及密钥
        Merchant merchant = merchantService.merchantInfo(map.get("sub_mch_id").toString());
        if (merchant == null) {
            log.error("未找到商户信息：{}", map.get("sub_mch_id").toString());
            billCallbackRes.setResult_code("FAIL");
            billCallbackRes.setReturn_msg("商户不存在");
            return new ResponseEntity<>(billCallbackRes, HttpStatus.OK);
        }

        // 进行签名验证
        if (!CallbackSignUtil.verifyCallbackSign(map, PayUtil.getKey(merchant.getMerKey()))) {
            log.error("签名验证失败");
            billCallbackRes.setResult_code("FAIL");
            billCallbackRes.setReturn_msg("签名验证失败");
            return new ResponseEntity<>(billCallbackRes, HttpStatus.OK);
        }

        // 更新订单状态
        transaction.setStatus(1);
        transaction.setTransactionId(map.get("transaction_id").toString());
        epayTransactionRepository.save(transaction);
        Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNum"), transaction.getOrderNum()));
        if (billOptional.isPresent()) {
            EpayBill bill = billOptional.get();
            bill.setStatus(2);
            epayBillRepository.save(bill);
        }
        billCallbackRes.setResult_code("SUCCESS");
        return new ResponseEntity<>(billCallbackRes, HttpStatus.OK);
    }


//    public static void main(String[] args) {
//        Map<String, Object> map = new HashMap<>();
//        map.put("transaction_id", "4200002735202507030654633720");
//        map.put("notify_type", "trade_status_sync");
//        map.put("gmt_payment", "20250703195654");
//        map.put("device_info", "30000177");
//        map.put("out_trade_no", "ET2507031954331764");
//        map.put("total_fee", "10");
//        map.put("sign", "230efdc2629222f691c1df868c81c7d1");
//        map.put("result_code", "SUCCESS");
//        map.put("currency", "THB");
//        map.put("mch_id", "1480695432");
//        map.put("sub_mch_id", "760978116");
//        map.put("return_code", "SUCCESS");
//        if (CallbackSignUtil.verifyCallbackSign(map, PayUtil.getKey(null))) {
//            log.error("签名验证成功");
//        }
//    }

}
