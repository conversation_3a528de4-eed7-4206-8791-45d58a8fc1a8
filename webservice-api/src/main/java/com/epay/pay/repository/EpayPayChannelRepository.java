package com.epay.pay.repository;

import com.epay.pay.domain.EpayPayChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface EpayPayChannelRepository extends JpaRepository<EpayPayChannel, Integer>, JpaSpecificationExecutor<EpayPayChannel> {

    List<EpayPayChannel> findByOrderNum(String orderNum);

}
