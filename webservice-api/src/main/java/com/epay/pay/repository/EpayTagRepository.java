package com.epay.pay.repository;

import com.epay.pay.domain.EpayTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface EpayTagRepository extends JpaRepository<EpayTag, Integer>, JpaSpecificationExecutor<EpayTag> {

    /**
     * 根据tagName和userId查询标签
     * @param tagName 标签名称
     * @param userId 用户ID
     * @return 标签实体
     */
    Optional<EpayTag> findByTagNameAndUserId(String tagName, String userId);

    /**
     * 根据tagName、userId和orgId查询标签
     * @param tagName 标签名称
     * @param userId 用户ID
     * @param orgId 组织ID
     * @return 标签实体
     */
    Optional<EpayTag> findByTagNameAndUserIdAndOrgId(String tagName, String userId, String orgId);

}
