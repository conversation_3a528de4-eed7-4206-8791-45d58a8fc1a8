package com.epay.pay.utils;

import com.epay.utils.StringUtils;

public class PayUtil {

    public static String getKey(String key) {
        String merKey = "DpHk!@#4";
        if (StringUtils.isNotEmpty(key)) {
            merKey = key;
        }
        return merKey;
    }

    public static String getDeviceInfo(String userId, String mode) {
        String modeStr = "billpay".equals(mode) ? "3" : "9";
        // 截取后7位（若不足则取全部）
        int startIndex = Math.max(0, userId.length() - 7);
        String last7 = userId.substring(startIndex);
        // 补零到7位数字
        String padded = String.format("%7s", last7).replace(' ', '0');
        // 组合为 modeStr + 7位数字，总长度8
        return modeStr + padded;
    }

}
