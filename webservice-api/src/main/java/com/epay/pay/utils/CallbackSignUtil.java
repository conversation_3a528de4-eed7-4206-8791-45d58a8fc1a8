package com.epay.pay.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epay.pay.domain.EpayBillCallbackReq;
import com.epay.pay.service.dto.EpayPaymentCallback;
import com.epay.utils.Md5Utils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.TreeMap;

@Slf4j
public class CallbackSignUtil {

    /**
     * 验证支付回调的签名
     * @param callback 回调参数
     * @param merKey 商户密钥
     * @return 签名是否有效
     */
    public static boolean verifyCallbackSign(EpayPaymentCallback callback, String merKey) {
        try {
            // 获取回调中的签名
            String receivedSign = callback.getSign();
            if (receivedSign == null || receivedSign.isEmpty()) {
                log.error("回调签名为空");
                return false;
            }

            // 将对象转为JSON对象
            JSONObject jsonObj = JSONObject.parseObject(JSON.toJSONString(callback));

            // 移除不参与签名的字段
            jsonObj.remove("sign");
            jsonObj.remove("sign_type");

            // 转为TreeMap确保按键名排序
            TreeMap<String, Object> sortedMap = new TreeMap<>();
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                // 所有属性都添加到签名计算中，null值转为空字符串
                sortedMap.put(key, value == null ? "" : value);
            }

            // 记录用于签名的参数
            log.debug("签名参数：{}", JSON.toJSONString(sortedMap));

            // 生成签名
            String calculatedSign = Md5Utils.md5Sign(sortedMap, merKey);

            // 比较签名
            boolean isValid = receivedSign.equalsIgnoreCase(calculatedSign);
            if (!isValid) {
                log.error("签名验证失败，接收到的签名：{}，计算的签名：{}", receivedSign, calculatedSign);
            }
            return isValid;
        } catch (Exception e) {
            log.error("验证签名异常", e);
            return false;
        }
    }
    /**
     * 验证支付回调的签名
     * @param callback 回调参数
     * @param merKey 商户密钥
     * @return 签名是否有效
     */
    public static boolean verifyCallbackSign(Map<String,Object> callback, String merKey) {
        try {
            // 获取回调中的签名
            String receivedSign = (String) callback.get("sign");
            if (receivedSign == null || receivedSign.isEmpty()) {
                log.error("回调签名为空");
                return false;
            }

            // 将对象转为JSON对象
            JSONObject jsonObj = JSONObject.parseObject(JSON.toJSONString(callback));

            // 移除不参与签名的字段
            jsonObj.remove("sign");
            jsonObj.remove("sign_type");

            // 转为TreeMap确保按键名排序
            TreeMap<String, Object> sortedMap = new TreeMap<>();
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                // 所有属性都添加到签名计算中，null值转为空字符串
                sortedMap.put(key, value == null ? "" : value);
            }

            // 记录用于签名的参数
            log.debug("签名参数：{}", JSON.toJSONString(sortedMap));

            // 生成签名
            String calculatedSign = Md5Utils.md5Sign(sortedMap, merKey);

            // 比较签名
            boolean isValid = receivedSign.equalsIgnoreCase(calculatedSign);
            if (!isValid) {
                log.error("签名验证失败，接收到的签名：{}，计算的签名：{}", receivedSign, calculatedSign);
            }
            return isValid;
        } catch (Exception e) {
            log.error("验证签名异常", e);
            return false;
        }
    }

    /**
     * 验证支付回调的签名
     * @param req 回调请求
     * @param merKey 商户密钥
     * @return 签名是否有效
     */
    public static boolean verifyCallbackSign(EpayBillCallbackReq req, String merKey) {
        try {
            // 获取回调中的签名
            String receivedSign = req.getSign();
            if (receivedSign == null || receivedSign.isEmpty()) {
                log.error("回调签名为空");
                return false;
            }

            // 将对象转为JSON对象
            JSONObject jsonObj = JSONObject.parseObject(JSON.toJSONString(req));

            // 移除不参与签名的字段
            jsonObj.remove("sign");

            // 转为TreeMap确保按键名排序
            TreeMap<String, Object> sortedMap = new TreeMap<>();
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                // 所有属性都添加到签名计算中，null值转为空字符串
                sortedMap.put(key, value == null ? "" : value);
            }

            // 记录用于签名的参数
            log.debug("签名参数：{}", JSON.toJSONString(sortedMap));

            // 使用密钥生成签名
            String key = PayUtil.getKey(merKey);
            String calculatedSign = Md5Utils.md5Sign(sortedMap, key);

            // 比较签名
            boolean isValid = receivedSign.equalsIgnoreCase(calculatedSign);
            if (!isValid) {
                log.error("签名验证失败，接收到的签名：{}，计算的签名：{}", receivedSign, calculatedSign);
            }
            return isValid;
        } catch (Exception e) {
            log.error("验证签名异常", e);
            return false;
        }
    }
}
