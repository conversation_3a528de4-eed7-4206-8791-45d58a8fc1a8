package com.epay.pay.service.dto;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class EpayTransactionDto extends BaseEntity {

    private Integer id;
    private String orderNum;
    private String orgId;
    private String userId;
    private String outTradeNo;
    private String transactionId;
    private String insCode;
    private String merCode;
    private String billType;
    private String paymentType;
    private String itemName;
    private Integer amount;
    private Integer commission;
    private Integer totalAmount;
    private String currency;
    private String cardNum;
    private Integer status;
    private String email;
    private String username;
    private String phoneNumber;
    private String address;
    private String traceType;
    private String customerName;
    private String customerPhone;
    private String customerAddress;
    private String customerEmail;
    private List<EpayTagDto> tagList;


}
