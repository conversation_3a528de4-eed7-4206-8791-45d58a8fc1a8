package com.epay.pay.service.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class EpayTransactionUpdateCustomInfo {

    @NotEmpty(message = "merCode is not empty")
    private String merCode; // 指定要使用的支付渠道商户编码

    @NotEmpty(message = "customerName is not empty")
    private String customerName;

    @NotEmpty(message = "customerPhone is not empty")
    private String customerPhone;

    @NotEmpty(message = "customerAddress is not empty")
    private String customerAddress;

    @NotEmpty(message = "customerEmail is not empty")
    private String customerEmail;

}
