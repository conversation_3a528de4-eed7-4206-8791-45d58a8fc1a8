package com.epay.pay.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
public class EpayTransactionCriteria {

    @Query(type = Query.Type.INNER_LIKE)
    private String userId;

    @Query(type = Query.Type.EQUAL)
    private String orgId;

    @Query(type = Query.Type.INNER_LIKE)
    private String orderNum;

    @Query(type = Query.Type.INNER_LIKE)
    private String orderName;

    @Query(type = Query.Type.INNER_LIKE)
    private String outTradeNo;

    @Query(type = Query.Type.INNER_LIKE)
    private String insCode;

    @Query(type = Query.Type.INNER_LIKE)
    private String merCode;

    @Query(type = Query.Type.EQUAL)
    private String billType;

    @Query(type = Query.Type.EQUAL)
    private String paymentType;

    @Query(type = Query.Type.EQUAL)
    private Integer status;

    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    private Timestamp startTime;

    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    private Timestamp endTime;

    @Query(type = Query.Type.INNER_LIKE)
    private String cardNum;

    private BigDecimal minAmount;

    private BigDecimal maxAmount;

    private String tagName;

    @Query(type = Query.Type.IN, propName = "outTradeNo")
    private List<String> outTradeNoList;

    private String searchKeyword;


}
