package com.epay.pay.service.dto;

import com.epay.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EpayBillDto extends BaseDto {

    private Integer id;
    private String orderNum;
    private String orderName;
    private String merCode;
    private String billType;
    private String paymentType;
    private String itemName;
    private Integer amount;
    private Integer commission;
    private Integer totalAmount;
    private String currency;
    private String imageUrl;
    private String paymentEmail;
    private String emailTitle;
    private String emailMessage;
    private Integer isFavourite;
    private Integer status;
    private String userId;
    private Long expiryTimestamp;
    private Integer userStatus;
    private Integer isLock;
    private String mode;

}
