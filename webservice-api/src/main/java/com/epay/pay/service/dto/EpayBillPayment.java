package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class EpayBillPayment {

    @NotEmpty(message = "merCode not empty")
    private String merCode; // 指定要使用的支付渠道商户编码

    @NotEmpty(message = "paymentType not empty")
    private String paymentType;

    @NotEmpty(message = "amount not empty")
    private String amount;

    @NotEmpty(message = "currency not empty")
    private String currency;

    @NotEmpty(message = "orderNum not empty")
    private String orderNum;

    private String frontEndPageUrl;

    private String frontFailPageUrl;

    private String password;

    @NotEmpty(message = "customerName is not empty")
    private String customerName;

    @NotEmpty(message = "customerPhone is not empty")
    private String customerPhone;

    @NotEmpty(message = "customerAddress is not empty")
    private String customerAddress;

    @NotEmpty(message = "customerEmail is not empty")
    private String customerEmail;


}
