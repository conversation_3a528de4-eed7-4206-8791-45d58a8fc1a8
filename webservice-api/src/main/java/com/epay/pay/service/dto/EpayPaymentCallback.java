package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 支付回调参数
 */
@Getter
@Setter
public class EpayPaymentCallback {
    private String charset;
    private String currency;
    private String out_trade_no;
    private String pay_card_type;
    private String resp_msg;
    private String result_code;
    private String settle_amount;
    private String settle_currency;
    private String settle_date;
    private String sign;
    private String sub_mch_id;
    private String total_fee;
    private String trace_number;
    private String trace_time;
    private String trans_type;
    private String transaction_id;
    private String version;
} 