package com.epay.pay.service.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class EpayBillCreate {

    @NotEmpty(message = "orderName not empty")
    private String orderName;

    @NotEmpty(message = "merCode not empty")
    private String merCode; // 支持多个商户编码，使用逗号(,)分隔

    @NotEmpty(message = "billType not empty")
    private String billType;

    @NotEmpty(message = "itemName not empty")
    private String itemName;

    @NotNull(message = "amount not empty")
    private Integer amount;

    private Integer commission = 0;

    @NotEmpty(message = "currency not empty")
    private String currency;

    @NotEmpty(message = "mode not empty")
    private String mode;

    private String imageUrl;

    private Integer isDraft;

    private String notifyUrl;

    private String paymentEmail;

    private String emailTitle;

    private String emailMessage;


    private Long expiryTimestamp;

}
