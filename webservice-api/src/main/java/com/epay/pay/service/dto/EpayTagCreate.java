package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class EpayTagCreate {

    @NotEmpty(message = "tagName not empty")
    private String tagName;

    @NotEmpty(message = "display not empty")
    private String display;

    @NotEmpty(message = "color not empty")
    private String color;

    @NotEmpty(message = "userId not empty")
    private String userId;

    @NotEmpty(message = "orgId not empty")
    private String orgId;

}
