package com.epay.pay.service.mapstruct;

import com.epay.base.BaseMapper;
import com.epay.pay.domain.EpayTag;
import com.epay.pay.service.dto.EpayTagDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EpayTagDtoMapper extends BaseMapper<EpayTagDto, EpayTag> {


}
