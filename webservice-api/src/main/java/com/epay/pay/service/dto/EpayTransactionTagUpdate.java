package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class EpayTransactionTagUpdate {

    @NotEmpty(message = "outTradeNo not empty")
    private String outTradeNo;

    @NotEmpty(message = "tagId not empty")
    private String tagId;

    @NotEmpty(message = "action not empty")
    private String action;
}
