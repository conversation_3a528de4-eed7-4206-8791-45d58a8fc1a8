package com.epay.pay.service.impl;

import com.epay.pay.domain.EpayTemplate;
import com.epay.pay.repository.EpayTemplateRepository;
import com.epay.pay.service.BillTemplateService;
import com.epay.pay.service.dto.EpayTemplateCreate;
import com.epay.pay.service.dto.EpayTemplateCriteria;
import com.epay.pay.service.dto.EpayTemplateDto;
import com.epay.pay.service.dto.EpayTemplateUpdate;
import com.epay.pay.service.mapstruct.EpayTemplateCreateMapper;
import com.epay.pay.service.mapstruct.EpayTemplateDtoMapper;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class BillTemplateServiceImpl implements BillTemplateService {

    private final EpayTemplateDtoMapper templateDtoMapper;
    private final EpayTemplateCreateMapper templateCreateMapper;
    private final EpayTemplateRepository templateRepository;

    @Override
    public PageResult<EpayTemplateDto> page(EpayTemplateCriteria criteria, Pageable pageable) {
        Page<EpayTemplate> page = templateRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return new PageResult<>(templateDtoMapper.toDto(page.getContent()), page.getTotalElements());
    }

    /**
     * 创建
     * @param create
     * @return
     */
    @Override
    public RestResult create(EpayTemplateCreate create) {
        List<EpayTemplate> templateRes = templateRepository.findAll((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("templateName"), create.getTemplateName()));
        if (!templateRes.isEmpty()) {
            return RestUtil.toRest(RestEnum.TEMPLATE_NAME_ALREADY_EXIST);
        }
        EpayTemplate templateCreate = templateCreateMapper.toEntity(create);
        templateCreate.setTemplateId(IdGenerator.generateUniqueCode());
        templateCreate.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
        templateCreate.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        templateRepository.save(templateCreate);
        return RestUtil.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Override
    public RestResult delete(Integer id) {
        templateRepository.deleteById(id);
        return RestUtil.ok();
    }

    /**
     * 更新
     * @param update
     * @param id
     * @return
     */
    @Override
    public RestResult update(EpayTemplateUpdate update, Integer id) {
        Optional<EpayTemplate> templateOptional = templateRepository.findById(id);
        if (!templateOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.TEMPLATE_NOT_EXIST);
        }
        EpayTemplate template = templateOptional.get();
        // 如果模板名称发生变化，需要检查是否重复
        if (!template.getTemplateName().equals(update.getTemplateName())) {
            List<EpayTemplate> templateRes = templateRepository.findAll((root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("templateName"), update.getTemplateName())
            );
            if (!templateRes.isEmpty()) {
                return RestUtil.toRest(RestEnum.TEMPLATE_NAME_ALREADY_EXIST);
            }
        }

        // 更新模板属性
        template.setTemplateName(update.getTemplateName());
        template.setTemplateData(update.getTemplateData());

        if (update.getBillType() != null) {
            template.setBillType(update.getBillType());
        }

        if (update.getInfo() != null) {
            template.setInfo(update.getInfo());
        }

        templateRepository.save(template);
        return RestUtil.ok();
    }
}
