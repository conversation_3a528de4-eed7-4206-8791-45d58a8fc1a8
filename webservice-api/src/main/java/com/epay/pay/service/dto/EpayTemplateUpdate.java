package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class EpayTemplateUpdate {

    private Integer id;

    @NotEmpty(message = "templateName not empty")
    private String templateName;

    @NotEmpty(message = "templateData not empty")
    private String templateData;

    private String billType;

    private String info;
}
