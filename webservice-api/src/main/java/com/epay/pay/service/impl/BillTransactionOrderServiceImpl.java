package com.epay.pay.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.CustomException;
import com.epay.pay.domain.EpayTag;
import com.epay.pay.domain.EpayTransaction;
import com.epay.pay.enums.TagActionEnum;
import com.epay.pay.repository.EpayTagRepository;
import com.epay.pay.repository.EpayTransactionRepository;
import com.epay.pay.service.BillTransactionService;
import com.epay.pay.service.dto.EpayTagDto;
import com.epay.pay.service.dto.EpayTransactionCriteria;
import com.epay.pay.service.dto.EpayTransactionDto;
import com.epay.pay.service.dto.EpayTransactionTagUpdate;
import com.epay.pay.service.mapstruct.EpayTagDtoMapper;
import com.epay.pay.service.mapstruct.EpayTransactionDtoMapper;
import com.epay.user.domain.UcUser;
import com.epay.user.service.dto.UcOrganizationCriteria;
import com.epay.user.service.dto.UcOrganizationDto;
import com.epay.user.service.dto.UcUserCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class BillTransactionOrderServiceImpl implements BillTransactionService {

    private final EpayTransactionRepository epayTransactionRepository;
    private final EpayTagRepository tagRepository;
    private final EpayTagDtoMapper epayTagDtoMapper;
    private final EpayTransactionDtoMapper epayTransactionDtoMapper;
    private final DataServerProperties dataServerProperties;
    private final EpayTagRepository epayTagRepository;


    /**
     * 查询交易数据
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<EpayTransactionDto> page(EpayTransactionCriteria criteria, Pageable pageable) {
        if (StringUtils.isNotEmpty(criteria.getTagName())) {
            criteria.setOutTradeNoList(tagRepository.findAll((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.like(root.get("tagName"), criteria.getTagName())).stream().map(EpayTag::getOutTradeNo).collect(Collectors.toList()));
        }
        Page<EpayTransaction> page = epayTransactionRepository.findAll((root, criteriaQuery, criteriaBuilder) ->
        {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);

            // 处理最小金额查询条件
            if (criteria.getMinAmount() != null) {
                // 将元转换为分（乘以100）
                Integer minAmountInCents = criteria.getMinAmount().multiply(new BigDecimal(100)).intValue();
                Predicate minAmountPredicate = criteriaBuilder.greaterThanOrEqualTo(root.get("amount"), minAmountInCents);
                predicate = criteriaBuilder.and(predicate, minAmountPredicate);
            }

            // 处理最大金额查询条件
            if (criteria.getMaxAmount() != null) {
                // 将元转换为分（乘以100）
                Integer maxAmountInCents = criteria.getMaxAmount().multiply(new BigDecimal(100)).intValue();
                Predicate maxAmountPredicate = criteriaBuilder.lessThanOrEqualTo(root.get("amount"), maxAmountInCents);
                predicate = criteriaBuilder.and(predicate, maxAmountPredicate);
            }

            return predicate;
        }, pageable);
        List<EpayTransactionDto> transactionList = epayTransactionDtoMapper.toDto(page.getContent());
        if (transactionList.isEmpty()) {
            return PageUtil.noData();
        }
        List<String> userIds = transactionList.stream().map(EpayTransactionDto::getUserId).collect(Collectors.toList());
        RestResult userList = CallUtil.query(dataServerProperties.getUserUrl() + "/user", new UcUserCriteria(userIds), PageRequest.of(0, Integer.MAX_VALUE));
        if (userList.getCode() != 0) {
            throw new CustomException(RestEnum.USER_NOT_EXIST.getCode(), RestEnum.USER_NOT_EXIST.getMessage());
        }
        List<UcUser> users = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(userList.getData())).getJSONArray("data")), UcUser.class);
        if (users.isEmpty()) {
            throw new CustomException(RestEnum.USER_NOT_EXIST.getCode(), RestEnum.USER_NOT_EXIST.getMessage());
        }
        List<EpayTag> epayTagList = epayTagRepository.findAll((root, criteriaQuery, criteriaBuilder) -> root.get("outTradeNo").in(transactionList.stream().map(EpayTransactionDto::getOutTradeNo).collect(Collectors.toList())));
        Map<String, List<EpayTagDto>> epayTagMap = epayTagList.stream().collect(Collectors.toMap(
                EpayTag::getOutTradeNo,
                t -> {
                    List<EpayTagDto> list = new ArrayList<>();
                    list.add(epayTagDtoMapper.toDto(t));
                    return list;
                },
                (list1, list2) -> {
                    // 合并两个列表
                    list1.addAll(list2);
                    return list1;
                }
        ));
        Map<String, UcUser> userMap = users.stream().collect(Collectors.toMap(UcUser::getUserId, t -> t));
        List<String> orgIds = users.stream().map(UcUser::getOrgId).collect(Collectors.toList());
        // 查询机构信息
//        List<String> orgIds = transactionList.stream().map(EpayTransactionDto::getOrgId).collect(Collectors.toList());
        RestResult organization = CallUtil.query(dataServerProperties.getUserUrl() + "/organization", new UcOrganizationCriteria(orgIds), PageRequest.of(0, Integer.MAX_VALUE));
        if (organization.getCode() != 0) {
            throw new CustomException(RestEnum.ORG_NOT_EXIST.getCode(), RestEnum.ORG_NOT_EXIST.getMessage());
        }
        List<UcOrganizationDto> organizationList = JSON.parseArray(JSON.toJSONString(JSON.parseObject(JSON.toJSONString(organization.getData())).getJSONArray("data")), UcOrganizationDto.class);
        Map<String, UcOrganizationDto> orgMap = new HashMap<>();
        if (!organizationList.isEmpty()) {
            orgMap = organizationList.stream().collect(Collectors.toMap(UcOrganizationDto::getOrgId, t -> t));
        }
        for (EpayTransactionDto epayTransactionDto : transactionList) {
            UcUser user = userMap.get(epayTransactionDto.getUserId());
            epayTransactionDto.setCardNum(SensitiveUtil.maskNumber(epayTransactionDto.getCardNum()));
            epayTransactionDto.setEmail(user.getEmail());
            epayTransactionDto.setUsername(user.getUsername());
            epayTransactionDto.setAddress(orgMap.get(user.getOrgId()).getAddress());
            epayTransactionDto.setPhoneNumber(orgMap.get(user.getOrgId()).getPhoneNumber());
            epayTransactionDto.setTagList(epayTagMap.get(epayTransactionDto.getOutTradeNo()));
        }
        return new PageResult<>(transactionList, page.getTotalElements());
    }

    /**
     * 更新交易单的标签
     *
     * @param update
     * @return
     */
    @Override
    public RestResult updateTag(EpayTransactionTagUpdate update) {
        Optional<EpayTag> tagOptional = tagRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("tagId"), update.getTagId()));
        if (!tagOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.TAG_NOT_EXIST);
        }
        long transactionCount = epayTransactionRepository.count((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("outTradeNo"), update.getOutTradeNo()));
        if (transactionCount <= 0) {
            return RestUtil.toRest(RestEnum.TRANSACTION_NOT_EXIST);
        }
        EpayTag tag = tagOptional.get();
        if (StringUtils.isNotEmpty(tag.getOutTradeNo()) && update.getAction().equals(TagActionEnum.ADD.getName())) {
            return RestUtil.toRest(RestEnum.TAG_IS_USED);
        }
        if (update.getAction().equals(TagActionEnum.ADD.getName())) {
            tag.setOutTradeNo(update.getOutTradeNo());
        }
        if (update.getAction().equals(TagActionEnum.DELETE.getName())) {
            tag.setOutTradeNo(null);
        }
        tagRepository.save(tag);
        return RestUtil.ok(new JSONObject().fluentPut("tags", update.getTagId()));
    }

}
