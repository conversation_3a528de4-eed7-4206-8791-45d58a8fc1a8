package com.epay.pay.service;


import com.epay.pay.service.dto.EpayTagCreate;
import com.epay.pay.service.dto.EpayTagCriteria;
import com.epay.pay.service.dto.EpayTagUpdate;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface BillTagService {

    /**
     * 分页查询
     *
     * @param criteria
     * @param pageable
     * @return
     */
    RestResult page(EpayTagCriteria criteria, Pageable pageable);

    /**
     * 创建
     *
     * @param create
     * @return
     */
    RestResult create(EpayTagCreate create);

    /**
     * 更新
     *
     * @param update
     * @param id
     * @return
     */
    RestResult update(EpayTagUpdate update, Integer id);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    RestResult delete(Integer id);
}
