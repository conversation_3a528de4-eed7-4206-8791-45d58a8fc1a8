package com.epay.pay.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class EpayBillCriteria {

    @Query(type = Query.Type.INNER_LIKE)
    private String userId;

    @Query(type = Query.Type.INNER_LIKE)
    private String orgId;

    @Query(type = Query.Type.INNER_LIKE)
    private String orderName;

    @Query(type = Query.Type.INNER_LIKE)
    private String merCode;

    @Query(type = Query.Type.EQUAL)
    private String billType;

    @Query(type = Query.Type.EQUAL)
    private String paymentType;

    @Query(type = Query.Type.INNER_LIKE)
    private String orderNum;

    @Query(type = Query.Type.INNER_LIKE)
    private String itemName;

    @Query(type = Query.Type.IN)
    private List<String> mode;

    @Query(type = Query.Type.EQUAL)
    private Integer isFavourite;

    @Query(type = Query.Type.EQUAL)
    private Integer valid;

    @Query(type = Query.Type.IN)
    private List<Integer> status;

    private String searchKeyword;

}
