package com.epay.pay.service;


import com.epay.pay.service.dto.EpayTransactionCriteria;
import com.epay.pay.service.dto.EpayTransactionDto;
import com.epay.pay.service.dto.EpayTransactionTagUpdate;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface BillTransactionService {

    /**
     * 查询交易数据
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<EpayTransactionDto> page(EpayTransactionCriteria criteria, Pageable pageable);

    /**
     * 更新交易单的标签
     * @param update
     * @return
     */
    RestResult updateTag(EpayTransactionTagUpdate update);
}
