package com.epay.pay.service;


import com.epay.pay.service.dto.*;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface BillPayOrderService {


    /**
     * 创建
     * @param create
     * @return
     */
    RestResult create(EpayBillCreate create);


    /**
     * 查询账单数据
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<EpayBillDto> page(EpayBillCriteria criteria, Pageable pageable);

    /**
     * 支付账单数据的更改
     *
     * @param update
     * @param id
     * @return
     */
    RestResult update(EpayBillUpdate update, Integer id);

    /**
     * 退款
     * @param refund
     * @return
     */
    RestResult refund(EpayBillRefund refund);

    /**
     * 初始化交易数据
     * @param login
     * @return
     */
    RestResult login(EpayBillLogin login) throws Exception;

    /**
     * 获取支付链接
     *
     * @param id
     * @param customInfo
     * @return
     */
    RestResult payByLink(Integer id, EpayTransactionUpdateCustomInfo customInfo);

    /**
     * 获取支付链接
     * @param id
     * @return
     */
    RestResult payByLinkQrCode(Integer id);

    /**
     * 提交一笔支付交易
     * @param payment
     * @return
     */
    RestResult payment(EpayBillPayment payment);

    /**
     * 获取支付结束数据
     * @param login
     * @return
     */
    RestResult paymentEnd(EpayBillLogin login);

    /**
     * 获取支付链接
     *
     * @param customInfo
     * @param id
     * @return
     */
    RestResult payByLinkTemporary(EpayTransactionUpdateCustomInfo customInfo, Integer id);
    
    /**
     * 处理支付回调
     * @param callback 回调参数
     * @return 重定向URL
     */
    String handlePaymentCallback(EpayPaymentCallback callback);

    /**
     * 检查账单是否存在交易数据
     * @param billId 账单ID
     * @return 存在返回true，不存在返回false
     */
    boolean existsTransaction(Integer billId);
}
