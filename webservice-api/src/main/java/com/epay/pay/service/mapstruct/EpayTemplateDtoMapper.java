package com.epay.pay.service.mapstruct;

import com.epay.base.BaseMapper;
import com.epay.pay.domain.EpayTemplate;
import com.epay.pay.service.dto.EpayTemplateCreate;
import com.epay.pay.service.dto.EpayTemplateDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EpayTemplateDtoMapper extends BaseMapper<EpayTemplateDto, EpayTemplate> {


}
