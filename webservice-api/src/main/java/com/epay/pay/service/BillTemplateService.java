package com.epay.pay.service;


import com.epay.pay.service.dto.EpayBillCriteria;
import com.epay.pay.service.dto.EpayTemplateCreate;
import com.epay.pay.service.dto.EpayTemplateCriteria;
import com.epay.pay.service.dto.EpayTemplateDto;
import com.epay.pay.service.dto.EpayTemplateUpdate;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface BillTemplateService {

    /**
     * 分页查询
     *
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<EpayTemplateDto> page(EpayTemplateCriteria criteria, Pageable pageable);

    /**
     * 创建
     *
     * @param create
     * @return
     */
    RestResult create(EpayTemplateCreate create);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    RestResult delete(Integer id);

    /**
     * 更新
     *
     * @param update
     * @param id
     * @return
     */
    RestResult update(EpayTemplateUpdate update, Integer id);
}
