package com.epay.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epay.base.login.JwtUserDto;
import com.epay.config.AesProperties;
import com.epay.config.bean.DataServerProperties;
import com.epay.config.bean.PayUrlProperties;
import com.epay.config.enums.RedisDelayQueueEnum;
import com.epay.config.utils.RedisDelayQueueUtil;
import com.epay.exception.BadRequestException;
import com.epay.exception.CustomException;
import com.epay.management.domain.Merchant;
import com.epay.management.domain.MerchantInfoDetail;
import com.epay.management.domain.MerchantList;
import com.epay.management.domain.TransactionList;
import com.epay.management.enums.GatewayEnum;
import com.epay.management.enums.TransactionPaymentMethodEnum;
import com.epay.management.service.MerchantService;
import com.epay.management.service.TransactionService;
import com.epay.management.service.dto.MerchantCriteria;
import com.epay.management.service.dto.TransactionCriteria;
import com.epay.management.utils.RefundNoGenerator;
import com.epay.management.utils.TransactionUtil;
import com.epay.pay.domain.EpayBill;
import com.epay.pay.domain.EpayPayChannel;
import com.epay.pay.domain.EpayTransaction;
import com.epay.pay.enums.BillTypeEnum;
import com.epay.pay.enums.ModeEnum;
import com.epay.pay.repository.EpayBillRepository;
import com.epay.pay.repository.EpayPayChannelRepository;
import com.epay.pay.repository.EpayTransactionRepository;
import com.epay.pay.service.BillPayOrderService;
import com.epay.pay.service.dto.*;
import com.epay.pay.service.mapstruct.EpayBillCreateMapper;
import com.epay.pay.service.mapstruct.EpayBillDtoMapper;
import com.epay.pay.service.mapstruct.EpayBillLoginMapper;
import com.epay.pay.utils.PayUtil;
import com.epay.security.security.TokenProvider;
import com.epay.utils.*;
import com.epay.utils.constants.RedisConstants;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class BillPayOrderServiceImpl implements BillPayOrderService {

    private static final Logger log = LoggerFactory.getLogger(BillPayOrderServiceImpl.class);
    private final RedisUtils redisUtils;
    private final MerchantService merchantService;
    private final EpayBillRepository epayBillRepository;
    private final EpayPayChannelRepository epayPayChannelRepository;
    private final EpayTransactionRepository epayTransactionRepository;
    private final EpayBillCreateMapper epayBillCreateMapper;
    private final EpayBillLoginMapper billLoginMapper;
    private final EpayBillDtoMapper epayBillDtoMapper;
    private final TransactionService transactionService;
    private final RefundNoGenerator refundNoGenerator;
    private final EmailSender emailSender;
    private final PasswordEncoder passwordEncoder;
    private final DataServerProperties dataServerProperties;
    private final RedisDelayQueueUtil redisDelayQueueUtil;
    private final TokenProvider tokenProvider;
    private final PayUrlProperties payUrlProperties;


    /**
     * 建立一笔支付账单数据
     *
     * @param create
     * @return
     */
    @Override
    @Transactional
    public RestResult create(EpayBillCreate create) {
        // 权限校验
        JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
        if (!currentUser.getPermissions().contains("A300020014")) {
            throw new CustomException(HttpStatus.FORBIDDEN.value(), "not operation permission！");
        }

        // 解析并验证所有商户编码
        String[] merCodes = create.getMerCode().split(",");
        for (String merCode : merCodes) {
            if (!currentUser.getAuthMap().containsKey(merCode) || !currentUser.getAuthMap().get(merCode).contains("A300020014")) {
                throw new CustomException(HttpStatus.FORBIDDEN.value(), "not merchant permission for: " + merCode);
            }
        }

        // 校验邮箱格式
        if (create.getPaymentEmail() != null && !create.getPaymentEmail().isEmpty()) {
            if (!EmailValidator.isValid(create.getPaymentEmail())) {
                return RestUtil.toRest(RestEnum.EMAIL_FORMAT_ERROR);
            }
        }

        // 校验有效期
        if (create.getExpiryTimestamp() != null) {
            // 获取当天零点的时间戳
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            long todayStartTimestamp = calendar.getTimeInMillis();

            if (create.getExpiryTimestamp() < todayStartTimestamp) {
                return RestUtil.toRest(RestEnum.EXPIRY_TIMESTAMP_ILLEGAL);
            }
        }

        // 校验merCode
        List<String> merCodeList = Arrays.asList(merCodes);
        PageResult<MerchantList> merchantList = merchantService.merchant(new MerchantCriteria(merCodeList), PageRequest.of(0, merCodeList.size()));
        if (merchantList.getData() == null || merchantList.getData().isEmpty() || merchantList.getData().size() != merCodeList.size()) {
            return RestUtil.toRest(RestEnum.MER_CODE_NOT_BELONG_USER);
        }

        // 批量查询所有商户信息，避免多次查询
        List<MerchantInfoDetail> merchantDetails = merchantService.merchantDetailList(merCodeList);
        if (merchantDetails == null || merchantDetails.isEmpty() || merchantDetails.size() != merCodeList.size()) {
            return RestUtil.toRest(RestEnum.MER_CODE_NOT_EXIST);
        }

        boolean currencyInconsistent = false;

        for (MerchantInfoDetail merchant : merchantDetails) {
            if (!create.getCurrency().equals(merchant.getTransactionCurrency())) {
                currencyInconsistent = true;
                break;
            }
        }

        if (currencyInconsistent) {
            return RestUtil.toRest(RestEnum.MERCHANT_CURRENCY_INCONSISTENT);
        }

        // 使用第一个商户的机构编码
        MerchantList merchant = merchantList.getData().get(0);
        // 校验订单名称是否存在
        List<EpayBill> epayBill = epayBillRepository.findAll((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderName"), create.getOrderName()));
        if (!epayBill.isEmpty()) {
            return RestUtil.toRest(RestEnum.BILL_ORDER_NAME_EXIST);
        }

        // 参数设置
        EpayBill bill = epayBillCreateMapper.toEntity(create);
        bill.setInsCode(merchant.getInsCode());
        bill.setTotalAmount(create.getAmount() + create.getCommission());
        bill.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
        bill.setUsername(SecurityUtils.getCurrentLoginUser().getUsername());
        bill.setOrderNum("ES" + refundNoGenerator.generateUniqueRefundNo());
        bill.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        bill.setNotifyUrl(dataServerProperties.getWebUrl() + "/callback/pay");
        bill.setCurrency(create.getCurrency());
        bill.setMerCode(null);

        // 查询并设置paymentType - 使用第一个商户编码查询paymentType
        String firstMerCode = create.getMerCode().split(",")[0];
        try {
            String paymentType = merchantService.getPaymentTypeByMerCode(firstMerCode);
            bill.setPaymentType(paymentType);
            log.info("Set paymentType: {} for merCode: {}", paymentType, firstMerCode);
        } catch (Exception e) {
            log.warn("Failed to get paymentType for merCode: {}, error: {}", firstMerCode, e.getMessage());
            // 继续执行，不中断流程
        }
        long diff = 0;
        if (create.getExpiryTimestamp() != null) {
            Date expiryTime = new Date(create.getExpiryTimestamp());
            diff = expiryTime.getTime() - new Date().getTime();
            if (diff < 0) {
                return RestUtil.toRest(RestEnum.EXPIRY_TIMESTAMP_ILLEGAL);
            }
        }

        // 创建支付方式集合
        List<EpayPayChannel> payChannelList = new ArrayList<>();
        String[] merCode = create.getMerCode().split(",");
        for (String code : merCode) {
            EpayPayChannel payChannel = new EpayPayChannel();
            payChannel.setMerCode(code);
            payChannel.setOrderNum(bill.getOrderNum());
            payChannelList.add(payChannel);
        }
        if (!Objects.equals(create.getMode(), ModeEnum.EDIT.getName())) {
            // 发送邮件通知
            if (StringUtils.isNotEmpty(create.getPaymentEmail())) {
                try {
                    String data = AES256Encryption.encrypt(bill.getOrderNum(), AesProperties.dataKey);
                    data = URLEncoder.encode(data, "UTF-8");
                    // 获取当前日期
                    LocalDate newDate = LocalDate.from(DateUtil.toLocalDateTime(new Date(create.getExpiryTimestamp())));
                    // 定义日期格式
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy").withLocale(Locale.ENGLISH);
                    // 格式化当前日期
                    String formattedDate = newDate.format(formatter);
                    String content = "<html>\n" +
                            "    <head>\n" +
                            "        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n" +
                            "    </head>\n" +
                            "    <body>\n" +
                            "        <p>" + create.getEmailMessage() + "</p>\n" +
                            "        <p>Thank you for using Dynamic Payment's BillPay Services.<br>\n" +
                            "            感谢您使用 Dynamic Payment 的 BillPay 付款服务。</p>\n" +
                            "        <p>\n" +
                            "            Dynamic ePay <br>\n" +
                            "            Bill number 账单号: <span style=\"margin-left: 50px\">" + bill.getOrderName() + "</span> <br>\n" +
                            "            Item name 项目名称: <span style=\"margin-left: 37px\">" + bill.getItemName() + "</span> <br>\n" +
                            "            Amount 金额: <span style=\"margin-left: 90px\">" + bill.getCurrency() + " " + new BigDecimal(bill.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) + "</span> <br>\n" +
                            "            Surcharge 手续费: <span style=\"margin-left: 90px\">" + bill.getCurrency() + " " + new BigDecimal(bill.getCommission()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) + "</span> <br>\n" +
                            "            TotalAmount 总金额: <span style=\"margin-left: 90px\">" + bill.getCurrency() + " " + new BigDecimal(bill.getTotalAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) + "</span> <br>\n" +
                            "            Expiry date 到期日: <span style=\"margin-left: 50px\">" + formattedDate + "</span> <br>\n" +
                            "        </p>\n" +
                            "\n" +
                            "        <p>\n" +
                            "            Please click the <a href=\"" + dataServerProperties.getEmailJumpUrl() + "?data=" + data + "\">Payment Link</a> to proceed with the payment.<br>\n" +
                            "            请点击 <a href=\"" + dataServerProperties.getEmailJumpUrl() + "?data=" + data + "\">付款链接</a> 继续付款。\n" +
                            "        </p>\n" +
                            "    </body>\n" +
                            "</html>";
                    Thread thread2 = new Thread(new Runnable() {
                        @Override
                        public void run() {
                            emailSender.sendEmail(create.getPaymentEmail(), create.getEmailTitle(), content);
                        }
                    });
                    thread2.start();
                } catch (Exception e) {
                    log.error("BillPayOrderServiceImpl create err {}", e.getMessage());
                    throw new RuntimeException(e);
                }
//                }
            }

            // 创建取消延时消息
            if (diff > 0) {
                Map<String, String> map = new HashMap<>();
                map.put("orderNum", bill.getOrderNum());
                redisDelayQueueUtil.addDelayQueue(map, diff, TimeUnit.MILLISECONDS, RedisDelayQueueEnum.BILL_PAYMENT_TIMEOUT.getCode());
            }
        }
        // 添加
        epayBillRepository.save(bill);
        // 保存支付渠道
        epayPayChannelRepository.saveAll(payChannelList);
        return RestUtil.ok(new JSONObject().fluentPut("orderNum", bill.getOrderNum()));
    }

    /**
     * 查询账单数据
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<EpayBillDto> page(EpayBillCriteria criteria, Pageable pageable) {
        Page<EpayBill> page = epayBillRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return new PageResult<>(epayBillDtoMapper.toDto(page.getContent()), page.getTotalElements());
    }

    /**
     * 支付账单数据的更改
     *
     * @param update
     * @param id
     * @return
     */
    @Override
    public RestResult update(EpayBillUpdate update, Integer id) {
        Optional<EpayBill> billOptional = epayBillRepository.findById(id);
        if (!billOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
        }
        EpayBill bill = billOptional.get();
        // 非草稿订单不可转为草稿
        if (update.getMode() != null && Objects.equals(update.getMode(), ModeEnum.EDIT.getName()) && !Objects.equals(bill.getMode(), ModeEnum.EDIT.getName())) {
            return RestUtil.toRest(RestEnum.BILL_NOT_CONVERT_TO_DRAFT);
        }

        // 校验是否锁定
        if ("unlock".equals(bill.getMode()) && "lock".equals(update.getMode())) {
            // 校验是否在有效期
            if (bill.getExpiryTimestamp() < DateUtil.date().getTime()) {
                return RestUtil.toRest(RestEnum.BILL_EXPIRE);
            }
        }
        if ("lock".equals(bill.getMode()) && update.getValid() != null && update.getValid() == 0) {
            return RestUtil.toRest(RestEnum.BILL_LOCK_NOT_DELETE);
        }

        // 更新支付渠道
        if (update.getMerCode() != null && !update.getMerCode().isEmpty()) {
            // 验证当前用户是否有权限使用这些商户编码
            JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
            String[] merCodes = update.getMerCode().split(",");
            for (String merCode : merCodes) {
                if (!currentUser.getAuthMap().containsKey(merCode) || !currentUser.getAuthMap().get(merCode).contains("A300020014")) {
                    throw new CustomException(HttpStatus.FORBIDDEN.value(), "not merchant permission for: " + merCode);
                }
            }

            // 校验所有商户的货币是否一致
            if (merCodes.length > 1) {
                // 批量查询所有商户信息，避免多次查询
                List<String> merCodeList = Arrays.asList(merCodes);
                List<MerchantInfoDetail> merchantDetails = merchantService.merchantDetailList(merCodeList);
                if (merchantDetails == null || merchantDetails.isEmpty() || merchantDetails.size() != merCodeList.size()) {
                    return RestUtil.toRest(RestEnum.MER_CODE_NOT_EXIST);
                }

                String currency = null;
                boolean currencyInconsistent = false;

                for (MerchantInfoDetail merchant : merchantDetails) {
                    if (currency == null) {
                        currency = merchant.getTransactionCurrency();
                    } else if (!currency.equals(merchant.getTransactionCurrency())) {
                        currencyInconsistent = true;
                        break;
                    }
                }

                if (currencyInconsistent) {
                    return RestUtil.toRest(RestEnum.MERCHANT_CURRENCY_INCONSISTENT);
                }
            }

            // 删除旧的支付渠道
            List<EpayPayChannel> oldChannels = epayPayChannelRepository.findByOrderNum(bill.getOrderNum());
            if (oldChannels != null && !oldChannels.isEmpty()) {
                epayPayChannelRepository.deleteAll(oldChannels);
            }

            // 创建新的支付渠道
            List<EpayPayChannel> newChannels = new ArrayList<>();
            for (String merCode : merCodes) {
                EpayPayChannel channel = new EpayPayChannel();
                channel.setOrderNum(bill.getOrderNum());
                channel.setMerCode(merCode);
                newChannels.add(channel);
            }
            epayPayChannelRepository.saveAll(newChannels);

            // 更新账单的主商户编码为第一个商户
            bill.setMerCode(merCodes[0]);
        }

        // 草稿转为正式订单处理
        if (update.getMode() != null && !Objects.equals(update.getMode(), ModeEnum.EDIT.getName()) && Objects.equals(bill.getMode(), ModeEnum.EDIT.getName())) {
            // 创建交易
//            String payLink = getPayLink(bill);
//            if (StringUtils.isEmpty(payLink)) {
//                return RestUtil.toRest(RestEnum.BILL_PAY_ERR);
//            }
//            bill.setPayLink(payLink);
            // 发送邮件
            if (StringUtils.isNotEmpty(bill.getPaymentEmail())) {
                Thread thread = new Thread(new Runnable() {
                    @Override
                    public void run() {
                        emailSender.sendEmail(bill.getPaymentEmail(), bill.getEmailTitle(), bill.getEmailMessage());
                    }
                });
                thread.start();
            }
            // 创建取消延时消息
            if (bill.getExpiryTimestamp() != null) {
                Date expiryTime = new Date(bill.getExpiryTimestamp());
                long diff = expiryTime.getTime() - new Date().getTime();
                Map<String, String> map = new HashMap<>();
                map.put("orderNum", bill.getOrderNum());
                redisDelayQueueUtil.addDelayQueue(map, diff, TimeUnit.MILLISECONDS, RedisDelayQueueEnum.BILL_PAYMENT_TIMEOUT.getCode());
            }
        }
        // 处理手续费和总金额的更新
        boolean needUpdateTotal = false;
        if (update.getCommission() != null) {
            bill.setCommission(update.getCommission());
            needUpdateTotal = true;
        }

        if (needUpdateTotal) {
            // 更新totalAmount，确保等于amount+commission
            bill.setTotalAmount(bill.getAmount() + bill.getCommission());
        } else if (update.getTotalAmount() != null) {
            // 如果直接设置totalAmount，需要验证是否符合规则
            if (update.getTotalAmount() != bill.getAmount() + bill.getCommission()) {
                return RestUtil.toRest(RestEnum.BILL_MOUNT_ERROR);
            }
            bill.setTotalAmount(update.getTotalAmount());
        }

        if (update.getIsFavourite() != null) {
            bill.setIsFavourite(update.getIsFavourite());
        }
        if (update.getBillType() != null) {
            bill.setBillType(update.getBillType());
        }
        if (update.getValid() != null) {
            bill.setValid(update.getValid());
        }
        if (update.getMode() != null) {
            bill.setMode(update.getMode());
        }
        epayBillRepository.save(bill);
        return RestUtil.ok();
    }

    /**
     * 获取支付链接
     *
     * @param epayBill
     * @param customInfo
     * @return
     */
    private String getPayLink(EpayBill epayBill, Optional<EpayTransaction> transactionOptional, EpayTransactionUpdateCustomInfo customInfo) {
        // 查询商户信息
        Merchant merchantMerKey = merchantService.merchantInfo(epayBill.getMerCode());
        if (merchantMerKey == null) {
            throw new BadRequestException(RestEnum.MER_CODE_NOT_EXIST.getMessage());
        }
        List<String> merCodes = new ArrayList<>();
        merCodes.add(epayBill.getMerCode());
        List<MerchantInfoDetail> merchantList = merchantService.merchantDetailList(merCodes);
        if (merchantList.isEmpty()) {
            throw new BadRequestException(RestEnum.MER_CODE_NOT_EXIST.getMessage());
        }
        MerchantInfoDetail merchant = merchantList.get(0);
        String paymentType = GatewayEnum.getDescByName(merchant.getGateway());
        Map<String, Object> parames = new HashMap<>();
        parames.put("out_trade_no", "ET" + refundNoGenerator.generateUniqueRefundNo());
        parames.put("sub_mch_id", customInfo.getMerCode()); //商户号
        parames.put("total_fee", epayBill.getTotalAmount()); //金额
        parames.put("currency", merchant.getSettlementCurrency()); //货币
        parames.put("spbill_create_ip", StringUtils.getLocalIp());
//        parames.put("spbill_create_ip", "0.0.0.0");
        if (TransactionPaymentMethodEnum.WECHAT.getName().equals(paymentType)) { //微信
            parames.put("service", TransactionPaymentMethodEnum.WECHAT_NATIVE.getName());
        }
        if (TransactionPaymentMethodEnum.ALIPAY.getName().equals(paymentType)) { //支付宝
            parames.put("service", TransactionPaymentMethodEnum.ALIPAY_NATIVE.getName());
        }
        if (TransactionPaymentMethodEnum.UNION_PAY.getName().equals(paymentType)) { //银联
            parames.put("service", TransactionPaymentMethodEnum.UNION_PAY_NATIVE.getName());
        }
        parames.put("body", epayBill.getOrderName());
//        parames.put("body", "test_body");
        parames.put("device_info", PayUtil.getDeviceInfo(epayBill.getUserId(), "billpay"));
        parames.put("msg_version", "1.2");
        parames.put("notify_url", epayBill.getNotifyUrl()); //通知地址
        String sign = Md5Utils.md5Sign(parames, PayUtil.getKey(merchantMerKey.getMerKey()));
        parames.put("sign", sign);
        parames.put("sign_type", "MD5"); //通知地址
        log.info("getPaymentLink parames {}", parames);
        // 查询内容
        HttpRequest request = HttpUtil.createPost(dataServerProperties.getPayUrl());
        request.body(JSON.toJSONString(parames));
        HttpResponse response = request.execute();
        // 处理结果
        JSONObject resListJson = JSON.parseObject(response.body());
        log.info("getPaymentLink resListJson {}", resListJson);
        if (response.getStatus() == HttpStatus.OK.value()) {
            // 判断请求是否成功
            if (resListJson.containsKey("returncode") && resListJson.getString("returncode").equals("FAIL")) {
                throw new BadRequestException(resListJson.getString("returnmsg"));
            }
            if ("SUCCESS".equals(resListJson.getString("result_code"))) {
                // 更新交易记录
                if (transactionOptional.isPresent()) {
                    EpayTransaction transaction = transactionOptional.get();
                    transaction.setOutTradeNo(parames.get("out_trade_no").toString());
//                    transaction.setTransactionId(resListJson.get("prepay_id").toString());
                    if (customInfo != null) {
                        transaction.setCustomerAddress(customInfo.getCustomerAddress());
                        transaction.setCustomerEmail(customInfo.getCustomerEmail());
                        transaction.setCustomerName(customInfo.getCustomerName());
                        transaction.setCustomerPhone(customInfo.getCustomerPhone());
                    }
                    epayTransactionRepository.save(transaction);
                } else {
                    // 创建交易
                    EpayTransaction transaction = new EpayTransaction();
                    transaction.setOrderNum(epayBill.getOrderNum());
                    if (SecurityUtils.isTemporary()) {
                        transaction.setOrgId(epayBill.getOrgId());
                        transaction.setUserId(epayBill.getUserId());
                    } else {
                        transaction.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
                        transaction.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
                    }
                    transaction.setOutTradeNo(parames.get("out_trade_no").toString());
//                    transaction.setTransactionId(resListJson.get("prepay_id").toString());
                    transaction.setInsCode(epayBill.getInsCode());
                    transaction.setMerCode(epayBill.getMerCode());
                    transaction.setBillType(epayBill.getBillType());
                    transaction.setPaymentType(paymentType);
                    transaction.setItemName(epayBill.getItemName());
                    transaction.setAmount(epayBill.getAmount());
                    transaction.setCurrency(epayBill.getCurrency());
                    transaction.setCommission(epayBill.getCommission());
                    transaction.setTotalAmount(epayBill.getTotalAmount());
                    transaction.setStatus(0);
                    transaction.setTraceType("Sale");
                    transaction.setCustomerAddress(customInfo.getCustomerAddress());
                    transaction.setCustomerEmail(customInfo.getCustomerEmail());
                    transaction.setCustomerName(customInfo.getCustomerName());
                    transaction.setCustomerPhone(customInfo.getCustomerPhone());
                    epayTransactionRepository.save(transaction);
                    epayBill.setStatus(1);
                    epayBillRepository.save(epayBill);
                }
                // 链接加入缓存
                String url = resListJson.getString("code_url");
                if (TransactionPaymentMethodEnum.WECHAT.getName().equals(epayBill.getPaymentType())) { //微信
                    redisUtils.set(RedisConstants.PAY_BY_LINK + parames.get("out_trade_no"), url, 7200, TimeUnit.SECONDS);
                }
                if (TransactionPaymentMethodEnum.ALIPAY.getName().equals(epayBill.getPaymentType())) { //支付宝
                    redisUtils.set(RedisConstants.PAY_BY_LINK + parames.get("out_trade_no"), url, 180, TimeUnit.SECONDS);
                }
                return url;
            } else {
                throw new BadRequestException(resListJson.getString("err_code_des"));
            }
        } else {
            throw new BadRequestException(RestEnum.BILL_PAY_ERR.getMessage());
        }
    }

    /**
     * 退款
     *
     * @param refund
     * @return
     */
    @Override
    @Transactional
    public RestResult refund(EpayBillRefund refund) {
        Optional<EpayTransaction> epayTransactionOptional = epayTransactionRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("outTradeNo"), refund.getOutTradeNo()));
        if (!epayTransactionOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.TRANSACTION_NOT_EXIST);
        }
        EpayTransaction transaction = epayTransactionOptional.get();
        // 权限校验
        JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
        if (!currentUser.getPermissions().contains("A300020013")) {
            throw new CustomException(HttpStatus.FORBIDDEN.value(), "not permission！");
        }
        if (!currentUser.getAuthMap().containsKey(transaction.getMerCode()) || !currentUser.getAuthMap().get(transaction.getMerCode()).contains("A300020013")) {
            throw new CustomException(HttpStatus.FORBIDDEN.value(), "not permission！");
        }
        if (transaction.getStatus() != 1) {
            return RestUtil.toRest(RestEnum.TRANSACTION_NOT_PAY);
        }
        Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNum"), transaction.getOrderNum()));
        if (!billOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
        }
        EpayBill bill = billOptional.get();
        RestResult res = RestUtil.toRest(RestEnum.SUCCESS);
        try {
            // 密码校验
            if (!passwordEncoder.matches(refund.getPassword(), SecurityUtils.getCurrentLoginUser().getPassword())) {
                return RestUtil.toRest(RestEnum.PASSWORD_ERR);
            }
            // 查询交易数据
            PageResult<TransactionList> page = transactionService.realtime(new TransactionCriteria(transaction.getOutTradeNo()), PageRequest.of(0, 1));
            List<TransactionList> list = page.getData();
            if (list.isEmpty()) {
                return RestUtil.toRest(RestEnum.TRANSACTION_NOT_EXIST);
            }

            // tableName : TBL_DIRECT_POS POS机 TBL_ONLINE_TXN 银联
            TransactionList transactionDetail = list.get(0);
            // 校验商户权限
//            JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
//            List<String> merCodes = new ArrayList<>();
//            for (String key : currentUser.getAuthMap().keySet()) {
//                if ("history".equals(refundReq.getType()) && currentUser.getAuthMap().get(key).contains("A200020014")) {
//                    merCodes.add(key);
//                }
//                if ("realtime".equals(refundReq.getType()) && currentUser.getAuthMap().get(key).contains("A200020014")) {
//                    merCodes.add(key);
//                }
//            }
//            if (!merCodes.contains(list.get(0).getMerCode())) {
//                throw new CustomException(HttpStatus.FORBIDDEN.value(), "not permission！");
//            }
            // 操作退款
            // 查询商户信息
            Merchant merchant = merchantService.merchantInfo(transactionDetail.getMerCode());
            if (merchant == null) {
                return RestUtil.toRest(RestEnum.MER_CODE_NOT_EXIST);
            }
            // 校验交易
//            if (bill.getPaymentType().equals(TransactionPaymentMethodEnum.UNION_PAY.getName())) {
//                if (!SupportFuncUtil.isVoid(transactionDetail.getSupportFuncFlag())) {
//                    return RestUtil.toRest(RestEnum.TRANSACTION_CURRENT_STATUS_NOT_REFUND);
//                }
//            } else {
//                if (!SupportFuncUtil.isRefund(transactionDetail.getSupportFuncFlag())) {
//                    return RestUtil.toRest(RestEnum.TRANSACTION_CURRENT_STATUS_NOT_REFUND);
//                }
//            }
            String refundNum = "ER" + refundNoGenerator.generateUniqueRefundNo();
            transaction.setRefundNum(refundNum);
            EpayTransaction epayTransaction = new EpayTransaction();
            BeanUtil.copyProperties(transaction, epayTransaction);
            epayTransaction.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
            epayTransaction.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
            epayTransaction.setStatus(1);
            epayTransaction.setOutTradeNo(refundNum);
            epayTransaction.setId(null);
            epayTransaction.setCreateTime(null);
            epayTransaction.setUpdateTime(null);
            epayTransaction.setValid(1);
            epayTransaction.setRefundNum(null);
            epayTransaction.setTransactionId(null);
            bill.setStatus(3);
            transaction.setStatus(2);
            epayBillRepository.save(bill);
            // 业务操作
            if (TransactionPaymentMethodEnum.WECHAT.getName().equals(transactionDetail.getPaymentMethod()) ||
                    TransactionPaymentMethodEnum.ALIPAY.getName().equals(transactionDetail.getPaymentMethod())) {
                epayTransaction.setTraceType("Refund");
                res = getWechatAliResult(refund, transaction, PayUtil.getKey(merchant.getMerKey()), refundNum);
            } else {
                epayTransaction.setTraceType("Valid");
                res = getUniPayResult(refund, transaction, PayUtil.getKey(merchant.getMerKey()), refundNum);
            }
            epayTransactionRepository.save(epayTransaction);
            epayTransactionRepository.save(transaction);
            return res;
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 初始化交易数据
     *
     * @param login
     * @return
     */
    @Override
    public RestResult login(EpayBillLogin login) throws Exception {
        String orderNum = AES256Encryption.decrypt(login.getData(), AesProperties.dataKey);
        Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNum"), orderNum));
        if (!billOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
        }
        EpayBill bill = billOptional.get();
        if (bill.getMode().equals(ModeEnum.LOCK.getName())) {
            return RestUtil.toRest(RestEnum.BILL_TIMEOUT);
        }
        if (bill.getExpiryTimestamp() < new Date().getTime()) {
            return RestUtil.toRest(RestEnum.BILL_TIMEOUT);
        }
        // 校验是否只能支付一次 并判断是否已支付
        if (oneOffIsPay(bill)) return RestUtil.toRest(RestEnum.BILL_ALREADY_PAY);
        EpayBillLoginRes billLogin = billLoginMapper.toDto(bill);
        AnonymousAuthenticationToken anonymousToken = new AnonymousAuthenticationToken(
                "key",
                bill.getOrderNum(),
                Collections.singletonList(new SimpleGrantedAuthority("Transformer"))
        );
        SecurityContextHolder.getContext().setAuthentication(anonymousToken);
        String token = tokenProvider.createToken(anonymousToken);
        String key = RedisConstants.TEMP_KEY + bill.getOrderNum() + "-" + DigestUtil.md5Hex(token);
        System.out.println("key:" + key);
        redisUtils.set(key, bill.getOrderNum(), 3600, TimeUnit.SECONDS);
        billLogin.setTempToken(token);
        return RestUtil.ok(billLogin);
    }

    /**
     * 获取支付链接
     *
     * @param id
     * @param customInfo
     * @return
     */
    @Override
    @Transactional
    public RestResult payByLink(Integer id, EpayTransactionUpdateCustomInfo customInfo) {
        try {
            Optional<EpayBill> billOptional = epayBillRepository.findById(id);
            if (!billOptional.isPresent()) {
                return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
            }
            EpayBill bill = billOptional.get();
            if (Objects.equals(bill.getMode(), ModeEnum.LOCK.getName())) {
                return RestUtil.toRest(RestEnum.BILL_LOCKED);
            }
            if (Objects.equals(bill.getMode(), ModeEnum.EDIT.getName())) {
                return RestUtil.toRest(RestEnum.BILL_IS_DRAFT);
            }
            // 校验是否只能支付一次 并判断是否已支付
            if (oneOffIsPay(bill)) return RestUtil.toRest(RestEnum.BILL_ALREADY_PAY);
            Optional<EpayTransaction> transactionOptional = epayTransactionRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("orderNum"), bill.getOrderNum()),
                    criteriaBuilder.equal(root.get("traceType"), "Sale"),
                    criteriaBuilder.equal(root.get("status"), 0)));
            if (transactionOptional.isPresent()) {
                EpayTransaction transaction = transactionOptional.get();
                Object obj = redisUtils.get(RedisConstants.PAY_BY_LINK + transaction.getOutTradeNo());
                if (obj != null) {
                    return RestUtil.ok(new JSONObject().fluentPut("url", String.valueOf(obj)));
                }
            }
            String payByLink = getPayLink(bill, transactionOptional, customInfo);
            String data = AES256Encryption.encrypt(bill.getOrderNum(), AesProperties.dataKey);
            data = URLEncoder.encode(data, "UTF-8");
            return RestUtil.ok(new JSONObject().fluentPut("url", payByLink).fluentPut("copyUrl", dataServerProperties.getEmailJumpUrl() + "?data=" + data));
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public RestResult payByLinkQrCode(Integer id) {
        try {
            Optional<EpayBill> billOptional = epayBillRepository.findById(id);
            if (!billOptional.isPresent()) {
                return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
            }
            EpayBill bill = billOptional.get();
            String data = AES256Encryption.encrypt(bill.getOrderNum(), AesProperties.dataKey);
            data = URLEncoder.encode(data, "UTF-8");
            return RestUtil.ok(new JSONObject().fluentPut("url", dataServerProperties.getEmailJumpUrl() + "?data=" + data));
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 提交一笔支付交易
     *
     * @param payment
     * @return
     */
    @Override
    public RestResult payment(EpayBillPayment payment) {
        try {
            // 校验是否有权限操作
            String orderNum = SecurityUtils.getTempKey();
            if (!orderNum.equals(payment.getOrderNum())) {
                return RestUtil.toRest(RestEnum.NOT_POWER);
            }
            // 查询账单
            Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNum"), payment.getOrderNum()));
            if (!billOptional.isPresent()) {
                return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
            }
            EpayBill bill = billOptional.get();

            // 验证选择的商户是否为该账单的支付渠道之一
            List<EpayPayChannel> payChannels = epayPayChannelRepository.findByOrderNum(bill.getOrderNum());
            boolean validChannel = false;
            for (EpayPayChannel channel : payChannels) {
                if (channel.getMerCode().equals(payment.getMerCode())) {
                    validChannel = true;
                    break;
                }
            }
            if (!validChannel) {
                return RestUtil.toRest(RestEnum.MER_CODE_NOT_BELONG_ORDER);
            }

            // 校验是否只能支付一次 并判断是否已支付
            if (oneOffIsPay(bill)) return RestUtil.toRest(RestEnum.BILL_ALREADY_PAY);
            // 校验账单是否超时
            if (bill.getExpiryTimestamp() < new Date().getTime()) {
                return RestUtil.toRest(RestEnum.BILL_TIMEOUT);
            }
            // 查询商户信息
            Merchant merchant = merchantService.merchantInfo(payment.getMerCode());
            if (merchant == null) {
                return RestUtil.toRest(RestEnum.MER_CODE_NOT_EXIST);
            }
            Optional<EpayTransaction> transactionOptional = epayTransactionRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(
                    criteriaBuilder.equal(root.get("orderNum"), bill.getOrderNum()),
                    criteriaBuilder.equal(root.get("traceType"), "Sale"),
                    criteriaBuilder.equal(root.get("status"), 0)));
            String outTradeNo = "ET" + refundNoGenerator.generateUniqueRefundNo();
            Map<String, Object> parames = new TreeMap<>();
            parames.put("out_trade_no", outTradeNo);
            parames.put("sub_mch_id", payment.getMerCode()); //商户号
            parames.put("total_fee", bill.getTotalAmount());
            parames.put("currency", bill.getCurrency());
            parames.put("spbill_create_ip", "0.0.0.0");
            parames.put("service", "unified.upop.web");
            parames.put("time_out", 1800);
            parames.put("charset", "UTF-8");
            parames.put("trade_type", "01");
            parames.put("channel_type", "07");
            parames.put("time_start", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
            parames.put("notify_url", bill.getNotifyUrl());
            parames.put("mer_front_end_url", payUrlProperties.getCallPageUrl());
            parames.put("mer_front_fail_url", payUrlProperties.getCallPageUrl());
            parames.put("acct_num", ""); //卡号
            parames.put("mer_reserved", "{cardNumberLock=1}");
            String sign = Md5Utils.md5Sign(parames, PayUtil.getKey(merchant.getMerKey()));
            parames.put("sign", sign);
            parames.put("sign_type", "MD5");
            String url = dataServerProperties.getOnlinePayUrl() + "?" + HttpUtil.toParams(parames);
            HttpRequest request = HttpUtil.createGet(url);
            log.info("BillPayOrderServiceImpl payment parames: {}", JSONObject.toJSONString(parames));
            HttpResponse response = request.execute();
            log.info("BillPayOrderServiceImpl payment response: {}", response.body());
            if (response.getStatus() == HttpStatus.OK.value()) {
                // 更新交易记录
                if (!transactionOptional.isPresent()) {
                    // 创建交易
                    EpayTransaction transaction = new EpayTransaction();
                    transaction.setOrderNum(bill.getOrderNum());
                    transaction.setOrgId(bill.getOrgId());
                    transaction.setUserId(bill.getUserId());
                    transaction.setOutTradeNo(outTradeNo);
//                    transaction.setTransactionId(resListJson.get("prepay_id").toString());
                    transaction.setInsCode(bill.getInsCode());
                    transaction.setMerCode(payment.getMerCode());
                    transaction.setBillType(bill.getBillType());
                    transaction.setPaymentType(payment.getPaymentType());
                    transaction.setItemName(bill.getItemName());
                    transaction.setAmount(bill.getAmount());
                    transaction.setCommission(bill.getCommission());
                    transaction.setTotalAmount(bill.getTotalAmount());
                    transaction.setCurrency(bill.getCurrency());
                    transaction.setStatus(0);
                    transaction.setTraceType("Sale");
                    transaction.setCustomerAddress(payment.getCustomerAddress());
                    transaction.setCustomerEmail(payment.getCustomerEmail());
                    transaction.setCustomerName(payment.getCustomerName());
                    transaction.setCustomerPhone(payment.getCustomerPhone());
                    epayTransactionRepository.save(transaction);
                    bill.setStatus(1);
                    epayBillRepository.save(bill);
                } else {
                    EpayTransaction transaction = transactionOptional.get();
                    transaction.setOutTradeNo(outTradeNo);
                    transaction.setCustomerAddress(payment.getCustomerAddress());
                    transaction.setCustomerEmail(payment.getCustomerEmail());
                    transaction.setCustomerName(payment.getCustomerName());
                    transaction.setCustomerPhone(payment.getCustomerPhone());
                    epayTransactionRepository.save(transaction);
                }
                return RestUtil.ok(new JSONObject().fluentPut("resultUrl", response.body()));
            } else {
                throw new BadRequestException(response.body());
            }
        } catch (Exception ignored) {
            throw new BadRequestException(ignored.getMessage());
        }
    }

    private boolean oneOffIsPay(EpayBill bill) {
        if (BillTypeEnum.ONE_OFF.getName().equals(bill.getBillType())) {
            long transaction = epayTransactionRepository.count((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.and(criteriaBuilder.equal(root.get("orderNum"), bill.getOrderNum()),
                    criteriaBuilder.equal(root.get("status"), 1), criteriaBuilder.equal(root.get("traceType"), "Sale")));
            return transaction > 0;
        }
        return false;
    }

    /**
     * 获取支付结束数据
     *
     * @param login
     * @return
     */
    @Override
    public RestResult paymentEnd(EpayBillLogin login) {
        try {
            String orderNum = AES256Encryption.decrypt(login.getData(), AesProperties.dataKey);
            Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNum"), orderNum));
            if (!billOptional.isPresent()) {
                return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
            }
            EpayBill bill = billOptional.get();
            EpayBillDto billDto = epayBillDtoMapper.toDto(bill);
            return RestUtil.ok(billDto);
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public RestResult payByLinkTemporary(EpayTransactionUpdateCustomInfo customInfo, Integer id) {
        Optional<EpayBill> billOptional = epayBillRepository.findById(id);
        if (!billOptional.isPresent()) {
            return RestUtil.toRest(RestEnum.BILL_NOT_EXIST);
        }
        EpayBill bill = billOptional.get();
        // 校验是否有权限操作
        String orderNum = SecurityUtils.getTempKey();
        if (!orderNum.equals(bill.getOrderNum())) {
            return RestUtil.toRest(RestEnum.NOT_POWER);
        }

        // 如果指定了商户编码，则验证是否为该账单的支付渠道之一
        if (customInfo != null && customInfo.getMerCode() != null && !customInfo.getMerCode().isEmpty()) {
            List<EpayPayChannel> payChannels = epayPayChannelRepository.findByOrderNum(bill.getOrderNum());
            boolean validChannel = false;
            for (EpayPayChannel channel : payChannels) {
                if (channel.getMerCode().equals(customInfo.getMerCode())) {
                    validChannel = true;
                    break;
                }
            }
            if (!validChannel) {
                return RestUtil.toRest(RestEnum.MER_CODE_NOT_BELONG_ORDER);
            }
            // 临时设置账单的商户编码为选择的编码
            bill.setMerCode(customInfo.getMerCode());
        }

        return payByLink(id, customInfo);
    }

    private RestResult getUniPayResult(EpayBillRefund refundReq, EpayTransaction transactionList, String merKey, String refundNum) {
        Map<String, Object> reqMap = new TreeMap<>();
        // 退款订单号
        reqMap.put("out_refund_no", refundNum);
        // 原交易订单号
        reqMap.put("out_trade_no", transactionList.getOutTradeNo());
        // 退款金额
        reqMap.put("refund_fee", String.valueOf(refundReq.getAmount().intValue()));
        // 退款币种
        reqMap.put("refund_fee_type", transactionList.getCurrency());
        // 交易ID
        reqMap.put("transaction_id", transactionList.getTransactionId());
        // 版本号
        reqMap.put("msg_version", "1.2");
        // 接口类型
        reqMap.put("service", TransactionPaymentMethodEnum.UNION_PAY_REFUND.getName());
        // 商户号
        reqMap.put("sub_mch_id", transactionList.getMerCode().trim());
        // 通知地址
        reqMap.put("notify_url", "http://opptest.hejhej.top/api/payment/notify");
        // 字元集
        // 签名
        reqMap.put("sign", Md5Utils.md5Sign(reqMap, merKey));
        // 签名方式
        reqMap.put("sign_type", "MD5");
        // 查询内容
        HttpRequest request = HttpUtil.createPost(dataServerProperties.getRefundUrl());
        log.info("refund request: {}", JSON.toJSONString(reqMap));
        request.body(JSONObject.toJSONString(reqMap));
        HttpResponse response = request.execute();
        // 处理结果
        JSONObject resListJson = JSON.parseObject(response.body());
        log.info("refund response: {}", resListJson.toJSONString());
        // 判断请求是否成功
        if (resListJson.containsKey("returncode") && resListJson.getString("returncode").equals("FAIL")) {
            throw new BadRequestException(resListJson.getString("returnmsg"));
        }
        if ("SUCCESS".equals(resListJson.getString("result_code"))) {
//            updateRefund(refundReq.getOutTradeNo());
            return RestUtil.toRest(RestEnum.SUCCESS);
        } else {
            throw new BadRequestException(resListJson.getString("return_msg"));
        }
    }


    private RestResult getWechatAliResult(EpayBillRefund refundReq, EpayTransaction transactionList, String merKey, String refundNum) {
        Map<String, Object> reqMap = new TreeMap<>();
        // 接口类型
        reqMap.put("service", TransactionUtil.getRefund(transactionList.getPaymentType()));
        // 商户号
        reqMap.put("sub_mch_id", transactionList.getMerCode().trim());
        // 设备终端号
        reqMap.put("device_info", PayUtil.getDeviceInfo(SecurityUtils.getCurrentLoginUser().getUserId(), "billpay"));
        // 原消费金额
        reqMap.put("total_fee", String.valueOf(refundReq.getAmount().intValue()));
        // 交易币种
        reqMap.put("currency", transactionList.getCurrency());
        // 原交易订单号
        reqMap.put("out_trade_no", transactionList.getOutTradeNo());
        // 退款订单号
        reqMap.put("out_refund_no", refundNum);
        // 退款金额
        reqMap.put("refund_fee", String.valueOf(refundReq.getAmount().intValue()));
        // 退款币种
        reqMap.put("refund_fee_type", transactionList.getCurrency());
        // 通知地址
        reqMap.put("notify_url", "http://opptest.hejhej.top/api/payment/notify");
        // 版本号
        reqMap.put("msg_version", "1.2");
        // 字元集
        reqMap.put("charset", "UTF-8");
        // 签名
        reqMap.put("sign", Md5Utils.md5Sign(reqMap, merKey));
        reqMap.put("sign_type", "MD5");

        // 查询内容
        HttpRequest request = HttpUtil.createPost(dataServerProperties.getRefundUrl());
        log.info("refund request: {}", JSON.toJSONString(reqMap));
        request.body(JSONObject.toJSONString(reqMap));
        HttpResponse response = request.execute();
        // 处理结果
        JSONObject resListJson = JSON.parseObject(response.body());
        log.info("refund response: {}", resListJson.toJSONString());
        // 判断请求是否成功
        if (resListJson.containsKey("returncode") && resListJson.getString("returncode").equals("FAIL")) {
            throw new BadRequestException(resListJson.getString("returnmsg"));
        }
        if ("SUCCESS".equals(resListJson.getString("result_code"))) {
//            updateRefund(refundReq.getOutTradeNo());
            return RestUtil.toRest(RestEnum.SUCCESS);
        } else {
            throw new BadRequestException(resListJson.getString("err_code_des"));
        }
    }

    /**
     * 处理支付回调
     *
     * @param callback 回调参数
     * @return 重定向URL
     */
    @Override
    @Transactional
    public String handlePaymentCallback(EpayPaymentCallback callback) {
        log.info("收到支付回调：{}", JSON.toJSONString(callback));

        try {
            // 根据订单号查询交易
            Optional<EpayTransaction> transactionOptional = epayTransactionRepository.findOne((root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("outTradeNo"), callback.getOut_trade_no()));

            if (!transactionOptional.isPresent()) {
                log.error("未找到对应的交易记录：{}", callback);
                return payUrlProperties.getFrontFailPageUrl();
            }

            EpayTransaction transaction = transactionOptional.get();
//            Merchant merchant = merchantService.merchantInfo(transaction.getMerCode());
            // 签名校验
//            boolean isValid = CallbackSignUtil.verifyCallbackSign(callback, merchant.getMerKey());
//            if (!isValid) {
//                log.error("签名验证失败：{}", callback);
//                return payUrlProperties.getFrontFailPageUrl();
//            }

            // 获取账单
            Optional<EpayBill> billOptional = epayBillRepository.findOne((root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("orderNum"), transaction.getOrderNum()));
            if (billOptional.isPresent()) {
                EpayBill bill = billOptional.get();
                String endUrl = payUrlProperties.getFrontEndPageUrl() + "&data=" + AES256Encryption.encrypt(bill.getOrderNum(), AesProperties.dataKey);
                String failUrl = payUrlProperties.getFrontFailPageUrl() + "&data=" + AES256Encryption.encrypt(bill.getOrderNum(), AesProperties.dataKey);
                // 处理支付结果
                String resultCode = callback.getResult_code();
                if ("success".equals(resultCode) || "SUCCESS".equals(resultCode)) {
                    // 支付成功，更新交易状态
                    transaction.setStatus(1); // 1-Success
                    transaction.setTransactionId(callback.getTransaction_id());
                    epayTransactionRepository.save(transaction);
                    bill.setStatus(2); // 2-已支付
                    epayBillRepository.save(bill);
                    return endUrl;
                } else {
                    // 支付失败，更新交易状态
                    transaction.setStatus(2); // 0-Processing或失败状态
                    transaction.setTransactionId(callback.getTransaction_id()); // 0-Processing或失败状态
                    epayTransactionRepository.save(transaction);
                    // 更新账单状态
                    bill.setStatus(4); // 4-失败
                    epayBillRepository.save(bill);
                    return failUrl;
                }
            }
            return "处理支付回调异常";
        } catch (Exception e) {
            log.error("处理支付回调异常", e);
            return "处理支付回调异常";
        }
    }

    @Override
    public boolean existsTransaction(Integer billId) {
        // 先获取账单信息
        Optional<EpayBill> billOptional = epayBillRepository.findById(billId);
        if (!billOptional.isPresent()) {
            return false; // 账单不存在，返回false
        }

        EpayBill bill = billOptional.get();
        // 查询是否存在相关交易数据
        long transactionCount = epayTransactionRepository.count(
                (root, criteriaQuery, criteriaBuilder) ->
                        criteriaBuilder.equal(root.get("orderNum"), bill.getOrderNum())
        );

        return transactionCount > 0;
    }
}
