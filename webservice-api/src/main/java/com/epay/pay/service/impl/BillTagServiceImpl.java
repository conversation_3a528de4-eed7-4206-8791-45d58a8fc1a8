package com.epay.pay.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.epay.pay.domain.EpayTag;
import com.epay.pay.repository.EpayTagRepository;
import com.epay.pay.service.BillTagService;
import com.epay.pay.service.dto.EpayTagCreate;
import com.epay.pay.service.dto.EpayTagCriteria;
import com.epay.pay.service.dto.EpayTagUpdate;
import com.epay.pay.service.mapstruct.EpayTagCreateMapper;
import com.epay.pay.service.mapstruct.EpayTagDtoMapper;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class BillTagServiceImpl implements BillTagService {

    private final EpayTagRepository tagRepository;
    private final EpayTagDtoMapper tagDtoMapper;
    private final EpayTagCreateMapper tagCreateMapper;

    @Override
    public RestResult page(EpayTagCriteria criteria, Pageable pageable) {
        Page<EpayTag> page = tagRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return RestUtil.ok(new PageResult<>(tagDtoMapper.toDto(page.getContent()), page.getTotalElements()));
    }

    /**
     * 创建
     *
     * @param create
     * @return
     */
    @Override
    public RestResult create(EpayTagCreate create) {
        String currentUserId = SecurityUtils.getCurrentLoginUser().getUserId();
        String currentOrgId = SecurityUtils.getCurrentLoginUser().getOrgId();
        // 校验TagName是否重复
        Optional<EpayTag> existingTag = tagRepository.findByTagNameAndUserIdAndOrgId(
            create.getTagName(), currentUserId, currentOrgId);
        if (existingTag.isPresent()) {
            return RestUtil.toRest(RestEnum.TAG_NAME_EXIST);
        }
        EpayTag tag = tagCreateMapper.toEntity(create);
        tag.setUserId(currentUserId);
        tag.setTagId(IdGenerator.generateUniqueCode());
        tag.setOrgId(currentOrgId);
        tagRepository.save(tag);
        return RestUtil.ok(new JSONObject().fluentPut("tagId", tag.getTagId()));
    }

    /**
     * 更新
     *
     * @param update
     * @param id
     * @return
     */
    @Override
    public RestResult update(EpayTagUpdate update, Integer id) {
        Optional<EpayTag> tagOptional = tagRepository.findById(id);
        if (tagOptional.isPresent()) {
            EpayTag tag = tagOptional.get();
            // 如果要更新tagName，需要校验是否重复
            if (update.getTagName() != null && !update.getTagName().isEmpty()) {
                // 检查新的tagName是否与当前标签的tagName不同
                if (!update.getTagName().equals(tag.getTagName())) {
                    // 校验新的TagName是否已存在
                    Optional<EpayTag> existingTag = tagRepository.findByTagNameAndUserIdAndOrgId(
                        update.getTagName(), tag.getUserId(), tag.getOrgId());
                    if (existingTag.isPresent()) {
                        return RestUtil.toRest(RestEnum.TAG_NAME_EXIST);
                    }
                }
                tag.setTagName(update.getTagName());
            }
            if (update.getColor() != null && !update.getColor().isEmpty()) {
                tag.setColor(update.getColor());
            }
            if (update.getDisplay() != null && !update.getDisplay().isEmpty()) {
                tag.setDisplay(update.getDisplay());
            }
            tagRepository.save(tag);
        }
        return RestUtil.ok();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public RestResult delete(Integer id) {
        EpayTag tag = tagRepository.getById(id);
        if (StringUtils.isNotEmpty(tag.getOutTradeNo())) {
            return RestUtil.toRest(RestEnum.TAG_USED);
        }
        tagRepository.deleteById(id);
        return RestUtil.ok();
    }


}
