package com.epay.pay.service.dto;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EpayVoidDto extends BaseEntity {

    private Integer id;
    private String orderNum;
    private String orgId;
    private String userId;
    private String outTradeNo;
    private String transactionId;
    private String voidNum;
    private String merCode;
    private String effectiveType;
    private String payChannel;
    private String itemName;
    private String amount;
    private String currency;
    private String cardNum;
    private String status;

}
