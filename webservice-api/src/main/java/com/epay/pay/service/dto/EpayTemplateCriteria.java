package com.epay.pay.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
public class EpayTemplateCriteria {

    @Query(type = Query.Type.INNER_LIKE)
    private Integer userId;

    @Query(type = Query.Type.INNER_LIKE)
    private String templateName;

    @Query(type = Query.Type.EQUAL)
    private String billType;

    @Query(type = Query.Type.GREATER_THAN, propName = "amount")
    private Integer minAmount;

    @Query(type = Query.Type.LESS_THAN, propName = "amount")
    private Integer maxAmount;

    @Query(type = Query.Type.BETWEEN)
    private Timestamp[] createTime;

    private String searchKeyword;

}
