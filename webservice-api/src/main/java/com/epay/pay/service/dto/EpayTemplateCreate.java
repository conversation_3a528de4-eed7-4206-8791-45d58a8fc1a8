package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class EpayTemplateCreate {

    @NotEmpty(message = "templateName not empty")
    private String templateName;

    @NotEmpty(message = "templateData not empty")
    private String templateData;

    @NotEmpty(message = "billType not empty")
    private String billType;

//    @NotEmpty(message = "userId not empty")
    private String userId;

    private String info;

//    @NotEmpty(message = "orgId not empty")
    private String orgId;

}
