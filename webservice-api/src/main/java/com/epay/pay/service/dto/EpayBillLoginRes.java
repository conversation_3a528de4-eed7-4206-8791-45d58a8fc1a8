package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class EpayBillLoginRes {

    private Integer id;
    private String tempToken;
    private String orderNum;
    private String orderName;
    private String merCode;
    private String billType;
    private String paymentType;
    private String itemName;
    private String amount;
    private Integer commission;
    private Integer totalAmount;
    private String currency;
    private String imageUrl;
    private String paymentEmail;
    private String emailTitle;
    private String emailMessage;
    private String expiryTimestamp;

}
