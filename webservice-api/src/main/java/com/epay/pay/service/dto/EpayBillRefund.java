package com.epay.pay.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Getter
@Setter
public class EpayBillRefund {

    @NotEmpty(message = "outTradeNo not empty")
    private String outTradeNo;

    @NotEmpty(message = "amount not empty")
    private BigDecimal amount;

    @NotEmpty(message = "currency not empty")
    private String currency;

    private String password;

    private String type;

    private String frontEndPageUrl;

}
