package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name="epay_refund")
@Where(clause = "valid = 1")
public class EpayRefund extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String orderNum;
    private String orgId;
    private String userId;
    private String outTradeNo;
    private String transactionId;
    private String refundNum;
    private String merCode;
    private String billType;
    private String paymentType;
    private String itemName;
    private Integer amount;
    private String currency;
    private String cardNum;
    private Integer status;


}
