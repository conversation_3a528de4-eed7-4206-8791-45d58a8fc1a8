package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name="epay_bill")
@Where(clause = "valid = 1")
public class EpayBill extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String orderNum;
    private String orgId;
    private String userId;
    private String username;
    private String orderName;
    private String outTradeNo = "2";
    private String insCode;
    private String merCode;
    private String billType = "one-off";
    private String paymentType;
    private String itemName;
    private Integer amount;
    private Integer commission;
    private Integer totalAmount;
    private String currency;
    private String imageUrl;
    private Long expiryTimestamp;
    private String paymentEmail;
    private String emailTitle;
    private String emailMessage;
    private String notifyUrl;
    private String refundNum;
    private String voidNum;
    private String payLink;
    private Integer isFavourite = 0;
    /**
     * 0待创建 1待支付 2已支付 3已退款 4失败 5超时
     */
    private Integer status = 0;
    private String mode = "unlock";


}
