package com.epay.pay.domain;

import lombok.Data;

@Data
public class EpayBillCallbackReq {

    /**
     * 微信或支付宝的订单号
     */
    private String transaction_id;

    /**
     * 通知类型
     */
    private String notify_type;

    /**
     * 商户号
     */
    private String sub_mch_id;

    /**
     * 设备终端号
     */
    private String device_info;

    /**
     * 交易订单号
     */
    private String out_trade_no;

    /**
     * 交易返回状态
     */
    private String trade_status;

    /**
     * 消费总金额
     */
    private String total_fee;

    /**
     * 签名
     */
    private String sign;

    /**
     * 交易币种
     */
    private String currency;

    /**
     * 机构号
     */
    private String mch_id;

    /**
     * 通知类型
     */
    private String gmt_payment;

    /**
     * 通知类型
     */
    private String result_code;

    /**
     * 通知类型
     */
    private String return_code;
}
