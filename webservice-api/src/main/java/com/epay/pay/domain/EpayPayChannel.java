package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "epay_pay_channel")
@Where(clause = "valid = 1")
public class EpayPayChannel extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String orderNum;
    private String merCode;


}
