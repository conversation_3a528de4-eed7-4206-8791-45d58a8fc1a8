package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name="epay_transaction")
@Where(clause = "valid = 1")
public class EpayTransaction extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String orderNum;
    private String orgId;
    private String userId;
    private String outTradeNo;
    private String refundNum;
    private String transactionId;
    private String insCode;
    private String merCode;
    private String billType;
    private String paymentType;
    private String itemName;
    private Integer amount;
    private Integer commission;
    private Integer totalAmount;
    private String currency;
    private String cardNum;
    private String traceType;
    /**
     * 0 Proccessing 1 Success 2 Closed
     */
    private Integer status;
    private String customerName;
    private String customerPhone;
    private String customerAddress;
    private String customerEmail;

}
