package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name="epay_tag")
@Where(clause = "valid = 1")
public class EpayTag extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String tagId;
    private String tagName;
    private String display;
    private String color;
    private String userId;
    private String outTradeNo;
    private Integer status = 0;
    private String orgId;

}
