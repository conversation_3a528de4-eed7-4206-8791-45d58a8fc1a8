package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name="bill_void")
@Where(clause = "valid = 1")
public class EpayVoid extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String orderNum;
    private String orgId;
    private String userId;
    private String outTradeNo;
    private String transactionId;
    private String voidNum;
    private String merCode;
    private String billType;
    private String paymentType;
    private String itemName;
    private String amount;
    private String currency;
    private String cardNum;
    private String status;


}
