package com.epay.pay.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name="epay_template")
@Where(clause = "valid = 1")
public class EpayTemplate extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String orgId;
    private String templateId;
    private String templateName;
    private String templateData;
    private String billType;
    private String userId;
    private Integer amount;
    private String info;

}
