package com.epay.config.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class DataServerProperties {

    /**
     * server url
     */
    private String url;

    /**
     * file url
     */
    private String fileUrl;

    /**
     * transaction url
     */
    private String transactionUrl;

    /**
     * user url
     */
    private String userUrl;

    /**
     * web url
     */
    private String webUrl;

    /**
     * refund url
     */
    private String refundUrl;

    /**
     * pay url
     */
    private String payUrl;

    /**
     * pay url
     */
    private String onlinePayUrl;

    /**
     * email-jump-url
     */
    private String emailJumpUrl;

    /**
     * opp url
     */
    private String oppUrl;
}
