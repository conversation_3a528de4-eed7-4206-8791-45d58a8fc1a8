package com.epay.config;

import com.epay.config.bean.DataServerProperties;
import com.epay.config.bean.PayUrlProperties;
import com.epay.security.bean.LoginProperties;
import com.epay.security.bean.SecurityProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @apiNote 配置文件转换Pojo类的 统一配置 类
 * @date 2024-04-14
 */
@Configuration
public class ConfigBeanConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "login")
    public LoginProperties loginProperties() {
        return new LoginProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "jwt")
    public SecurityProperties securityProperties() {
        return new SecurityProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "data-server")
    public DataServerProperties dataServerProperties() {
        return new DataServerProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "pay-url")
    public PayUrlProperties payUrlProperties() {
        return new PayUrlProperties();
    }

}
