package com.epay.config.utils;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis延迟队列工具 - 基于Spring Data Redis实现
 */
@Slf4j
@Component
@AllArgsConstructor
public class RedisDelayQueueUtil {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 添加延迟队列
     * @param value 队列值
     * @param delay 延迟时间
     * @param timeUnit 时间单位
     * @param queueCode 队列键
     * @param <T>
     */
    public <T> void addDelayQueue(T value, long delay, TimeUnit timeUnit, String queueCode){
        try {
            // 计算执行时间戳（毫秒）
            long executeTime = System.currentTimeMillis() + timeUnit.toMillis(delay);

            // 将数据序列化为JSON字符串
            String jsonValue = JSON.toJSONString(value);

            // 使用Redis Sorted Set存储延迟任务，score为执行时间戳
            redisTemplate.opsForZSet().add(queueCode, jsonValue, executeTime);

            log.info("(添加延时队列成功) 队列键：{}，队列值：{}，延迟时间：{}", queueCode, value, timeUnit.toSeconds(delay) + "秒");
        } catch (Exception e) {
            log.error("(添加延时队列失败) {}", e.getMessage());
            throw new RuntimeException("(添加延时队列失败)");
        }
    }

    /**
     * 获取延迟队列中到期的任务
     * @param queueCode 队列键
     * @param <T>
     * @return 到期的任务，如果没有到期任务返回null
     */
    public <T> T getDelayQueue(String queueCode) {
        try {
            long currentTime = System.currentTimeMillis();

            // 获取到期的任务（score <= 当前时间戳）
            Set<Object> expiredTasks = redisTemplate.opsForZSet().rangeByScore(queueCode, 0, currentTime, 0, 1);

            if (expiredTasks != null && !expiredTasks.isEmpty()) {
                Object task = expiredTasks.iterator().next();

                // 从队列中移除已获取的任务
                redisTemplate.opsForZSet().remove(queueCode, task);

                // 反序列化JSON字符串为Map类型（因为大部分延迟队列任务都是Map）
                return (T) JSON.parseObject(task.toString(), Map.class);
            }

            return null;
        } catch (Exception e) {
            log.error("(获取延时队列失败) {}", e.getMessage());
            return null;
        }
    }
}
