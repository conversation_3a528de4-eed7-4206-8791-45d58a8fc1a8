package com.epay.config;

import cn.hutool.extra.spring.SpringUtil;
import com.epay.config.enums.RedisDelayQueueEnum;
import com.epay.config.utils.RedisDelayQueueUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 启动延迟队列
 */
@Slf4j
@Component
@AllArgsConstructor
public class RedisDelayQueueRunner implements CommandLineRunner {

    private final RedisDelayQueueUtil redisDelayQueueUtil;

    @Override
    public void run(String... args) {
        new Thread(() -> {
            while (true) {
                try {
                    RedisDelayQueueEnum[] queueEnums = RedisDelayQueueEnum.values();
                    for (RedisDelayQueueEnum queueEnum : queueEnums) {
                        Object value = redisDelayQueueUtil.getDelayQueue(queueEnum.getCode());
                        if (value != null) {
                            RedisDelayQueueHandle<Object> redisDelayQueueHandle = SpringUtil.getBean(queueEnum.getBeanId());
                            redisDelayQueueHandle.execute(value);
                        }
                    }

                    // 添加短暂休眠，避免CPU占用过高
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error("(Redis延迟队列异常) {}", e.getMessage());
                    try {
                        Thread.sleep(5000); // 出错时等待5秒再重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }).start();
        log.info("(Redis延迟队列启动成功)");
    }
}
