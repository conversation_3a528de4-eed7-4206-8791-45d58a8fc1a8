package com.epay.config;

import com.epay.utils.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration; /**
 * Redisson配置
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String host;
    @Value("${spring.redis.port}")
    private int port;
    @Value("${spring.redis.database}")
    private int database;
    @Value("${spring.redis.password}")
    private String password;
    @Value("${spring.redis.ssl:false}")
    private boolean ssl;

    @Bean
    public RedissonClient redissonClient() {
        System.out.println("init redisson");
        String protocol = ssl ? "rediss://" : "redis://";
        String address = protocol + host + ":" + port;
        System.out.println("Redis address: " + address + " database: " + database + " ssl: " + ssl);
        
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer()
                .setAddress(address)
                .setDatabase(database);
        
        if (ssl) {
            // 禁用SSL端点标识验证以支持AWS ElastiCache Serverless
            singleServerConfig.setSslEnableEndpointIdentification(false);
        }
        
        if (StringUtils.isNotEmpty(password)) {
            singleServerConfig.setPassword(password);
        }
        return Redisson.create(config);
    }

}
