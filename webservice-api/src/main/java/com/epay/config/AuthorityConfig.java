package com.epay.config;

import com.epay.exception.CustomException;
import com.epay.base.login.JwtUserDto;
import com.epay.base.login.UcPermissionDto;
import com.epay.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service(value = "el")
public class AuthorityConfig {

    public Boolean check(String... permissions) {
        JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
        // 返回 token 与 用户信息
        if (currentUser.getUserLoginDto().getType().equals("PrimaryUser")) {
            return true;
        }
        // 判断当前用户的所有权限是否包含接口上定义的权限
        boolean flag = Arrays.stream(permissions).anyMatch(currentUser.getPermissions()::contains);
        if (!flag) {
            throw new CustomException(HttpStatus.FORBIDDEN.value(),"not operation permission！");
        }
        return true;
    }
}
