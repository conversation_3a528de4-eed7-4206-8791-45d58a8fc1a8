package com.epay.management.utils;

import com.epay.management.enums.TransactionPaymentMethodEnum;

public class SupportFuncUtil {

    /**
     * 确保支持功能标志是6位，不足则在后面补0
     * @param supportFuncFlag 支持功能标志
     * @return 补全后的6位标志
     */
    public static String ensureSixDigits(String supportFuncFlag) {
        if (supportFuncFlag == null) {
            return "000000";
        }
        supportFuncFlag = supportFuncFlag.trim();
        StringBuilder sb = new StringBuilder(supportFuncFlag);
        while (sb.length() < 6) {
            sb.append("0");
        }
        return sb.toString();
    }

    public static boolean isRefund(String supportFuncFlag, String paymentMethod) {
        if (TransactionPaymentMethodEnum.UNION_PAY.getName().equals(paymentMethod)) {
            supportFuncFlag = ensureSixDigits(supportFuncFlag);
            return '1' == supportFuncFlag.charAt(2);
        }
        supportFuncFlag = ensureSixDigits(supportFuncFlag);
        return '1' == supportFuncFlag.charAt(4);
    }

    public static boolean isVoid(String supportFuncFlag) {
        supportFuncFlag = ensureSixDigits(supportFuncFlag);
        return '1' == supportFuncFlag.charAt(1);
    }

}
