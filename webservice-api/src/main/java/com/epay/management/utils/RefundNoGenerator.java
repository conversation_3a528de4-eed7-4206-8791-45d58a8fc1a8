package com.epay.management.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2024/4/20
 */
@Slf4j
@Component
public class RefundNoGenerator {

    // 获取当前时间戳到秒
    private static final String timestamp = DateTimeFormatter.ofPattern("yyMMddHHmmss").format(LocalDateTime.now());

    private static final String KEY = "refund_order_no";


    @Resource
    private RedisTemplate<Object, Object> redisTemplate;


    public RefundNoGenerator() {
    }

    /**
     * 初始化ID的起始值，如果尚未设置
     */
    @PostConstruct
    private void initializeId() {
        // 使用RedisTemplate检查是否已经有ID设置
        if (Boolean.FALSE.equals(redisTemplate.hasKey(KEY))) {
            // 设置起始ID为10000
            redisTemplate.opsForValue().increment(KEY, 1000);
        }
    }

    /**
     * 生成按顺序的ID，从1000开始，满足后自动增加位数
     * @return 唯一ID
     */
    public String generateUniqueRefundNo() {
        // 自动递增ID
        Long num = redisTemplate.opsForValue().increment(KEY, 1);
        if (num != null && num > 9999) {
            // 超过9999后重新开始
            redisTemplate.opsForValue().increment(KEY, 1000);
            return timestamp + "1000";
        }
        return timestamp + num;
    }
}
