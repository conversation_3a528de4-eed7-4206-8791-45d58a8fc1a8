package com.epay.management.utils;

import com.epay.management.enums.TransactionPaymentMethodEnum;
import com.epay.utils.StringUtils;
import java.util.List;
import java.util.ArrayList;

public class TransactionUtil {

    public static String getRefund(String paymentMethod) {

        if (TransactionPaymentMethodEnum.ALIPAY.getName().equals(paymentMethod)) {
            return TransactionPaymentMethodEnum.ALIPAY_REFUND.getName();
        }

        if (TransactionPaymentMethodEnum.WECHAT.getName().equals(paymentMethod)) {
            return TransactionPaymentMethodEnum.WECHAT_REFUND.getName();
        }

        if (TransactionPaymentMethodEnum.UNION_PAY.getName().equals(paymentMethod)) {
            return TransactionPaymentMethodEnum.UNION_PAY_REFUND.getName();
        }

        return null;
    }

    public static String getTradeType(String tradeType, String paymentMethod,String transactionType) {

        if (StringUtils.isEmpty(tradeType) || StringUtils.isEmpty(paymentMethod)) {
            return null;
        }

        if ("APP".equals(transactionType) || "Online".equals(transactionType) || "PBL".equals(transactionType)) {

            if (TransactionPaymentMethodEnum.ALIPAY.getName().equals(paymentMethod)) {
                switch (tradeType) {
                    case "create_forex_trade":
                    case "create_forex_trade_wap":
                    case "mobile.securitypay.pay":
                    case "alipay.acquire.overseas.spot.pay":
                    case "alipay.acquire.precreate":
                        return "Sale";
                    case "alipay.acquire.cancel":
                        return "Void";
                    case "alipay.acquire.overseas.spot.refund":
                    case "forex_refund":
                        return "Refund";
                    case "TSQ":
                        return "Transaction Query";
                }
            }

            if (TransactionPaymentMethodEnum.WECHAT.getName().equals(paymentMethod)) {
                switch (tradeType) {
                    case "PRN":
                    case "PRM":
                    case "PRJ":
                        return "Sale";
                    case "PVR":
                        return "Void";
                    case "CTH":
                        return "Refund";
                    case "TSQ":
                        return "Transaction Query";
                }
            }

            if (TransactionPaymentMethodEnum.UNION_PAY.getName().equals(paymentMethod)) {
                return getUniPayTransactionType(tradeType);
            }

        }

        if ("POS".equals(transactionType)) {
            return getUniPayTransactionType(tradeType);
        }

        return null;
    }

    public static String getUniPayTransactionType(String tradeType) {
        switch (tradeType) {
            case "PER":
            case "RER":
            case "PEC":
                return "Sale";
            case "PVR":
            case "PVC":
                return "Void";
            case "CTH":
                return "Refund";
            case "PPA":
                return "Pre-Auth";
            case "PPC":
                return "Reversal";
            case "PNP":
                return "Pre-Auth Cancel";
            case "PMC":
                return "Reversal of Pre-Auth Cancel";
            case "CJT":
                return "Pre-Auth Completion Offline";
            case "ACR":
                return "Pre-Auth Completion";
            case "PCC":
                return "Reversal of Pre-Auth Completion";
            case "PCR":
                return "Pre-Auth Completion Void";
            case "PRC":
                return "Reversal of Pre-Auth Completion Void";
            case "SAT":
                return "Tips Adjustment";
            case "CPS":
                return "Coupon Sale";
            case "CPR":
                return "Coupon Reversal";
            case "ECR":
                return "Credit Adjustment";
            case "PRS":
                return "Presentment";
            case "CBK":
                return "Chargeback";
            case "RPS":
                return "Re-Presentment";
            case "RCB":
                return "Second Chargeback";
            case "GFT":
                return "GoodFaith";
            case "TSQ":
                return "Transaction Query";
        }
        return null;
    }

    /**
     * 反解析交易类型 - 根据getTradeType方法的返回结果获取所有可能的原始tradeType
     * @param tradeTypeResult getTradeType方法的返回结果
     * @param transactionType 交易类型
     * @return 所有可能的原始tradeType值列表
     */
    public static List<String> parseTradeType(String tradeTypeResult, String transactionType) {

        List<String> result = new ArrayList<>();

        if (StringUtils.isEmpty(tradeTypeResult)) {
            return result;
        }

        if ("APP".equals(transactionType) || "Online".equals(transactionType) || "PBL".equals(transactionType)) {

            // 支付宝相关的tradeType
            switch (tradeTypeResult) {
                case "Sale":
                    result.add("create_forex_trade");
                    result.add("create_forex_trade_wap");
                    result.add("mobile.securitypay.pay");
                    result.add("alipay.acquire.overseas.spot.pay");
                    result.add("alipay.acquire.precreate");
                    break;
                case "Void":
                    result.add("alipay.acquire.cancel");
                    break;
                case "Refund":
                    result.add("alipay.acquire.overseas.spot.refund");
                    result.add("forex_refund");
                    break;
                case "Transaction Query":
                    result.add("TSQ");
                    break;
            }

            // 微信相关的tradeType
            switch (tradeTypeResult) {
                case "Sale":
                    if (!result.contains("PRN")) result.add("PRN");
                    if (!result.contains("PRM")) result.add("PRM");
                    if (!result.contains("PRJ")) result.add("PRJ");
                    break;
                case "Void":
                    if (!result.contains("PVR")) result.add("PVR");
                    break;
                case "Refund":
                    if (!result.contains("CTH")) result.add("CTH");
                    break;
                case "Transaction Query":
                    if (!result.contains("TSQ")) result.add("TSQ");
                    break;
            }

            // 银联相关的tradeType
            List<String> uniPayResults = parseUniPayTradeTypeList(tradeTypeResult);
            for (String uniPayResult : uniPayResults) {
                if (!result.contains(uniPayResult)) {
                    result.add(uniPayResult);
                }
            }
        }

        if ("POS".equals(transactionType)) {
            List<String> uniPayResults = parseUniPayTradeTypeList(tradeTypeResult);
            for (String uniPayResult : uniPayResults) {
                if (!result.contains(uniPayResult)) {
                    result.add(uniPayResult);
                }
            }
        }

        return result;
    }

    /**
     * 反解析银联交易类型 - 返回列表
     * @param tradeTypeResult 交易类型结果
     * @return 所有可能的原始tradeType值列表
     */
    public static List<String> parseUniPayTradeTypeList(String tradeTypeResult) {
        List<String> result = new ArrayList<>();

        switch (tradeTypeResult) {
            case "Sale":
                result.add("PER");
                result.add("RER");
                result.add("PEC");
                break;
            case "Void":
                result.add("PVR");
                result.add("PVC");
                break;
            case "Refund":
                result.add("CTH");
                break;
            case "Pre-Auth":
                result.add("PPA");
                break;
            case "Reversal":
                result.add("PPC");
                break;
            case "Pre-Auth Cancel":
                result.add("PNP");
                break;
            case "Reversal of Pre-Auth Cancel":
                result.add("PMC");
                break;
            case "Pre-Auth Completion Offline":
                result.add("CJT");
                break;
            case "Pre-Auth Completion":
                result.add("ACR");
                break;
            case "Reversal of Pre-Auth Completion":
                result.add("PCC");
                break;
            case "Pre-Auth Completion Void":
                result.add("PCR");
                break;
            case "Reversal of Pre-Auth Completion Void":
                result.add("PRC");
                break;
            case "Tips Adjustment":
                result.add("SAT");
                break;
            case "Coupon Sale":
                result.add("CPS");
                break;
            case "Coupon Reversal":
                result.add("CPR");
                break;
            case "Credit Adjustment":
                result.add("ECR");
                break;
            case "Presentment":
                result.add("PRS");
                break;
            case "Chargeback":
                result.add("CBK");
                break;
            case "Re-Presentment":
                result.add("RPS");
                break;
            case "Second Chargeback":
                result.add("RCB");
                break;
            case "GoodFaith":
                result.add("GFT");
                break;
            case "Transaction Query":
                result.add("TSQ");
                break;
        }
        return result;
    }

    /**
     * 反解析银联交易类型 - 返回第一个匹配值（保持向后兼容）
     * @param tradeTypeResult 交易类型结果
     * @return 原始的tradeType值，如果有多个可能值则返回第一个匹配的
     */
    public static String parseUniPayTradeType(String tradeTypeResult) {
        List<String> results = parseUniPayTradeTypeList(tradeTypeResult);
        return results.isEmpty() ? null : results.get(0);
    }
}
