package com.epay.management.utils;

import cn.hutool.crypto.SecureUtil;

import java.util.Map;

public class SignatureGenerator {

    public static String generateSignature(Map<String, Object> params, String sign) {

        // 拼接 QueryString 格式的字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (!"sign".equals(entry.getKey()) && entry.getValue() != null) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }

        // 移除最后的 "&"
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        // 拼接签名原始串
        sb.append(sign);

        return SecureUtil.md5(sb.toString());

    }

}
