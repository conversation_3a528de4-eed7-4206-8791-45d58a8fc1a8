package com.epay.management.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/5/31
 */
public enum GatewayEnum {

    UNION("1", "unionPay"),
    WECHAT("8", "wechatPay"),
    ALIPAY("10", "aliPay"),
    PROMTPAY("13", "promtPay"),
    ;

    @Getter
    private final String code;
    private final String name;

    GatewayEnum(String code, String desc) {
        this.code = code;
        this.name = desc;
    }

    public String getDesc() {
        return name;
    }

    /**
     * 根据code 获取name
     *
     * @param code
     * @return
     */
    public static String getDescByName(String code) {
        for (GatewayEnum gatewayEnum : GatewayEnum.values()) {
            if (gatewayEnum.getCode().equals(code)) {
                return gatewayEnum.getDesc();
            }
        }
        return null;
    }

}
