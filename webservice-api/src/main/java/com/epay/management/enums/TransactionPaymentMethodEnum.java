package com.epay.management.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransactionPaymentMethodEnum {

         WECHAT("wechatPay"),

         WECHAT_REFUND("unified.weixin.refund"),
         WECHAT_NATIVE("unified.weixin.native"),

         ALIPAY("aliPay"),

         ALIPAY_REFUND("unified.alipay.refund"),
         ALIPAY_NATIVE("unified.alipay.native"),

        UNION_PAY("unionPay"),

        UNION_PAY_REFUND("unified.upop.refund"),
        UNION_PAY_NATIVE("unified.upop.native"),
        ;

        private final String name;

}
