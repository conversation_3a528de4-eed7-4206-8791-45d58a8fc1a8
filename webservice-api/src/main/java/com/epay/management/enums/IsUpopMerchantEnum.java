package com.epay.management.enums;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/31
 */
public enum IsUpopMerchantEnum {

    POS("0", "POS"),
    NPP("8", "NPP"),
    Online("1,5,6", "Online"),
    APP("9", "APP"),
    PBL("2,5,6", "PBL"),
    ;

    private final String code;
    private final String desc;

    IsUpopMerchantEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code 获取desc
     *
     * @param code
     * @return
     */
    public static String getDescByCode(String code) {
        for (IsUpopMerchantEnum isUpopMerchantEnum : IsUpopMerchantEnum.values()) {
            if (Arrays.stream(isUpopMerchantEnum.getCode().split(",")).collect(Collectors.toList()).contains(code)) {
                return isUpopMerchantEnum.getDesc();
            }
        }
        return "";
    }

}
