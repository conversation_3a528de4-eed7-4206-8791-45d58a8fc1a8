package com.epay.management.enums;

/**
 * <AUTHOR>
 * @date 2024/5/31
 */
public enum AcquireTypeEnum {

    UPOP("UPOP", "upop"),
    WECHATPAY("WeChatPay", "wechat"),
    ALIPAYONLINE("AlipayOnline", "Alipay"),
    ALIPAY3RD("<PERSON><PERSON><PERSON> (3rd)", "Ali<PERSON>y"),
    ;

    private final String code;
    private final String desc;

    AcquireTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code 获取desc
     *
     * @param code
     * @return
     */
    public static String getDescByCode(String code) {
        for (AcquireTypeEnum isUpopMerchantEnum : AcquireTypeEnum.values()) {
            if (isUpopMerchantEnum.getCode().equals(code)) {
                return isUpopMerchantEnum.getDesc();
            }
        }
        return null;
    }

}
