package com.epay.management.rest;

import com.epay.config.enums.RedisDelayQueueEnum;
import com.epay.config.utils.RedisDelayQueueUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 延迟队列测试
 * Created by LPB on 2020/04/20.
 */
@RestController
@AllArgsConstructor
public class RedisDelayQueueController {

    private final RedisDelayQueueUtil redisDelayQueueUtil;

    @PostMapping("/addQueue")
    public void addQueue(@RequestBody Map<String,String> map) {
        Map<String, String> map1 = new HashMap<>();
        map1.put("orderId", map.get("orderId"));
        map1.put("remark", "订单支付超时，自动取消订单");

        // 添加订单支付超时，自动取消订单延迟队列。为了测试效果，延迟10秒钟
        redisDelayQueueUtil.addDelayQueue(map1, Integer.parseInt(map.get("time")), TimeUnit.SECONDS, RedisDelayQueueEnum.BILL_PAYMENT_TIMEOUT.getCode());

    }

}
