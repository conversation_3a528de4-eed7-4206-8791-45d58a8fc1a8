package com.epay.management.rest;

import com.alibaba.fastjson.JSONObject;
import com.epay.logging.annotation.Log;
import com.epay.management.service.MerchantService;
import com.epay.management.service.dto.*;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * merchant
 */
@RestController
@RequiredArgsConstructor
@RequestMapping()
public class MerchantController {

    private final MerchantService merchantService;

    /**
     * query merchant
     *
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/merchant")
    public ResponseEntity<RestResult> merchant(MerchantCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(merchantService.merchant(criteria, pageable)), HttpStatus.OK);
    }

    /**
     * query merchant
     *
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/merchantList")
    public ResponseEntity<RestResult> merchantList(MerchantListCriteria criteria, Pageable pageable) {
        criteria.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
        return new ResponseEntity<>(RestUtil.ok(merchantService.merchantList(criteria, pageable)), HttpStatus.OK);
    }

    /**
     * query paymentType
     *
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/paymentType")
    public ResponseEntity<RestResult> paymentType(PaymentTypeCriteria criteria) {
        return new ResponseEntity<>(RestUtil.ok(merchantService.paymentType(criteria)), HttpStatus.OK);
    }

    /**
     * query paymentType
     *
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/paymentTypeTemporary")
    public ResponseEntity<RestResult> paymentTypeTemporary(PaymentTypeCriteria criteria) {
        return new ResponseEntity<>(RestUtil.ok(merchantService.paymentTypeTemporary(criteria)), HttpStatus.OK);
    }

    /**
     * query merchant detail
     *
     * @param id
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/merchant/detail/{id}")
    public ResponseEntity<RestResult> merchantDetail(@PathVariable("id") String id) {
        return new ResponseEntity<>(RestUtil.ok(merchantService.merchantDetail(id)), HttpStatus.OK);
    }

    /**
     * query ins
     *
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/ins")
    public ResponseEntity<RestResult> ins(InsCriteria insCriteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(merchantService.ins(insCriteria, pageable)), HttpStatus.OK);
    }

    /**
     * query terminal
     *
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/terminal")
    public ResponseEntity<RestResult> terminal(TerminalCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(merchantService.terminal(criteria, pageable)), HttpStatus.OK);
    }

}
