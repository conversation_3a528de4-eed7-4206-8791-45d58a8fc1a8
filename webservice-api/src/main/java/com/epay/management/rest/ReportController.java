package com.epay.management.rest;

import com.epay.annotation.AnonymousAccess;
import com.epay.logging.annotation.Log;
import com.epay.management.domain.ReportList;
import com.epay.management.service.ReportService;
import com.epay.management.service.dto.ReportCriteria;
import com.epay.management.service.dto.ReportFile;
import com.epay.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequiredArgsConstructor
@RequestMapping("/report")
public class ReportController {

    private final ReportService reportService;


    /**
     * 报表数据浏览
     *
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/data")
    @PreAuthorize("@el.check('A500010001')")
    public ResponseEntity<RestResult> data(ReportCriteria criteria, Pageable pageable) {
        try {
            String[] queryArr = new String[]{"insCode", "fileName"};
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                for (String query : queryArr) {
                    ReportCriteria keyCriteria = ReflectionUtil.setPropertyValue(ReportCriteria.class, query, criteria.getSearchKeyword());
                    Map<String, Object> result = reportService.data(keyCriteria, pageable);
                    PageResult<ReportList> page = (PageResult<ReportList>) result.get("page");
                    if (page.getTotal() > 0) {
                        return new ResponseEntity<>(RestUtil.ok(result), HttpStatus.OK);
                    }
                }
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("page", PageUtil.noData());
                Map<String, Integer> countMap = new HashMap<>();
                // 当搜索无结果时，返回空的countMap（不包含任何支付方式）
                emptyResult.put("countMap", countMap);
                return new ResponseEntity<>(RestUtil.ok(emptyResult), HttpStatus.OK);
            }
            return new ResponseEntity<>(RestUtil.ok(reportService.data(criteria, pageable)), HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 报表数据浏览
     *
     * @param file
     * @return
     */
    @Log(isSaveResponseData = false)
    @PreAuthorize("@el.check('A500010001')")
    @GetMapping("/file")
    public ResponseEntity<StreamingResponseBody> file(ReportFile file) {
        return reportService.file(file);
    }

    /**
     * 报表数据浏览
     *
     * @param file
     * @return
     */
    @Log(isSaveResponseData = false)
    @PreAuthorize("@el.check('A500010001')")
    @AnonymousAccess
    @GetMapping("/preview")
    public void preview(HttpServletResponse response, ReportFile file) {
        reportService.preview(response,file);
    }

}
