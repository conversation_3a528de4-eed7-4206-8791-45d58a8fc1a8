package com.epay.management.rest;

import cn.hutool.core.io.FileUtil;
import com.epay.config.FileProperties;
import com.epay.management.domain.FileInfo;
import com.epay.management.service.ReportService;
import com.epay.management.service.dto.ReportCriteria;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/file")
public class FileController {

    private final String fileSavePath = FileUtil.getTmpDirPath() + "/";
    private final FileProperties fileProperties;


    /**
     * 数据上传
     *
     * @param fileInfo
     * @return
     */
    @PostMapping("/upload")
    public ResponseEntity<RestResult> upload(FileInfo fileInfo) throws IOException {
        MultipartFile file = fileInfo.getMultipartFile();
        String fileName = "";
        if (fileInfo.getMultipartFile() != null) {
            fileName = file.getOriginalFilename();
            // 文件重命名
            fileName = System.currentTimeMillis() + fileName.substring(fileName.lastIndexOf("."));
            // 判断文件存储路径是否存在
            File fileDir = new File(fileProperties.getPath() + fileInfo.getType());
            if (!fileDir.exists()) {
                FileUtil.mkdir(fileDir);
            }
            File deployFile = new File(fileProperties.getPath() + fileInfo.getType() + "/" + fileName);
            file.transferTo(deployFile);
        } else {
            log.error("没有找到相对应的文件");
            return ResponseEntity.ok(RestUtil.toRest(RestEnum.FILE_NOT_FIND));
        }
        log.info("文件上传成功，原文件名：{},新文件名{}", Objects.requireNonNull(file).getOriginalFilename(), fileName);
        Map<String, Object> map = new HashMap<>(2);
        map.put("url", fileProperties.getBaseUrl() + "file/" + fileInfo.getType() + "/" + fileName);
        return ResponseEntity.ok(RestUtil.ok(map));
    }


}
