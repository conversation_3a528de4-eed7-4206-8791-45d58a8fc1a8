package com.epay.management.rest;

import com.epay.logging.annotation.Log;
import com.epay.management.service.MpsRoleService;
import com.epay.management.service.dto.*;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.enums.RestEnum;
import com.epay.validated.AddVerify;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 角色
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping()
public class MpsRoleController {

    private final MpsRoleService roleService;

    /**
     * 查询用户角色的权限信息
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/permission")
    @PreAuthorize("@el.check('admin')")
    public ResponseEntity<RestResult> query(MpsRoleCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(roleService.queryAll(criteria, pageable)), HttpStatus.OK);
    }

    /**
     * 修改用户角色的权限信息
     *
     * @param update
     * @param id
     * @return
     */
    @Log
    @PutMapping("/role/permission/{id}")
    public ResponseEntity<RestResult> update(@RequestBody @Validated MpsRolePermissionAddUpdate update, @PathVariable Integer id) {
        return new ResponseEntity<>(roleService.update(update, id), HttpStatus.OK);
    }

    /**
     * 创建角色
     *
     * @param create
     * @return
     */
    @Log
    @PostMapping("/role")
    public ResponseEntity<RestResult> create(@RequestBody @Validated(AddVerify.class) MpsRoleAddUpdate create) {
        return new ResponseEntity<>(roleService.create(create), HttpStatus.OK);
    }

    /**
     * 删除角色
     *
     * @return
     */
    @Log
    @DeleteMapping("/role/{id}")
    public ResponseEntity<RestResult> delete(@PathVariable Integer id) {
        roleService.delete(id);
        return new ResponseEntity<>(RestUtil.toRest(RestEnum.SUCCESS), HttpStatus.OK);
    }

}
