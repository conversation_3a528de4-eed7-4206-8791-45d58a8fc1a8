package com.epay.management.rest;

import com.epay.logging.annotation.Log;
import com.epay.management.domain.TransactionList;
import com.epay.management.service.TransactionService;
import com.epay.management.service.dto.RefundReq;
import com.epay.management.service.dto.TransactionCriteria;
import com.epay.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 交易数据管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/transaction")
public class TransactionController {

    private final TransactionService transactionService;

    /**
     * realtime
     *
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/realtime")
    public ResponseEntity<RestResult> realtime(@Validated TransactionCriteria criteria, Pageable pageable) {
        try {
            String[] queryArr = new String[]{"insCode", "merCode", "cardNo", "orderNo", "referenceNo"};
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                if (DateUtil.isValidDate(criteria.getSearchKeyword())) {
                    PageResult<TransactionList> searchKeywordResult = transactionService.realtime(criteria, pageable);
                    if (searchKeywordResult.getTotal() > 0) {
                        return new ResponseEntity<>(RestUtil.ok(searchKeywordResult), HttpStatus.OK);
                    }
                }
                for (String query : queryArr) {
                    TransactionCriteria keyCriteria = ReflectionUtil.setPropertyValue(TransactionCriteria.class, query, criteria.getSearchKeyword());
                    keyCriteria.setType(criteria.getType());
                    PageResult<TransactionList> result = transactionService.realtime(keyCriteria, pageable);
                    if (result.getTotal() > 0) {
                        return new ResponseEntity<>(RestUtil.ok(result), HttpStatus.OK);
                    }
                }
                return new ResponseEntity<>(RestUtil.ok(PageUtil.noData()), HttpStatus.OK);
            }
            return new ResponseEntity<>(RestUtil.ok(transactionService.realtime(criteria, pageable)), HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * refund
     *
     * @param refundReq
     * @return
     */
    @Log
    @PostMapping("/refund")
    public ResponseEntity<RestResult> refund(@RequestBody RefundReq refundReq) {
        return new ResponseEntity<>(transactionService.refund(refundReq), HttpStatus.OK);
    }

}
