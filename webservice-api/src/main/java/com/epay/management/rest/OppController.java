package com.epay.management.rest;

import com.epay.annotation.AnonymousAccess;
import com.epay.logging.annotation.Log;
import com.epay.management.service.OppMerchantService;
import com.epay.management.service.dto.OppMerchantAdd;
import com.epay.management.service.dto.OppMerchantCriteria;
import com.epay.management.service.dto.OppMerchantResetKey;
import com.epay.management.service.dto.OppMerchantUpdate;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * OOP商户
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/opp")
public class OppController {

    @Resource
    private OppMerchantService oppMerchantService;

    /**
     * 查询目前可用的OPP商户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/merchant")
    @PreAuthorize("@el.check('A600010011')")
    public ResponseEntity<RestResult> query(OppMerchantCriteria criteria, Pageable pageable) {
        criteria.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(RestUtil.ok(oppMerchantService.merchant(criteria, pageable)), HttpStatus.OK);
    }

    /**
     * 获取一个的OPP交易机构的密钥
     *
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/privateKey")
    @PreAuthorize("@el.check('A600010012')")
    public ResponseEntity<RestResult> privateKey(@RequestParam("orgId") String orgId) {
        return new ResponseEntity<>(RestUtil.ok(oppMerchantService.privateKey(orgId)), HttpStatus.OK);
    }

    /**
     * 重置一个的OPP交易机构的密钥
     *
     * @return
     */
    @Log(isSaveResponseData = false)
    @PutMapping("/privateKey")
    @PreAuthorize("@el.check('A600010012')")
    public ResponseEntity<RestResult> resetPrivateKey(@RequestBody OppMerchantResetKey merchantResetKey) {
        return new ResponseEntity<>(RestUtil.ok(oppMerchantService.resetPrivateKey(merchantResetKey)), HttpStatus.OK);
    }

    /**
     * 新增可用的OPP商户
     *
     * @param create
     * @return
     */
    @Log(isSaveResponseData = false)
    @PostMapping("/merchant")
    @PreAuthorize("@el.check('A600010011')")
    public ResponseEntity<RestResult> create(@RequestBody @Valid OppMerchantAdd create) {
        create.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(oppMerchantService.create(create), HttpStatus.OK);
    }

    /**
     * 更新可用的OPP商户
     *
     * @param update
     * @return
     */
    @Log(isSaveResponseData = false)
    @PatchMapping("/merchant/{id}")
    @PreAuthorize("@el.check('A600010011')")
    public ResponseEntity<RestResult> update(@RequestBody @Valid OppMerchantUpdate update, @PathVariable("id") Integer id) {
        return new ResponseEntity<>(oppMerchantService.update(update,id), HttpStatus.OK);
    }

    /**
     * 移除可用的OPP商户
     *
     * @param delete
     * @return
     */
    @Log(isSaveResponseData = false)
    @DeleteMapping("/merchant")
    @PreAuthorize("@el.check('A600010011')")
    public ResponseEntity<RestResult> delete(@RequestBody @Valid OppMerchantAdd delete) {
        delete.setOrgId(SecurityUtils.getCurrentLoginUser().getOrgId());
        return new ResponseEntity<>(oppMerchantService.delete(delete), HttpStatus.OK);
    }

    /**
     * 获取Code
     *
     * @return
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/code")
    @PreAuthorize("@el.check('A600010012')")
    public ResponseEntity<RestResult> getCode() {
        return new ResponseEntity<>(RestUtil.ok(oppMerchantService.getCode()), HttpStatus.OK);
    }

    /**
     * 下载技术文件
     *
     * @return
     */
    @AnonymousAccess
    @Log(isSaveResponseData = false)
    @GetMapping("/download")
    @PreAuthorize("@el.check('A600010012')")
    public ResponseEntity<StreamingResponseBody> technicalManualDownload() {
        return oppMerchantService.technicalManualDownload();
    }
}
