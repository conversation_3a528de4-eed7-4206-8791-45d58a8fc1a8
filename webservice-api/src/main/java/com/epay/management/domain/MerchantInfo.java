package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.epay.utils.DateUtil;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Getter
public class MerchantInfo implements Serializable {

    private String merchantName;
    private String mcc;
    private String acquirer;
    private String transactionCurrency;
    private String CUPsettlement;
    private String Currency;
    private String settlementCurrency;
    private String merType;
    private String normalMDR;
    private String QRMDR;
    private String accountName;
    private String accountNo;
    private String bankName;
    private String bankCode;
    private String whatMerchant;
    private String recCreateTm;
    private String recUpdateTm;
    private String merKey;
    private List<TerminalList> terminalList;


    @JSONField(name = "MER_NAME")
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    @JSONField(name = "MER_TYPE")
    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    @JSONField(name = "MER_ACQ_CODE")
    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    @JSONField(name = "MER_TRANS_CURR")
    public void setTransactionCurrency(String transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }

    @JSONField(name = "ACQ_SETT_CURR")
    public void setCUPsettlement(String CUPsettlement) {
        if ("    ".equals(CUPsettlement)) {
            this.CUPsettlement = "Default";
        } if ("156".equals(CUPsettlement)) {
            this.CUPsettlement = "156 - RMB";
        }
    }
    @JSONField(name = "MER_SETT_CURR")
    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    @JSONField(name = "MER_COMMISION_VALUE")
    public void setNormalMDR(String normalMDR) {
        this.normalMDR = normalMDR;
    }

    @JSONField(name = "MER_QR_COMMISSION_VALUE")
    public void setQRMDR(String QRMDR) {
        this.QRMDR = QRMDR;
    }

    @JSONField(name = "MER_SETT_ACCT_NAME")
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    @JSONField(name = "MER_SETT_ACCT")
    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    @JSONField(name = "MER_CB_NAME")
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    @JSONField(name = "MER_CB_CODE")
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    @JSONField(name = "IS_UPOP_MERCHANT")
    public void setWhatMerchant(String whatMerchant) {
        this.whatMerchant = whatMerchant;
        if ("0".equals(whatMerchant)) {
            this.merType = "Offline";
        } else {
            this.merType = "online";
        }
    }

    @JSONField(name = "REC_CREATE_TM")
    public void setRecCreateTm(String recCreateTm) {
        this.recCreateTm = DateUtil.transferMerchantDate(recCreateTm);
    }

    @JSONField(name = "REC_UPDATE_TM")
    public void setRecUpdateTm(String recUpdateTm) {
        this.recUpdateTm = DateUtil.transferMerchantDate(recUpdateTm);
    }

    public void setTerminalList(List<TerminalList> terminalList) {
        this.terminalList = terminalList;
    }
}
