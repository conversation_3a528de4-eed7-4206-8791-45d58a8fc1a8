package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.epay.management.enums.IsUpopMerchantEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Getter
public class MerchantSelfCenterList implements Serializable {


    private String merCode;

    private String merName;

    private String paymentType;

    private String createTime;

    private String currency;

    private String channel;

    private String shortName;


    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "MER_NAME")
    public void setMerName(String merName) {
        this.merName = merName;
    }


    @JSONField(name = "INSPROP")
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @JSONField(name = "REC_CREATE_TM")
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @JSONField(name = "MER_SETT_CURR")
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @JSONField(name = "IS_UPOP_MERCHANT")
    public void setChannel(String channel) {
        this.channel = IsUpopMerchantEnum.getDescByCode(channel);
    }

    @JSONField(name = "MER_SHORT_NAME")
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }
}
