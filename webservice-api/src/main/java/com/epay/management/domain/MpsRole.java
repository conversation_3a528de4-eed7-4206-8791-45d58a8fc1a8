package com.epay.management.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Entity
@Getter
@Setter
@Table(name="mps_role")
@Where(clause = "valid = 1")
public class MpsRole extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String roleName;

    private String roleId;

    private Integer level = 2;

    private String info;
}
