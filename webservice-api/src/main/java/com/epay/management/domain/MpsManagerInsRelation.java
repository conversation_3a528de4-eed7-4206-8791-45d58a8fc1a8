package com.epay.management.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 管理员关联商户
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@Entity
@Getter
@Setter
@Table(name = "mps_manager_ins_relation")
public class MpsManagerInsRelation extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotNull(message = "商户ID不能为空")
    private Integer managerId;

    @NotEmpty(message = "商户代码不能为空")
    private String insCode;

    private String info;

}
