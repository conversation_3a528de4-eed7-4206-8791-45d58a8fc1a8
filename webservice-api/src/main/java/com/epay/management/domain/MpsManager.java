package com.epay.management.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;

/**
 * 管理员
* <AUTHOR>
* @date 2024-04-14
*/
@Entity
@Getter
@Setter
@Table(name="mps_manager")
@Where(clause = "valid = 1")
public class MpsManager extends BaseEntity {

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Integer managerId;

    @NotEmpty(message = "用户名称不能为空")
    private String account;

    @NotEmpty(message = "密码不能为空")
    private String password;

    private String salt;

    @NotEmpty(message = "用户名称不能为空")
    private String name;

    @NotEmpty(message = "邮箱不能为空")
    private String email;

    @NotEmpty(message = "角色名称")
    private String roleName;

    @NotEmpty(message = "角色ID")
    private String roleId;

    private String info;

}
