package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

@Getter
public class ReportList {

    private String insCode;
    private String settDate;
    private String fileType;
    private String fileName;
    private String createTime;
    private String merCode;
    @Setter
    private String merName;
    @Setter
    private String payChannel;
    private String filePath;

    @JSONField(name = "INS_CODE")
    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    @JSONField(name = "SETT_DATE")
    public void setSettDate(String settDate) {
        this.settDate = settDate;
    }

    @JSONField(name = "FILE_TYPE")
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @JSONField(name = "FILE_NAME")
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @JSONField(name = "REC_CREATE_TM")
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "FILE_PATH")
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

}
