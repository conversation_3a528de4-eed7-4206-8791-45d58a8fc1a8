package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.epay.management.enums.IsUpopMerchantEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Getter
public class MerchantList implements Serializable {

    private String merTransCurr;

    private String merCode;

    private String insCode;

    private String merName;

    private String merStatus;

    private String gateway;

    @Setter
    private Integer terminal = 1;

    private String type;

    private String recCreateTm;

    private String supportBillPayFlag;

    private String acquirer;

    private String transactionCurrency;

    private String settlementCurrency;

    private String merType;

    private String shortName;

    @JSONField(name = "MER_TRANS_CURR")
    public void setMerTransCurr(String merTransCurr) {
        this.merTransCurr = merTransCurr;
    }

    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "MER_NAME")
    public void setMerName(String merName) {
        this.merName = merName;
    }

    @JSONField(name = "MER_STATUS")
    public void setMerStatus(String merStatus) {
        this.merStatus = merStatus;
    }

    @JSONField(name = "MER_ACQ_CODE")
    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    @JSONField(name = "INSPROP")
    public void setGateway(String gateway) {
        this.gateway = gateway;
//        if ("0".equals(gateway) || "2".equals(gateway)) {
//            this.type = IsUpopMerchantEnum.getDescByCode(gateway);
//        } else {
//            this.type = "online";
//        }
    }

    @JSONField(name = "REC_CREATE_TM")
    public void setRecCreateTm(String recCreateTm) {
        this.recCreateTm = recCreateTm;
    }


    @JSONField(name = "SUPPORT_BILLPAY_FLAG")
    public void setSupportBillPayFlag(String supportBillPayFlag) {
        this.supportBillPayFlag = supportBillPayFlag;
    }

    @JSONField(name = "MER_SETT_CURR")
    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    @JSONField(name = "MER_TRANS_CURR")
    public void setTransactionCurrency(String transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }

    @JSONField(name = "MER_ACQ_CODE")
    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    @JSONField(name = "IS_UPOP_MERCHANT")
    public void setWhatMerchant(String whatMerchant) {
        this.merType = IsUpopMerchantEnum.getDescByCode(whatMerchant);
    }

    @JSONField(name = "MER_SHORT_NAME")
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }
}
