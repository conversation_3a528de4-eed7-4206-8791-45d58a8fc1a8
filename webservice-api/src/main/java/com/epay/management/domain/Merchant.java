package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.epay.utils.DateUtil;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Getter
public class Merchant implements Serializable {

    private String merTransCurr;
    private String merCode;
    private String merNation;
    private String merKey;
    private String merName;
    private String signMethod;
    private String merType;
    private String merStatus;
    private String merAcqCode;


    @JSONField(name = "MER_TRANS_CURR")
    public void setMerTransCurr(String merTransCurr) {
        this.merTransCurr = merTransCurr;
    }

    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "MER_NATION")
    public void setMerNation(String merNation) {
        this.merNation = merNation;
    }

    @JSONField(name = "MER_KEY")
    public void setMerKey(String merKey) {
        this.merKey = merKey;
    }

    @JSONField(name = "MER_NAME")
    public void setMerName(String merName) {
        this.merName = merName;
    }

    @JSONField(name = "SIGN_METHOD")
    public void setSignMethod(String signMethod) {
        this.signMethod = signMethod;
    }

    @JSONField(name = "MER_TYPE")
    public void setMerType(String merType) {
        this.merType = merType;
    }

    @JSONField(name = "MER_STATUS")
    public void setMerStatus(String merStatus) {
        this.merStatus = merStatus;
    }

    @JSONField(name = "MER_ACQ_CODE")
    public void setMerAcqCode(String merAcqCode) {
        this.merAcqCode = merAcqCode;
    }
}
