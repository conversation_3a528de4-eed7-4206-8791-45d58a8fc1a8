package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * InsInfo
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class InsInfo implements Serializable {

    private String merCommissionType;

    private String merCommissionValue;

    private String merQrCommissionValue;

    private String merCommissionLimit;

    private String merPremiumCommissionValue;

    private String merLinkType;

    private String merCommissionFixedValue;

    @JSONField(name = "MER_COMMISSION_TYPE")
    public void setMerCommissionType(String merCommissionType) {
        this.merCommissionType = merCommissionType;
    }

    @JSONField(name = "MER_COMMISSION_VALUE")

    public void setMerCommissionValue(String merCommissionValue) {
        this.merCommissionValue = merCommissionValue;
    }

    @JSONField(name = "MER_QR_COMMISSION_VALUE")
    public void setMerQrCommissionValue(String merQrCommissionValue) {
        this.merQrCommissionValue = merQrCommissionValue;
    }

    @JSONField(name = "MER_COMMISSION_LIMIT")
    public void setMerCommissionLimit(String merCommissionLimit) {
        this.merCommissionLimit = merCommissionLimit;
    }

    @JSONField(name = "MER_PREMIUM_COMMISSION_VALUE")
    public void setMerPremiumCommissionValue(String merPremiumCommissionValue) {
        this.merPremiumCommissionValue = merPremiumCommissionValue;
    }

    @JSONField(name = "MER_LINK_TYPE")
    public void setMerLinkType(String merLinkType) {
        this.merLinkType = merLinkType;
    }

    @JSONField(name = "MER_COMMISSION_FIXED_VALUE")
    public void setMerCommissionFixedValue(String merCommissionFixedValue) {
        this.merCommissionFixedValue = merCommissionFixedValue;
    }
}
