package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@AllArgsConstructor
public class TransactionList implements Serializable {

    private String insCode;

    private String merCode;

    private String merName;

    private String termCode;

    private String status;

    private BigDecimal amount;

    private String currency;

    private String orderNo;

    private String traceNo;

    private String cardNo;

    private String referenceNo;

    private String tableName;

    private String paymentMethod;

    private String traceType;

    private String rejectCode;

    private String revFlag;

    private String resCode;

    private String supportFuncFlag;

    private String insProp;

    private String type;

    private String voucherNo;

    private String discount;

//    private String termTraceNum;
//
//    private String traceNum;
//
//    private String retrivlRefNum;
//
//    private String orderNumber;
//
//    private String outTradeNo;
//
//    private String transId;
//
//    private String tradeNo;
//
//    private String qrcVoucherNo;
//
    private String isUpopMerchant;

    private Timestamp createTime;

    @JSONField(name = "INSCODE")
    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    @JSONField(name = "MERCODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "MERNAME")
    public void setMerName(String merName) {
        this.merName = merName;
    }

    @JSONField(name = "TERMID")
    public void setTermCode(String termCode) {
        this.termCode = termCode;
    }

    @JSONField(name = "STATUS")
    public void setStatus(String status) {
        this.status = status;
    }

    @JSONField(name = "AMOUNT")
    public void setAmount(BigDecimal amount) {
        this.amount = amount.multiply(new BigDecimal(100));
    }

    @JSONField(name = "CURRENCY")
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @JSONField(name = "ORDERNO")
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @JSONField(name = "TABLENAME")
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @JSONField(name = "TRACENO")
    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    @JSONField(name = "ACCTNUM")
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    @JSONField(name = "REFERENCENO")
    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    @JSONField(name = "CREATETIME")
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @JSONField(name = "TRADETYPE")
    public void setTraceType(String traceType) {
        this.traceType = traceType;
    }

    @JSONField(name = "RNUM")
    public void setRejectCode(String rejectCode) {
        this.rejectCode = rejectCode;
    }

    @JSONField(name = "RESCODE")
    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    @JSONField(name = "SUPPORTFUNCFLAG")
    public void setSupportFuncFlag(String supportFuncFlag) {
        this.supportFuncFlag = supportFuncFlag;
    }

    @JSONField(name = "INSPROP")
    public void setInsProp(String insProp) {
        this.insProp = insProp;
    }

    @JSONField(name = "VOUCHERNO")
    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    @JSONField(name = "ISUPOPMERCHANT")
    public void setIsUpopMerchant(String isUpopMerchant) {
        this.isUpopMerchant = isUpopMerchant;
    }

    @JSONField(name = "REVFLAG")
    public void setRevFlag(String revFlag) {
        this.revFlag = revFlag;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setType(String type) {
        this.type = type;
    }

//    @JSONField(name = "term_trace_num")
//    public void setTermTraceNum(String termTraceNum) {
//        this.termTraceNum = termTraceNum;
//    }
//
//    @JSONField(name = "trace_num")
//    public void setTraceNum(String traceNum) {
//        this.traceNum = traceNum;
//    }
//
//    @JSONField(name = "retrivl_ref_num")
//    public void setRetrivlRefNum(String retrivlRefNum) {
//        this.retrivlRefNum = retrivlRefNum;
//    }
//
//    @JSONField(name = "order_number")
//    public void setOrderNumber(String orderNumber) {
//        this.orderNumber = orderNumber;
//    }
//
//    @JSONField(name = "out_trade_no")
//    public void setOutTradeNo(String outTradeNo) {
//        this.outTradeNo = outTradeNo;
//    }
//
//    @JSONField(name = "trans_id")
//    public void setTransId(String transId) {
//        this.transId = transId;
//    }
//
//    @JSONField(name = "trade_no")
//    public void setTradeNo(String tradeNo) {
//        this.tradeNo = tradeNo;
//    }
//
//    @JSONField(name = "term_trace_num")
//    public void setQrcVoucherNo(String qrcVoucherNo) {
//        this.qrcVoucherNo = qrcVoucherNo;
//    }

}
