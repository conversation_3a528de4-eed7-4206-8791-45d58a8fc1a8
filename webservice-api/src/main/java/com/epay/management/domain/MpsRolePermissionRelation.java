package com.epay.management.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Entity
@Getter
@Setter
@Table(name="mps_role_permission_relation")
@Where(clause = "valid = 1")
public class MpsRolePermissionRelation extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "角色ID不能为空")
    private String roleId;

    @NotEmpty(message = "权限代码不能为空")
    private String permissionCode;

    private String info;
}
