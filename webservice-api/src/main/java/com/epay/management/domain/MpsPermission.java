package com.epay.management.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Entity
@Getter
@Setter
@Table(name="mps_permission")
@Where(clause = "valid = 1")
public class MpsPermission extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotEmpty(message = "权限代码不能为空")
    private String permissionCode;

    @NotEmpty(message = "权限名称不能为空")
    private String permissionName;

    @NotNull(message = "等级不能为空")
    private Integer level;

    private String info;
}
