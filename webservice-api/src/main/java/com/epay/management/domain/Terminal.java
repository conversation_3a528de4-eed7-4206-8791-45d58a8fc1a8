package com.epay.management.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Getter
public class Terminal implements Serializable {

    private String insCode;

    private String terminalSN;

    private String merCode;

    private String termCode;

    private String type;

    private String status;

    private String simCardNumber;

    private String model;

    private String tmkDownloadFlag;

    private String tmkDownloadMethod;

    private String msgEncryptFlag;

    private String icSupportFlag;

    private String tipsSupportFlag;

    private String recCreateTm;

    private String recUpdateTm;


    @JSONField(name = "MER_ACQ_CODE")
    public void setInsCode(String insCode) {
        this.insCode = insCode;
    }

    @JSONField(name = "POS_SERIAL_NO")
    public void setTerminalSN(String terminalSN) {
        this.terminalSN = terminalSN;
    }

    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @J<PERSON>NField(name = "TERM_CODE")
    public void setTermCode(String termCode) {
        this.termCode = termCode;
    }

    @JSONField(name = "CONNECT_TYPE")
    public void setType(String type) {
        this.type = type;
    }

    @JSONField(name = "TERM_STATUS")
    public void setStatus(String status) {
        this.status = status;
    }

    @JSONField(name = "SIMCARD_SERIAL_NO")
    public void setSimCardNumber(String simCardNumber) {
        this.simCardNumber = simCardNumber;
    }

    @JSONField(name = "TERM_TYPE")
    public void setModel(String model) {
        this.model = model;
    }

    @JSONField(name = "TMK_DOWNLOAD_FLAG")
    public void setTmkDownloadFlag(String tmkDownloadFlag) {
        this.tmkDownloadFlag = tmkDownloadFlag;
    }

    @JSONField(name = "TMK_DOWNLOAD_METHOD")
    public void setTmkDownloadMethod(String tmkDownloadMethod) {
        this.tmkDownloadMethod = tmkDownloadMethod;
    }

    @JSONField(name = "MSG_ENCRYPT_FLAG")
    public void setMsgEncryptFlag(String msgEncryptFlag) {
        this.msgEncryptFlag = msgEncryptFlag;
    }

    @JSONField(name = "IC_SUPPORT_FLAG")
    public void setIcSupportFlag(String icSupportFlag) {
        this.icSupportFlag = icSupportFlag;
    }

    @JSONField(name = "TIPS_SUPPORT_FLAG")
    public void setTipsSupportFlag(String tipsSupportFlag) {
        this.tipsSupportFlag = tipsSupportFlag;
    }

    @JSONField(name = "REC_CREATE_TM")
    public void setRecCreateTm(String recCreateTm) {
        this.recCreateTm = recCreateTm;
    }

    @JSONField(name = "REC_UPDATE_TM")
    public void setRecUpdateTm(String recUpdateTm) {
        this.recUpdateTm = recUpdateTm;
    }
}
