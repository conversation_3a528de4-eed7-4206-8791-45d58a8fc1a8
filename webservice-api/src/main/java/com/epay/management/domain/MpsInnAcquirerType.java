package com.epay.management.domain;

import com.epay.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Getter
@Setter
public class MpsInnAcquirerType extends BaseEntity {

    private Integer id;

    private String location;

    private String type;

    private String acquirer;

    private String isPos;

    private String isOnline;

    private String isBillpay;

}
