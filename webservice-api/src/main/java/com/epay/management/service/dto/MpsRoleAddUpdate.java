package com.epay.management.service.dto;

import com.epay.validated.AddVerify;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/12
 */
@Getter
@Setter
public class MpsRoleAddUpdate implements Serializable {

    @NotEmpty(message = "角色名称不能为空", groups = {AddVerify.class})
    private String roleName;

    @NotEmpty(message = "描述不能为空", groups = {AddVerify.class})
    private String info;

}
