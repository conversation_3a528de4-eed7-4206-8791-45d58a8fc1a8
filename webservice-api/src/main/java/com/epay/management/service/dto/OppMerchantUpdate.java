package com.epay.management.service.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class OppMerchantUpdate implements Serializable {

	@NotEmpty(message = "merCode not empty")
	private String merCode;

	@NotNull(message = "status not empty")
	private Integer status;

}
