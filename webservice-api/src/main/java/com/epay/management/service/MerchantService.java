package com.epay.management.service;

import com.epay.management.domain.*;
import com.epay.management.service.dto.*;
import com.epay.user.domain.TblInsCfg;
import com.epay.utils.PageResult;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
public interface MerchantService {

    /**
     * query Merchant
     *
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<MerchantList> merchant(MerchantCriteria criteria, Pageable pageable);


    /**
     * query merchantList
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<MerchantSelfCenterList> merchantList(MerchantListCriteria criteria, Pageable pageable);


    /**
     * query merchant detail
     *
     * @param id
     * @return
     */
    MerchantInfo merchantDetail(String id);

    /**
     * query merchant detail
     *
     * @param merCodes
     * @return
     */
    List<MerchantInfoDetail> merchantDetailList(List<String> merCodes);


    /**
     * query merchant detail
     *
     * @param criteria
     * @return
     */
    List<MerchantInfoDetail> merchantDetailList(MerchantDetailCriteria criteria);

    /**
     * query merchant detail
     *
     * @param merCode
     * @return
     */
    Merchant merchantInfo(String merCode);

    /**
     * query ins
     *
     * @param pageable
     * @return
     */
    PageResult<TblInsCfg> ins(InsCriteria insCriteria, Pageable pageable);

    /**
     * query terminal
     *
     * @param pageable
     * @return
     */
    PageResult<Terminal> terminal(TerminalCriteria criteria, Pageable pageable);

    /**
     * payment Type
     * @return
     */
    List<PaymentType> paymentType(PaymentTypeCriteria criteria);

    /**
     * payment Type temporary
     * @param criteria
     * @return
     */
    List<PaymentType> paymentTypeTemporary(PaymentTypeCriteria criteria);

    /**
     * get paymentType by merCode
     * @param merCode
     * @return
     */
    String getPaymentTypeByMerCode(String merCode);

}
