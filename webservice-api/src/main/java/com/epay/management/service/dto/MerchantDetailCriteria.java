package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
@AllArgsConstructor
public class MerchantDetailCriteria {

	public MerchantDetailCriteria() {
	}

	public MerchantDetailCriteria(String merCode) {
		this.merCode = merCode;
	}

	public MerchantDetailCriteria(List<String> merCodes) {
		this.merCodes = merCodes;
	}


	private String insCode;
	private String merCode;
	private String merType;
	private List<String> merCodes;
	private String merName;
	private String orgId;
	private String userId;
	private String orgName;
	private String merStatus;
	private String type;
	private String startTime;
	private String endTime;
	private Boolean isOrgBind;


}
