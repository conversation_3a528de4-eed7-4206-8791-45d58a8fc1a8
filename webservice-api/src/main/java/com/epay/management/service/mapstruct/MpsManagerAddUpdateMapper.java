package com.epay.management.service.mapstruct;

import com.epay.base.BaseMapper;
import com.epay.management.domain.MpsManager;
import com.epay.management.service.dto.MpsManagerAddUpdate;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Mapper(componentModel = "spring",uses = {},unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MpsManagerAddUpdateMapper extends BaseMapper<MpsManagerAddUpdate, MpsManager> {

}
