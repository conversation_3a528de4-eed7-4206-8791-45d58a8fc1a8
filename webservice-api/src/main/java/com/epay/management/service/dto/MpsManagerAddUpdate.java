package com.epay.management.service.dto;

import com.epay.validated.AddVerify;
import com.epay.validated.UpdateVerify;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
public class MpsManagerAddUpdate implements Serializable {

    @NotEmpty(message = "account not empty", groups = {AddVerify.class})
    private String account;

    @NotEmpty(message = "name not empty", groups = {AddVerify.class})
    private String name;

    @NotEmpty(message = "phone not empty", groups = {AddVerify.class})
    @Email(message = "email format error", groups = {AddVerify.class, UpdateVerify.class})
    private String email;

    @NotEmpty(message = "password not empty", groups = {AddVerify.class})
    private String password;

    private String roleId;

    @NotEmpty(message = "insCode not empty", groups = {AddVerify.class})
    private String insCode;

    private String info;

}
