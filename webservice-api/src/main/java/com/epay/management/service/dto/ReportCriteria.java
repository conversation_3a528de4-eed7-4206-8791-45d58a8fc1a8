package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
@AllArgsConstructor
public class ReportCriteria {

	public ReportCriteria() {
	}

	private String searchKeyword;
	private Integer classify;
	private String fileName;
	private String insCode;
	private String fileType;
	private Timestamp startDate;
	private Timestamp endDate;
	private String merCode;
	private String reportType;
	private String paymentType;

}
