package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
@AllArgsConstructor
public class TransactionCriteria {

	public TransactionCriteria() {
	}

	public TransactionCriteria(String type,String orderNo) {
		this.type = type;
		this.orderNo = orderNo;
	}

	public TransactionCriteria(String orderNo) {
		this.type = "all";
		this.orderNo = orderNo;
	}

	@NotEmpty(message = "type not empty")
	private String type;

	private String transactionType;

	private String insCode;
	private String merCode;
	private String termCode;
	private String status;
	private String startDate;
	private String endDate;
	private String orderNo;
	private String traceNo;
	private String cardNo;
	private String referenceNo;
	private BigDecimal amount;
	private String currency;
	private String paymentMethod;
	private String traceType;
	private String paymentType;
	private Integer startRow;
	private Integer endRow;
	private String searchKeyword;
	private String voucherNo;
	private String menuType;


}
