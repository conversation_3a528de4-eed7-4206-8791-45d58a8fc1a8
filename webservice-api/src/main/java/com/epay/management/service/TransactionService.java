package com.epay.management.service;

import com.epay.management.service.dto.*;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;
import com.epay.management.domain.MerchantInfo;
import com.epay.management.domain.MerchantList;
import com.epay.management.domain.Terminal;
import com.epay.management.domain.TransactionList;
import com.epay.user.domain.TblInsCfg;
import com.epay.utils.PageResult;

/**
 * 交易数据管理
 * <AUTHOR>
 * @date 2024/4/18
 */
public interface TransactionService {

    /**
     * realtime
     *
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<TransactionList> realtime(TransactionCriteria criteria, Pageable pageable);

    /**
     * refund
     * @param refundReq
     * @return
     */
    RestResult refund(RefundReq refundReq);

}
