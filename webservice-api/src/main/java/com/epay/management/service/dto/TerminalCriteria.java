package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
@AllArgsConstructor
public class TerminalCriteria implements Serializable {

    public TerminalCriteria() {
    }

    public TerminalCriteria(String merCode) {
        this.merCode = merCode;
    }

    private String searchKeyword;

    private String insCode;

    private String terminalSN;

    private String merCode;

    private String termCode;

    private String type;

    private Integer status;

    private String simCardNumber;

    private String model;

    private String startDate;

    private String endDate;
}
