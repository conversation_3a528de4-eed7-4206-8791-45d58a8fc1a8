package com.epay.management.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epay.base.login.JwtUserDto;
import com.epay.config.KeyPathConstant;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.BadRequestException;
import com.epay.management.domain.MerchantList;
import com.epay.management.domain.ReportList;
import com.epay.management.service.MerchantService;
import com.epay.management.service.ReportService;
import com.epay.management.service.dto.MerchantCriteria;
import com.epay.management.service.dto.ReportCriteria;
import com.epay.management.service.dto.ReportFile;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    private final DataServerProperties dataServerProperties;
    private final MerchantService merchantService;

    /**
     * 报表功能
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public Map<String, Object> data(ReportCriteria criteria, Pageable pageable) {
        Map<String, Object> resMap = new HashMap<>();
        try {
            // 权限校验
            JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
            if (StringUtils.isEmpty(criteria.getReportType())) {
                // 当ReportType为空时，需要同时具备两个权限
                if (!currentUser.getPermissions().contains("A500020011") || !currentUser.getPermissions().contains("A500020012")) {
                    throw new BadRequestException(RestEnum.NOT_POWER.getMessage());
                }
            } else {
                // 当ReportType不为空时，基于ReportType的值检查权限
                String reportType = criteria.getReportType();
                boolean hasPermission = ("Monthly".equals(reportType) && currentUser.getPermissions().contains("A500020012"))
                        || ("Daily".equals(reportType) && currentUser.getPermissions().contains("A500020011"));
                if (!hasPermission) {
                    throw new BadRequestException(RestEnum.NOT_POWER.getMessage());
                }
            }
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_SETTLE_FILE");
            JSONObject condition = new JSONObject();
            if (criteria.getClassify() != null && criteria.getClassify() == 2) {
                condition.put("FILE_IO", 2);
                condition.put("FILE_TYPE", "MER");
            } else {
                condition.put("FILE_IO", "$ne(2)");
                condition.put("FILE_TYPE", "$ne(MER)");
            }
            if (StringUtils.isNotEmpty(criteria.getFileName())) {
                condition.put("FILE_NAME", criteria.getFileName());
            }
            if (StringUtils.isNotEmpty(criteria.getInsCode())) {
                condition.put("INS_CODE", criteria.getInsCode());
            }
            if (StringUtils.isNotEmpty(criteria.getFileType())) {
                condition.put("FILE_TYPE", criteria.getFileType());
            }
//            if (StringUtils.isNotEmpty(criteria.getStartDate())) {
//                condition.put("SETT_DATE", "$gte(" + criteria.getStartDate() + ")");
//            }
//            if (StringUtils.isNotEmpty(criteria.getEndDate())) {
//                condition.put("SETT_DATE", "$lte(" + criteria.getEndDate() + ")");
//            }
            if (criteria.getStartDate() != null && criteria.getEndDate() != null) {
                condition.put("SETT_DATE", "$between(" + DateUtil.parseTimeStr(criteria.getStartDate()) + "," + DateUtil.parseTimeStr(criteria.getEndDate()) + ")");
            }
            condition.put("MER_CODE", "$in(" + String.join(",", currentUser.getAuthMap().keySet()) +")");
            if (criteria.getClassify() != null && criteria.getClassify() == 2 && StringUtils.isNotEmpty(criteria.getMerCode())) {
                condition.put("@MER_CODE", "$like(%" + criteria.getMerCode() + "%)");
            }
            if (criteria.getReportType() != null && "Monthly".equals(criteria.getReportType())) {
                // 包含MonthReport
                condition.put("FILE_PATH", "$like(MonthReport)");
            }
            if (criteria.getReportType() != null && "Daily".equals(criteria.getReportType())) {
                // 不包含MonthReport
                condition.put("FILE_PATH", "$notlike(MonthReport)");
            }
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("sort", "rec_create_tm");
            params.put("sortType", "desc");
            for (Sort.Order order : pageable.getSort()) {
                params.put("sort", convert(order.getProperty()));
                params.put("sortType", order.getDirection().name());
            }
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<ReportList> res = JSON.parseArray(resListJson.getString("data"), ReportList.class);
                StringUtils.trimStringsInList(res);
                List<String> merCodes = res.stream()
                        .map(reportList -> StringUtils.isNotEmpty(reportList.getMerCode().trim()) ? reportList.getMerCode() : null)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (merCodes.isEmpty()) {
                    Map<String, Integer> countMap = new HashMap<>();
                    // 当没有商户代码时，返回空的countMap（不包含任何支付方式）
                    resMap.put("page", PageUtil.toPage(res, resCountJson.getInteger("data")));
                    resMap.put("countMap", countMap);
                    return resMap;
                }
                PageResult<MerchantList> merchantList = merchantService.merchant(new MerchantCriteria(merCodes), PageRequest.of(0, Integer.MAX_VALUE));
                Map<String, MerchantList> merchantMap = merchantList.getData().stream().collect(Collectors.toMap(MerchantList::getMerCode, merchant -> merchant));
                res.forEach(report -> {
                    MerchantList merchant = merchantMap.get(report.getMerCode());
                    if (merchant != null) {
                        report.setPayChannel(merchant.getGateway());
                        report.setMerName(merchant.getMerName());
                    } else {
                        report.setPayChannel("");
                    }
                });

                // 根据 paymentType 过滤结果
                if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                    res = res.stream()
                            .filter(report -> criteria.getPaymentType().equals(report.getPayChannel()))
                            .collect(Collectors.toList());
                }

                // 生成支付方式统计数据（不带 paymentType 条件）
                Map<String, Integer> countMap = getReportCountMap(params, condition, currentUser);

                // 如果有 paymentType 过滤，需要重新计算总数
                int totalCount = resCountJson.getInteger("data");
                if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                    totalCount = res.size();
                }

                PageResult<ReportList> page = PageUtil.toPage(res, totalCount);
                resMap.put("page", page);
                resMap.put("countMap", countMap);
                return resMap;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    private String convert(String source) {
        switch (source) {
            case "createTime":
                return "rec_create_tm";
            case "settDate":
                return "sett_date";
            default:
                return source;
        }
    }

    /**
     * 报表文件下载
     *
     * @param file
     * @return
     */
    @Override
    public ResponseEntity<StreamingResponseBody> file(ReportFile file) {
        try {
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("filePath", file.getFilePath());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getFileUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestList.execute();
            byte[] fileContent = responseCount.bodyBytes();
            InputStream inputStream = new ByteArrayInputStream(fileContent);

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + FileUtil.getName(file.getFilePath()) + "\""); // You can set the filename as needed
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            StreamingResponseBody responseBody = outputStream -> {
                int numberOfBytesToWrite;
                byte[] data = new byte[1024];
                while ((numberOfBytesToWrite = inputStream.read(data, 0, data.length)) != -1) {
                    outputStream.write(data, 0, numberOfBytesToWrite);
                }
                inputStream.close();
            };

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(fileContent.length)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void preview(HttpServletResponse response, ReportFile reportFile) {
        try {
            //获取文件名后缀
            String suffix = reportFile.getFilePath().substring(reportFile.getFilePath().lastIndexOf(".") + 1);
            if (!suffix.equals("pdf")) {
                response.setContentType("text/plain");
                response.getWriter().write("Can only preview PDF file.");
                return;
            }
            // 获取文件名
            String fileName = reportFile.getFilePath().substring(reportFile.getFilePath().lastIndexOf("/") + 1);
            byte[] fileContent = null;
            String relativeFilePath = "upload" + reportFile.getFilePath();
            // 保存到项目目录下的 upload 目录
            String absoluteFilePath = Paths.get(System.getProperty("user.dir"), relativeFilePath).toString();
            File existFile = new File(absoluteFilePath);
            if (existFile.exists()) {
                fileContent = FileUtil.readBytes(existFile);
            } else {
                Map<String, Object> params = new TreeMap<>();
                params.put("timestamp", new Date().getTime() / 1000);
                params.put("filePath", reportFile.getFilePath());
                params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
                HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getFileUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
                HttpResponse responseCount = requestList.execute();
                fileContent = responseCount.bodyBytes();
                InputStream inputStream = new ByteArrayInputStream(fileContent);
                // 创建目录并写入文件
                File parentDir = new File(absoluteFilePath).getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    boolean dirsCreated = parentDir.mkdirs();
                    if (!dirsCreated) {
                        throw new RuntimeException("Failed to create directory: " + parentDir.getAbsolutePath());
                    }
                }
                FileUtil.writeFromStream(inputStream, absoluteFilePath);
            }
            //在这里设置头信息
            response.setContentType("application/pdf;charset=UTF-8");
            response.setHeader("Content-Disposition", "inline");
            OutputStream out = response.getOutputStream();
            byte[] bt = fileContent;
            try (InputStream stream = new ByteArrayInputStream(bt)) {
                // 需要引入pdfbox依赖,这里只是为了修改在浏览器上预览时展示的文件名,如果不需要,别的方式输出也可以
                PDDocument document = PDDocument.load(stream);
                PDDocumentInformation info = document.getDocumentInformation(); //获得文档属性对象
                info.setTitle(fileName); //此方法可以修改pdf预览文件名
                document.setDocumentInformation(info);
                document.save(out); //输出
                document.close();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取各支付方式的统计数据
     *
     * @param params 查询参数
     * @param condition 查询条件
     * @param currentUser 当前用户
     * @return 支付方式统计Map
     */
    private Map<String, Integer> getReportCountMap(Map<String, Object> params, JSONObject condition, JwtUserDto currentUser) throws Exception {
        Map<String, Integer> resMap = new HashMap<>();

        // 获取用户有权限的商户列表
        Set<String> userMerCodes = currentUser.getAuthMap().keySet();
        if (userMerCodes.isEmpty()) {
            // 当用户没有权限的商户时，返回空的countMap
            return resMap;
        }

        // 获取商户信息，用于确定支付方式
        MerchantCriteria merchantCriteria = new MerchantCriteria(new ArrayList<>(userMerCodes));
        PageResult<MerchantList> merchantPage = merchantService.merchant(merchantCriteria, PageRequest.of(0, Integer.MAX_VALUE));
        List<MerchantList> merchantList = merchantPage.getData();

        // 按支付方式分组商户
        Map<String, List<String>> paymentMethodMerchants = new HashMap<>();
        paymentMethodMerchants.put("unionPay", new ArrayList<>());
        paymentMethodMerchants.put("wechatPay", new ArrayList<>());
        paymentMethodMerchants.put("aliPay", new ArrayList<>());

        for (MerchantList merchant : merchantList) {
            String gateway = merchant.getGateway();
            // 当支付方式为0或其他无效值时，gateway为null，不进行统计
            if ("unionPay".equals(gateway)) {
                paymentMethodMerchants.get("unionPay").add(merchant.getMerCode());
            } else if ("wechatPay".equals(gateway)) {
                paymentMethodMerchants.get("wechatPay").add(merchant.getMerCode());
            } else if ("aliPay".equals(gateway)) {
                paymentMethodMerchants.get("aliPay").add(merchant.getMerCode());
            }
            // 当gateway为null或其他值时，不添加到任何支付方式列表中
        }

        // 为每种支付方式统计数据
        for (Map.Entry<String, List<String>> entry : paymentMethodMerchants.entrySet()) {
            String paymentMethod = entry.getKey();
            List<String> merCodes = entry.getValue();

            // 如果该支付方式没有对应的商户（即支付方式为0），则不添加到结果中
            if (merCodes.isEmpty()) {
                continue;
            }

            // 创建新的条件对象，添加商户代码过滤
            JSONObject paymentCondition = new JSONObject(condition);
            paymentCondition.put("MER_CODE", "$in(" + String.join(",", merCodes) + ")");

            // 创建新的参数对象进行统计查询
            Map<String, Object> countParams = new HashMap<>(params);
            countParams.put("condition", JSON.toJSONString(paymentCondition));
            countParams.put("analysis", "count(*)");
            countParams.remove("skip");
            countParams.remove("limit");
            countParams.remove("sign");
            countParams.put("sign", SignUtil.getSign(countParams, KeyPathConstant.MERCHANT_KEY));

            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(countParams));
            HttpResponse responseCount = requestCount.execute();
            JSONObject resCountJson = JSON.parseObject(responseCount.body());

            if (resCountJson.getInteger("code") == 0) {
                int count = resCountJson.getInteger("data");
                // 只有当统计数量大于0时才添加到结果中
                if (count > 0) {
                    resMap.put(paymentMethod, count);
                }
            }
        }

        return resMap;
    }

}
