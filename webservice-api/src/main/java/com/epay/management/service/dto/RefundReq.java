package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Data
@AllArgsConstructor
public class RefundReq {

    public RefundReq() {
    }

    @NotEmpty(message = "orderNo is not empty")
    private String orderNo;

    @NotNull(message = "amount is not null")
    private BigDecimal amount;

    @NotEmpty(message = "password is not empty")
    private String password;

    @NotEmpty(message = "type is not empty")
    private String type;

}
