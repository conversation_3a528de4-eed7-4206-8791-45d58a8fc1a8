package com.epay.management.service;


import com.epay.management.service.dto.*;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
public interface MpsRoleService {


    /**
     * 查询角色
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<MpsPermissionDto> queryAll(MpsRoleCriteria criteria, Pageable pageable);

    /**
     * 修改用户角色的权限信息
     * @param update
     * @param id
     * @return
     */
    RestResult update(MpsRolePermissionAddUpdate update, Integer id);

    /**
     * 创建角色
     * @param create
     * @return
     */
    RestResult create(MpsRoleAddUpdate create);

    /**
     * 删除角色
     * @param id
     */
    void delete(Integer id);
}
