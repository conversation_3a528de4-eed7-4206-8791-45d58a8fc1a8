package com.epay.management.service.dto;

import com.epay.annotation.Query;
import com.epay.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class MpsRoleCriteria extends BaseDto implements Serializable {

	@Query(type = Query.Type.INNER_LIKE)
	private String permissionCode;

	@Query(type = Query.Type.INNER_LIKE)
	private String permissionName;

	@Query(type = Query.Type.INNER_LIKE)
	private String roleId;

	@Query(type = Query.Type.INNER_LIKE)
	private String level;

}
