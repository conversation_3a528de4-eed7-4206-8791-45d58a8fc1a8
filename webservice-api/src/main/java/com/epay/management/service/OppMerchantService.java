package com.epay.management.service;

import com.alibaba.fastjson.JSONObject;
import com.epay.management.service.dto.*;
import com.epay.utils.PageResult;
import com.epay.utils.RestResult;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
public interface OppMerchantService {

    /**
     * 查询目前可用的OPP商户
     * @param criteria
     * @param pageable
     * @return
     */
    PageResult<OppMerchantDto> merchant(OppMerchantCriteria criteria, Pageable pageable);

    /**
     * 获取一个的OPP交易机构的密钥
     * @param orgId
     * @return
     */
    String privateKey(String orgId);

    /**
     * 重置一个的OPP交易机构的密钥
     * @param merchantResetKey
     * @return
     */
    String resetPrivateKey(OppMerchantResetKey merchantResetKey);

    /**
     * 创建
     * @param create
     * @return
     */
    RestResult create(OppMerchantAdd create);

    /**
     * 更新可用的OPP商户
     * @param update
     * @param id
     * @return
     */
    RestResult update(OppMerchantUpdate update, Integer id);

    /**
     * 移除可用的OPP商户
     * @param delete
     * @return
     */
    RestResult delete(OppMerchantAdd delete);

    /**
     * 获取code
     * @return
     */
    JSONObject getCode();

    /**
     * 技术文档下载
     * @return
     */
    ResponseEntity<StreamingResponseBody> technicalManualDownload();

}
