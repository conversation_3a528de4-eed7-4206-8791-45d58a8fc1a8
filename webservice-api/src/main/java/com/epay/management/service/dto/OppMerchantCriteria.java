package com.epay.management.service.dto;

import com.epay.annotation.Query;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class OppMerchantCriteria implements Serializable {

	public OppMerchantCriteria() {
	}

	public OppMerchantCriteria(String merCode) {
		this.merCode = merCode;
	}

	public OppMerchantCriteria(String merCode,String orgId) {
		this.merCode = merCode;
		this.orgId = orgId;
	}

	@Query(type = Query.Type.INNER_LIKE)
	private String merCode;

	@Query(type = Query.Type.INNER_LIKE)
	private String orgId;

}
