package com.epay.management.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epay.config.KeyPathConstant;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.BadRequestException;
import com.epay.management.domain.*;
import com.epay.management.enums.GatewayEnum;
import com.epay.management.service.MerchantService;
import com.epay.management.service.dto.*;
import com.epay.management.service.dto.InsCriteria;
import com.epay.management.service.dto.MerchantCriteria;
import com.epay.management.service.mapstruct.MpsTerminalListMapper;
import com.epay.pay.domain.EpayPayChannel;
import com.epay.pay.repository.EpayPayChannelRepository;
import com.epay.user.domain.TblInsCfg;
import com.epay.user.domain.UcUserMerchantPossess;
import com.epay.user.service.UcOrganizationMerchantPossessService;
import com.epay.user.service.UcOrganizationService;
import com.epay.user.service.UcUserMerchantPossessService;
import com.epay.user.service.dto.*;
import com.epay.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Service
@RequiredArgsConstructor
public class MerchantServiceImpl implements MerchantService {

    private final DataServerProperties dataServerProperties;
    private final UcOrganizationService organizationService;
    private final UcOrganizationMerchantPossessService organizationMerchantPossessService;
    private final UcUserMerchantPossessService ucUserMerchantPossessService;
    private final MpsTerminalListMapper terminalListMapper;
    private final EpayPayChannelRepository epayPayChannelRepository;

    /**
     * 查询商户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<MerchantList> merchant(MerchantCriteria criteria, Pageable pageable) {
        try {
            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            Map<String, String> condition = new HashMap<>();
            if (StringUtils.isNotEmpty(criteria.getInsCode())) {
                condition.put("MER_ACQ_CODE", criteria.getInsCode());
            }
            if (StringUtils.isNotEmpty(criteria.getMerCode())) {
                condition.put(new String("MER_CODE"), "$like(%" + criteria.getMerCode() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getMerName())) {
                condition.put("MER_NAME", "$like(%" + criteria.getMerName() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                condition.put("MER_NAME", "$like(%" + criteria.getSearchKeyword() + "%)");
            }
            // 获取orgId、orgName 关联的商户数据
            List<String> orgIds = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getOrgId())) {
                orgIds.add(criteria.getOrgId());
            }
            if (criteria.getMerCodes() != null && !criteria.getMerCodes().isEmpty()) {
                ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", criteria.getMerCodes()) + ")");
            }
            if (criteria.getIsOrgBind() != null && criteria.getIsOrgBind()) {
                RestResult organizationMerchantPossessListRes = organizationMerchantPossessService.queryAll(new UcOrganizationMerchantPossessCriteria(), PageRequest.of(0, Integer.MAX_VALUE));
                List<UcOrganizationMerchantPossessDto> organizationMerchantPossessList = JSON.parseArray(JSON.toJSONString(
                        JSON.parseObject(JSON.toJSONString(organizationMerchantPossessListRes.getData())).getJSONArray("data")), UcOrganizationMerchantPossessDto.class);
                List<String> merCodes = organizationMerchantPossessList.stream().map(UcOrganizationMerchantPossessDto::getMerCode).distinct().collect(Collectors.toList());
                if (!merCodes.isEmpty()) {
                    ConditionUtil.put(condition, "MER_CODE", "$notin(" + String.join(",", merCodes) + ")");
                }
            }
            if (StringUtils.isNotEmpty(criteria.getOrgName())) {
                UcOrganizationCriteria organizationCriteria = new UcOrganizationCriteria();
                organizationCriteria.setOrgName(criteria.getOrgName());
                RestResult organizationListRest = organizationService.queryAll(organizationCriteria, PageRequest.of(0, 10));
                if (organizationListRest.getCode() == 0) {
                    JSONObject organization = JSONObject.parseObject(JSON.toJSONString(organizationListRest.getData()));
                    Integer total = organization.getInteger("total");
                    if (total > 0) {
                        List<UcOrganizationDto> organizationList = JSON.parseArray(organization.getString("data"), UcOrganizationDto.class);
                        orgIds.addAll(organizationList.stream().map(UcOrganizationDto::getOrgId).collect(Collectors.toList()));
                    }
                }
            }
            List<String> merCodes = new ArrayList<>();
            if (!orgIds.isEmpty()) {
                UcOrganizationMerchantPossessCriteria organizationMerchantPossessCriteria = new UcOrganizationMerchantPossessCriteria();
                organizationMerchantPossessCriteria.setOrgIds(orgIds);
                RestResult organizationMerchantPossess = organizationMerchantPossessService.queryAll(organizationMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
                if (organizationMerchantPossess.getCode() == 0) {
                    JSONObject organization = JSONObject.parseObject(JSON.toJSONString(organizationMerchantPossess.getData()));
                    Integer total = organization.getInteger("total");
                    if (total > 0) {
                        List<UcOrganizationMerchantPossessDto> organizationList = JSON.parseArray(organization.getString("data"), UcOrganizationMerchantPossessDto.class);
                        merCodes = organizationList.stream().map(UcOrganizationMerchantPossessDto::getMerCode).collect(Collectors.toList());
                        if (!merCodes.isEmpty()) {
                            ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", merCodes) + ")");
                        }
                    } else {
                        return PageUtil.toPage(new ArrayList<>(), 0);
                    }
                }
            }
            if (criteria.getUserId() != null) {
                UcUserMerchantPossessCriteria userMerchantPossessCriteria = new UcUserMerchantPossessCriteria();
                userMerchantPossessCriteria.setUserId(criteria.getUserId());
                RestResult userMerchantPossess = ucUserMerchantPossessService.queryAll(userMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
                if (userMerchantPossess.getCode() == 0) {
                    JSONObject organization = JSONObject.parseObject(JSON.toJSONString(userMerchantPossess.getData()));
                    Integer total = organization.getInteger("total");
                    if (total > 0) {
                        List<UcUserMerchantPossess> organizationList = JSON.parseArray(organization.getString("data"), UcUserMerchantPossess.class);
                        merCodes = organizationList.stream().map(UcUserMerchantPossess::getMerCode).collect(Collectors.toList());
                        if (!merCodes.isEmpty()) {
                            ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", merCodes) + ")");
                        }
                    } else {
                        return PageUtil.toPage(new ArrayList<>(), 0);
                    }
                }
            }
            if (StringUtils.isNotEmpty(criteria.getMerStatus())) {
                condition.put("MER_STATUS", criteria.getMerStatus());
            }
            if (StringUtils.isNotEmpty(criteria.getType()) && !"all".equals(criteria.getType())) {
                String type = criteria.getType();
                if ("pos".equals(type)) {
                    type = "0";
                } else if ("npp".equals(type)) {
                    type = "8";
                } else if ("online".equals(type)) {
                    type = "$in(1,5,6)";
                } else if ("nbl".equals(type)) {
                    type = "$in(2,5,6)";
                } else if ("app".equals(type)) {
                    type = "9";
                }
                condition.put("IS_UPOP_MERCHANT", type);
            }
            if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                String type = criteria.getPaymentType();
                if ("unionPay".equals(type)) {
                    type = "1";
                } else if ("wechatPay".equals(type)) {
                    type = "8";
                } else if ("aliPay".equals(type)) {
                    type = "10";
                }
                condition.put("INSPROP", type);
            }
            if (StringUtils.isNotEmpty(criteria.getStartTime())) {
                condition.put("REC_CREATE_TM", "$gte(" + criteria.getStartTime() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getEndTime())) {
                condition.put("REC_CREATE_TM", "$lte(" + criteria.getEndTime() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getStartTime()) && StringUtils.isNotEmpty(criteria.getEndTime())) {
                condition.put("REC_CREATE_TM", "$between(" + criteria.getStartTime() + "," + criteria.getEndTime() + ")");
            }
            // condition
            // 相同key 的第二个key 加@
            params.put("condition", JSON.toJSONString(condition));
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(new JSONObject().fluentPut("MER_CODE", "$like(%" + criteria.getSearchKeyword() + "%)"));
                jsonArray.add(new JSONObject().fluentPut("MER_NAME", "$like(%" + criteria.getSearchKeyword() + "%)"));
                jsonArray.add(new JSONObject().fluentPut("MER_ACQ_CODE", "$like(%" + criteria.getSearchKeyword() + "%)"));
                params.put("condition", JSON.toJSONString(jsonArray));
            }
            params.put("count", 1);
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<MerchantList> res = JSON.parseArray(resListJson.getString("data"), MerchantList.class);
                StringUtils.trimStringsInList(res);
                for (MerchantList merchant : res) {
                    //terminal
                    if (criteria.getIsTerminalCount()) {
                        merchant.setTerminal(terminalCount(new TerminalCriteria(merchant.getMerCode())));
                    }
                    merchant.setGateway(GatewayEnum.getDescByName(merchant.getGateway()));
                    merchant.setTransactionCurrency(getEngAbbDesc(merchant.getTransactionCurrency()));
                    merchant.setSettlementCurrency(getEngAbbDesc(merchant.getSettlementCurrency()));
                    merchant.setAcquirer(getAcquirer(merchant.getAcquirer()));
                }
                return PageUtil.toPage(res, resCountJson.getInteger("data"));
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * query merchantList
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<MerchantSelfCenterList> merchantList(MerchantListCriteria criteria, Pageable pageable) {
        try {
            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            Map<String, String> condition = new HashMap<>();
            if (StringUtils.isNotEmpty(criteria.getInsCode())) {
                condition.put("MER_ACQ_CODE", criteria.getInsCode());
            }
            if (StringUtils.isNotEmpty(criteria.getMerCode())) {
                condition.put(new String("MER_CODE"), "$like(%" + criteria.getMerCode() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getMerName())) {
                condition.put("MER_NAME", "$like(%" + criteria.getMerName() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                condition.put("MER_NAME", "$like(%" + criteria.getSearchKeyword() + "%)");
            }
            // 获取orgId、orgName 关联的商户数据
            List<String> orgIds = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getOrgId())) {
                orgIds.add(criteria.getOrgId());
            }
            if (criteria.getMerCodes() != null && !criteria.getMerCodes().isEmpty()) {
                ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", criteria.getMerCodes()) + ")");
            }
            if (criteria.getIsOrgBind() != null && criteria.getIsOrgBind()) {
                RestResult organizationMerchantPossessListRes = organizationMerchantPossessService.queryAll(new UcOrganizationMerchantPossessCriteria(), PageRequest.of(0, Integer.MAX_VALUE));
                List<UcOrganizationMerchantPossessDto> organizationMerchantPossessList = JSON.parseArray(JSON.toJSONString(
                        JSON.parseObject(JSON.toJSONString(organizationMerchantPossessListRes.getData())).getJSONArray("data")), UcOrganizationMerchantPossessDto.class);
                List<String> merCodes = organizationMerchantPossessList.stream().map(UcOrganizationMerchantPossessDto::getMerCode).distinct().collect(Collectors.toList());
                if (!merCodes.isEmpty()) {
                    ConditionUtil.put(condition, "MER_CODE", "$notin(" + String.join(",", merCodes) + ")");
                }
            }
            if (StringUtils.isNotEmpty(criteria.getOrgName())) {
                UcOrganizationCriteria organizationCriteria = new UcOrganizationCriteria();
                organizationCriteria.setOrgName(criteria.getOrgName());
                RestResult organizationListRest = organizationService.queryAll(organizationCriteria, PageRequest.of(0, 10));
                if (organizationListRest.getCode() == 0) {
                    JSONObject organization = JSONObject.parseObject(JSON.toJSONString(organizationListRest.getData()));
                    Integer total = organization.getInteger("total");
                    if (total > 0) {
                        List<UcOrganizationDto> organizationList = JSON.parseArray(organization.getString("data"), UcOrganizationDto.class);
                        orgIds.addAll(organizationList.stream().map(UcOrganizationDto::getOrgId).collect(Collectors.toList()));
                    }
                }
            }
            List<String> merCodes = new ArrayList<>();
            if (!orgIds.isEmpty()) {
                UcOrganizationMerchantPossessCriteria organizationMerchantPossessCriteria = new UcOrganizationMerchantPossessCriteria();
                organizationMerchantPossessCriteria.setOrgIds(orgIds);
                RestResult organizationMerchantPossess = organizationMerchantPossessService.queryAll(organizationMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
                if (organizationMerchantPossess.getCode() == 0) {
                    JSONObject organization = JSONObject.parseObject(JSON.toJSONString(organizationMerchantPossess.getData()));
                    Integer total = organization.getInteger("total");
                    if (total > 0) {
                        List<UcOrganizationMerchantPossessDto> organizationList = JSON.parseArray(organization.getString("data"), UcOrganizationMerchantPossessDto.class);
                        merCodes = organizationList.stream().map(UcOrganizationMerchantPossessDto::getMerCode).collect(Collectors.toList());
                        if (!merCodes.isEmpty()) {
                            ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", merCodes) + ")");
                        }
                    } else {
                        return PageUtil.toPage(new ArrayList<>(), 0);
                    }
                }
            }
            if (criteria.getUserId() != null) {
                UcUserMerchantPossessCriteria userMerchantPossessCriteria = new UcUserMerchantPossessCriteria();
                userMerchantPossessCriteria.setUserId(criteria.getUserId());
                RestResult userMerchantPossess = ucUserMerchantPossessService.queryAll(userMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
                if (userMerchantPossess.getCode() == 0) {
                    JSONObject organization = JSONObject.parseObject(JSON.toJSONString(userMerchantPossess.getData()));
                    Integer total = organization.getInteger("total");
                    if (total > 0) {
                        List<UcUserMerchantPossess> organizationList = JSON.parseArray(organization.getString("data"), UcUserMerchantPossess.class);
                        merCodes = organizationList.stream().map(UcUserMerchantPossess::getMerCode).collect(Collectors.toList());
                        if (!merCodes.isEmpty()) {
                            ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", merCodes) + ")");
                        }
                    } else {
                        return PageUtil.toPage(new ArrayList<>(), 0);
                    }
                }
            }
            if (StringUtils.isNotEmpty(criteria.getMerStatus())) {
                condition.put("MER_STATUS", criteria.getMerStatus());
            }
            if (StringUtils.isNotEmpty(criteria.getType()) && !"all".equals(criteria.getType())) {
                String type = criteria.getType();
                if ("pos".equals(type)) {
                    type = "0";
                } else if ("npp".equals(type)) {
                    type = "8";
                } else if ("online".equals(type)) {
                    type = "$in(1,5,6)";
                } else if ("nbl".equals(type)) {
                    type = "$in(2,5,6)";
                } else if ("app".equals(type)) {
                    type = "9";
                }
                condition.put("IS_UPOP_MERCHANT", type);
            }
            if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                String type = criteria.getPaymentType();
                if ("unionPay".equals(type)) {
                    type = "1";
                } else if ("wechatPay".equals(type)) {
                    type = "8";
                } else if ("aliPay".equals(type)) {
                    type = "10";
                }
                condition.put("INSPROP", type);
            }
            if (StringUtils.isNotEmpty(criteria.getStartTime())) {
                condition.put("REC_CREATE_TM", "$gte(" + criteria.getStartTime() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getEndTime())) {
                condition.put("REC_CREATE_TM", "$lte(" + criteria.getEndTime() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getStartTime()) && StringUtils.isNotEmpty(criteria.getEndTime())) {
                condition.put("REC_CREATE_TM", "$between(" + criteria.getStartTime() + "," + criteria.getEndTime() + ")");
            }
            // condition
            // 相同key 的第二个key 加@
            params.put("condition", JSON.toJSONString(condition));
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(new JSONObject().fluentPut("MER_CODE", "$like(%" + criteria.getSearchKeyword() + "%)"));
                jsonArray.add(new JSONObject().fluentPut("MER_NAME", "$like(%" + criteria.getSearchKeyword() + "%)"));
                jsonArray.add(new JSONObject().fluentPut("MER_ACQ_CODE", "$like(%" + criteria.getSearchKeyword() + "%)"));
                params.put("condition", JSON.toJSONString(jsonArray));
            }
            params.put("count", 1);
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<MerchantSelfCenterList> res = JSON.parseArray(resListJson.getString("data"), MerchantSelfCenterList.class);
                StringUtils.trimStringsInList(res);
                for (MerchantSelfCenterList merchant : res) {
                    merchant.setPaymentType(GatewayEnum.getDescByName(merchant.getPaymentType()));
                    merchant.setCurrency(getEngAbbDesc(merchant.getCurrency()));
                }
                return PageUtil.toPage(res, resCountJson.getInteger("data"));
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public MerchantInfo merchantDetail(String id) {
        try {
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            JSONObject condition = new JSONObject();
            condition.put("MER_CODE", id);
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0) {
                List<MerchantInfo> res = JSON.parseArray(resListJson.getString("data"), MerchantInfo.class);
                if (res.isEmpty()) {
                    return null;
                }
                MerchantInfo merchantInfo = res.get(0);
                merchantInfo.setTransactionCurrency(getEngAbbDesc(merchantInfo.getTransactionCurrency()));
                merchantInfo.setSettlementCurrency(getEngAbbDesc(merchantInfo.getSettlementCurrency()));
                merchantInfo.setAcquirer(getAcquirer(merchantInfo.getAcquirer()));
                PageResult<Terminal> terminal = terminal(new TerminalCriteria(id), PageRequest.of(0, 5));
                merchantInfo.setTerminalList(terminalListMapper.toDto(terminal.getData()));
                return merchantInfo;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public List<MerchantInfoDetail> merchantDetailList(List<String> merCodes) {
        try {
            // 存储请求数据
            List<MerchantInfoDetail> res = new ArrayList<>();
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            JSONObject condition = new JSONObject();
            condition.put("MER_CODE", "$in(" + String.join(",", merCodes) + ")");
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0) {
                res = JSON.parseArray(resListJson.getString("data"), MerchantInfoDetail.class);
                if (res.isEmpty()) {
                    return res;
                }
                for (MerchantInfoDetail merchantInfo : res) {
                    merchantInfo.setTransactionCurrency(getEngAbbDesc(merchantInfo.getTransactionCurrency()));
                    merchantInfo.setSettlementCurrency(getEngAbbDesc(merchantInfo.getSettlementCurrency()));
                    merchantInfo.setAcquirer(getAcquirer(merchantInfo.getAcquirer()));
                }
                return res;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public List<MerchantInfoDetail> merchantDetailList(MerchantDetailCriteria criteria) {
        try {
            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            Map<String, String> condition = new HashMap<>();
            if (StringUtils.isNotEmpty(criteria.getInsCode())) {
                condition.put("MER_ACQ_CODE", criteria.getInsCode());
            }
            if (StringUtils.isNotEmpty(criteria.getMerCode())) {
                condition.put(new String("MER_CODE"), "$like(%" + criteria.getMerCode() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getMerName())) {
                condition.put("MER_NAME", "$like(%" + criteria.getMerName() + "%)");
            }
            // 获取orgId、orgName 关联的商户数据
            List<String> orgIds = new ArrayList<>();
            if (StringUtils.isNotEmpty(criteria.getOrgId())) {
                orgIds.add(criteria.getOrgId());
            }
            if (StringUtils.isNotEmpty(criteria.getMerStatus())) {
                condition.put("MER_STATUS", criteria.getMerStatus());
            }
            if (criteria.getMerCodes() == null) {
                criteria.setMerCodes(new ArrayList<>());
            }
            if (criteria.getMerCodes() != null && !criteria.getMerCodes().isEmpty()) {
                ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", criteria.getMerCodes()) + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getType())) {
                String type = criteria.getType();
                if ("pos".equals(type)) {
                    type = "0";
                } else if ("npp".equals(type)) {
                    type = "8";
                } else if ("online".equals(type)) {
                    type = "$in(1,5,6)";
                } else if ("nbl".equals(type)) {
                    type = "$in(2,5,6)";
                } else if ("app".equals(type)) {
                    type = "9";
                }
                condition.put("IS_UPOP_MERCHANT", type);
            }
            if (StringUtils.isNotEmpty(criteria.getStartTime())) {
                condition.put("REC_CREATE_TM", "$gte(" + criteria.getStartTime() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getEndTime())) {
                condition.put("REC_CREATE_TM", "$lte(" + criteria.getEndTime() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getStartTime()) && StringUtils.isNotEmpty(criteria.getEndTime())) {
                condition.put("REC_CREATE_TM", "$between(" + criteria.getStartTime() + "," + criteria.getEndTime() + ")");
            }
            // condition
            // 相同key 的第二个key 加@
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0) {
                List<MerchantInfoDetail> res = JSON.parseArray(resListJson.getString("data"), MerchantInfoDetail.class);
                StringUtils.trimStringsInList(res);
                for (MerchantInfoDetail merchant : res) {
                    merchant.setTransactionCurrency(getEngAbbDesc(merchant.getTransactionCurrency()));
                    merchant.setSettlementCurrency(getEngAbbDesc(merchant.getSettlementCurrency()));
                    merchant.setAcquirer(getAcquirer(merchant.getAcquirer()));
                }
                return res;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public Merchant merchantInfo(String merCode) {
        try {
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MERCHANT_INFO");
            JSONObject condition = new JSONObject();
            condition.put("MER_CODE", merCode);
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0) {
                List<Merchant> res = JSON.parseArray(resListJson.getString("data"), Merchant.class);
                if (res.isEmpty()) {
                    return null;
                }
                return res.get(0);
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public String getAcquirer(String insCode) {
        try {
            // 存储请求数据
            JSONObject condition = new JSONObject();
            condition.put("INS_CODE", insCode);
            JSONArray res = getDataRes("TBL_INS_CFG", condition);
            if (res == null) {
                return null;
            }
            return insCode + res.getJSONObject(0).getString("INS_ENG_NAME");

        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public String getEngAbbDesc(String currCode) {
        try {
            // 存储请求数据
            JSONObject condition = new JSONObject();
            condition.put("CURR_CODE", currCode);
            JSONArray res = getDataRes("TBL_CURR_CODE", condition);
            if (res == null) {
                return null;
            }
            return res.getJSONObject(0).getString("ENG_ABB_DESC");

        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * query ins
     *
     * @param pageable
     * @return
     */
    @Override
    public PageResult<TblInsCfg> ins(InsCriteria insCriteria, Pageable pageable) {
        try {
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_INS_CFG");
            JSONObject condition = new JSONObject();
            if (!StringUtils.isAllEmpty(insCriteria.getMerCode())) {
                PageResult<MerchantList> merchant = merchant(new MerchantCriteria(insCriteria.getMerCode()), PageRequest.of(0, 10));
                if (!merchant.getData().isEmpty()) {
                    condition.put("INS_CODE", merchant.getData().get(0).getInsCode());
                }
            }
            if (!StringUtils.isAllEmpty(insCriteria.getTermCode())) {
                PageResult<Terminal> terminal = terminal(new TerminalCriteria(insCriteria.getTermCode()), PageRequest.of(0, 10));
                if (!terminal.getData().isEmpty()) {
                    condition.put("INS_CODE", terminal.getData().get(0).getInsCode());
                }
            }
            if (!StringUtils.isAllEmpty(insCriteria.getInsCode())) {
                condition.put("INS_CODE", insCriteria.getInsCode());
            }
            if (!StringUtils.isAllEmpty(insCriteria.getInsName())) {
                condition.put("INS_ENG_NAME", insCriteria.getInsName());
            }
            if (!StringUtils.isAllEmpty(insCriteria.getInsCountry())) {
                condition.put("INS_COUNTRY", insCriteria.getInsCountry());
            }
            params.put("condition", condition.toString());
            if (StringUtils.isNotEmpty(insCriteria.getSearchKeyword())) {
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(new JSONObject().fluentPut("INS_CODE", "$like(%" + insCriteria.getSearchKeyword() + "%)"));
                jsonArray.add(new JSONObject().fluentPut("INS_ENG_NAME", "$like(%" + insCriteria.getSearchKeyword() + "%)"));
                params.put("condition", JSON.toJSONString(jsonArray));
            }
            params.put("count", 1);
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<TblInsCfg> res = JSON.parseArray(resListJson.getString("data"), TblInsCfg.class);
                StringUtils.trimStringsInList(res);
                return PageUtil.toPage(res, resCountJson.getInteger("data"));
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * query terminal
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<Terminal> terminal(TerminalCriteria criteria, Pageable pageable) {
        try {
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_TERM_INFO");
            JSONObject condition = new JSONObject();
            if (!StringUtils.isEmpty(criteria.getInsCode())) {
                condition.put("MER_ACQ_CODE", criteria.getInsCode());
            }
            if (!StringUtils.isEmpty(criteria.getTerminalSN())) {
                condition.put("POS_SERIAL_NO", criteria.getTerminalSN());
            }
            if (!StringUtils.isEmpty(criteria.getMerCode())) {
                condition.put("MER_CODE", "$like(%" + criteria.getMerCode() + "%)");
            }
            if (!StringUtils.isEmpty(criteria.getTermCode())) {
                condition.put("TERM_CODE", criteria.getTermCode());
            }
            if (!StringUtils.isEmpty(criteria.getType())) {
                condition.put("CONNECT_TYPE", criteria.getType());
            }
            if (criteria.getStatus() != null) {
                condition.put("TERM_STATUS", criteria.getStatus());
            }
            if (!StringUtils.isEmpty(criteria.getSimCardNumber())) {
                condition.put("SIMCARD_SERIAL_NO", criteria.getSimCardNumber());
            }
            if (!StringUtils.isEmpty(criteria.getModel())) {
                condition.put("TERM_TYPE", criteria.getModel());
            }
            if (!StringUtils.isEmpty(criteria.getStartDate())) {
                condition.put("REC_CREATE_TM", "$gte(" + criteria.getStartDate() + ")");
            }
            if (!StringUtils.isEmpty(criteria.getEndDate())) {
                condition.put("REC_CREATE_TM", "$lte(" + criteria.getEndDate() + ")");
            }
            params.put("condition", JSON.toJSONString(condition));
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(new JSONObject().fluentPut("TERM_CODE", "$like(%" + criteria.getSearchKeyword() + "%)"));
                jsonArray.add(new JSONObject().fluentPut("POS_SERIAL_NO", "$like(%" + criteria.getSearchKeyword() + "%)"));
                params.put("condition", JSON.toJSONString(jsonArray));
            }
            params.put("count", 1);
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());
            params.put("sort", " TERM_CODE");
            params.put("sortType", "ASC");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<Terminal> res = JSON.parseArray(resListJson.getString("data"), Terminal.class);
                StringUtils.trimStringsInList(res);
                return PageUtil.toPage(res, resCountJson.getInteger("data"));
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public List<PaymentType> paymentType(PaymentTypeCriteria criteria) {
        try {
            List<String> merCodes;
            // 原有的查询逻辑 - 不变
            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            Map<String, String> condition = new HashMap<>();
            // 通过订单号查询支付渠道
            if (StringUtils.isNotEmpty(criteria.getOrderNum())) {
                List<EpayPayChannel> channels = epayPayChannelRepository.findByOrderNum(criteria.getOrderNum());
                if (channels.isEmpty()) {
                    return new ArrayList<>();
                }
                merCodes = channels.stream().map(EpayPayChannel::getMerCode).collect(Collectors.toList());
            } else {
                // 获取用户下的权限数据
                UcUserMerchantPossessCriteria userMerchantPossessCriteria = new UcUserMerchantPossessCriteria();
                userMerchantPossessCriteria.setUserId(SecurityUtils.getCurrentLoginUser().getUserId());
                RestResult userMerchantPossess = ucUserMerchantPossessService.queryAll(userMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
                List<UcUserMerchantPossessDto> merchantList = JSON.parseArray(JSON.parseObject(JSON.toJSONString(userMerchantPossess.getData())).getString("data"), UcUserMerchantPossessDto.class);
                merCodes = merchantList.stream().map(UcUserMerchantPossessDto::getMerCode).collect(Collectors.toList());
            }
            if (merCodes.isEmpty()) {
                return new ArrayList<>();
            }
            if (StringUtils.isNotEmpty(criteria.getMerCode())) {
                merCodes.clear();
                merCodes.add(criteria.getMerCode());
            }
            ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", merCodes) + ")");
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("skip", 0);
            params.put("limit", Integer.MAX_VALUE);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<MerchantInfoDetail> res = JSON.parseArray(resListJson.getString("data"), MerchantInfoDetail.class);
                List<PaymentType> resList = new ArrayList<>();
                for (MerchantInfoDetail merchant : res) {
                    // todo  如果不是Y则不支持billPay交易  放开注释 正式启用时
                    // 如果不是Y则不支持billPay交易
//                    if ("Y".equals(merchant.getSupportBillPayFlag())) {
                    PaymentType paymentType = new PaymentType();
                    paymentType.setMerCode(merchant.getMerCode());
                    paymentType.setPaymentType(GatewayEnum.getDescByName(merchant.getGateway()));
                    paymentType.setCurrency(getEngAbbDesc(merchant.getTransactionCurrency()));
                    if (paymentType.getPaymentType() != null) {
                        resList.add(paymentType);
                    }
//                    }
                }
                StringUtils.trimStringsInList(resList);
                return resList;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public List<PaymentType> paymentTypeTemporary(PaymentTypeCriteria criteria) {
        return paymentType(criteria);
    }

    @Override
    public String getPaymentTypeByMerCode(String merCode) {
        try {
            PaymentTypeCriteria criteria = new PaymentTypeCriteria();
            criteria.setMerCode(merCode);
            List<PaymentType> paymentTypes = paymentType(criteria);

            if (paymentTypes != null && !paymentTypes.isEmpty()) {
                // 返回第一个匹配的paymentType
                return paymentTypes.get(0).getPaymentType();
            }

            return null;
        } catch (Exception e) {
            throw new BadRequestException("Failed to get paymentType for merCode: " + merCode + ", error: " + e.getMessage());
        }
    }

    private JSONArray getDataRes(String tableName, JSONObject condition) throws Exception {
        Map<String, Object> params = new TreeMap<>();
        params.put("timestamp", new Date().getTime() / 1000);
        params.put("table", tableName);
        params.put("condition", JSON.toJSONString(condition));
        params.put("count", 1);
        params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
        // 查询内容
        HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
        HttpResponse responseList = requestList.execute();
        // 处理结果
        JSONObject resListJson = JSON.parseObject(responseList.body());
        // 判断请求是否成功
        if (resListJson.getInteger("code") == 0) {
            JSONArray res = JSON.parseArray(resListJson.getString("data"));
            if (res.isEmpty()) {
                return null;
            }
            return res;
        } else {
            throw new BadRequestException(resListJson.getString("message"));
        }
    }


    /**
     * query terminal count
     *
     * @param criteria
     * @return
     */
    public int terminalCount(TerminalCriteria criteria) {
        try {
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_TERM_INFO");
            JSONObject condition = new JSONObject();
            if (!StringUtils.isEmpty(criteria.getInsCode())) {
                condition.put("MER_ACQ_CODE", criteria.getInsCode());
            }
            if (!StringUtils.isEmpty(criteria.getTerminalSN())) {
                condition.put("POS_SERIAL_NO", criteria.getTerminalSN());
            }
            if (!StringUtils.isEmpty(criteria.getMerCode())) {
                condition.put("MER_CODE", "$like(%" + criteria.getMerCode() + "%)");
            }
            if (!StringUtils.isEmpty(criteria.getTermCode())) {
                condition.put("TERM_CODE", criteria.getTermCode());
            }
            if (!StringUtils.isEmpty(criteria.getType())) {
                condition.put("CONNECT_TYPE", criteria.getType());
            }
            if (criteria.getStatus() != null) {
                condition.put("TERM_STATUS", criteria.getStatus());
            }
            if (!StringUtils.isEmpty(criteria.getSimCardNumber())) {
                condition.put("SIMCARD_SERIAL_NO", criteria.getSimCardNumber());
            }
            if (!StringUtils.isEmpty(criteria.getModel())) {
                condition.put("TERM_TYPE", criteria.getModel());
            }
            if (!StringUtils.isEmpty(criteria.getStartDate())) {
                condition.put("REC_CREATE_TM", "$gte(" + criteria.getStartDate() + ")");
            }
            if (!StringUtils.isEmpty(criteria.getEndDate())) {
                condition.put("REC_CREATE_TM", "$lte(" + criteria.getEndDate() + ")");
            }
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resCountJson.getInteger("code") == 0) {
                return resCountJson.getInteger("data");
            } else {
                throw new BadRequestException(resCountJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

//    public static void main(String[] args) throws Exception {
//        // 存储请求数据
//        Map<String, Object> params = new TreeMap<>();
//        params.put("email", "1");
//        params.put("userId", "1");
//        params.put("orgId", "1");
//        params.put("merCode", "1");
//        params.put("sign", SignUtil.getSign(params));
//        // 查询内容
//        HttpRequest requestList = HttpUtil.createGet("http://127.0.0.1:8089/api/user" + "?" + SignUtil.mapToUrlParamsEncoding(params));
//        HttpResponse responseList = requestList.execute();
//        System.out.println(responseList.body());
//    }
}
