package com.epay.management.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epay.config.KeyPathConstant;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.BadRequestException;
import com.epay.exception.CustomException;
import com.epay.management.domain.MerchantList;
import com.epay.management.enums.GatewayEnum;
import com.epay.management.service.OppMerchantService;
import com.epay.management.service.dto.*;
import com.epay.user.service.UcOrganizationMerchantPossessService;
import com.epay.user.service.dto.UcOrganizationMerchantPossessCriteria;
import com.epay.user.service.dto.UcOrganizationMerchantPossessDto;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Service
@RequiredArgsConstructor
public class OppMerchantServiceImpl implements OppMerchantService {

    private final DataServerProperties dataServerProperties;
    private final UcOrganizationMerchantPossessService organizationMerchantPossessService;

    /**
     * 查询目前可用的OPP商户
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<OppMerchantDto> merchant(OppMerchantCriteria criteria, Pageable pageable) {
        try {
            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MER_INFO");
            Map<String, String> condition = new HashMap<>();
            List<String> merCodes = new ArrayList<>();

            UcOrganizationMerchantPossessCriteria organizationMerchantPossessCriteria = new UcOrganizationMerchantPossessCriteria();
            organizationMerchantPossessCriteria.setOrgId(criteria.getOrgId());
            RestResult organizationMerchantPossess = organizationMerchantPossessService.queryAll(organizationMerchantPossessCriteria, PageRequest.of(0, Integer.MAX_VALUE));
            if (organizationMerchantPossess.getCode() == 0) {
                JSONObject organization = JSONObject.parseObject(JSON.toJSONString(organizationMerchantPossess.getData()));
                Integer total = organization.getInteger("total");
                if (total > 0) {
                    List<UcOrganizationMerchantPossessDto> organizationList = JSON.parseArray(organization.getString("data"), UcOrganizationMerchantPossessDto.class);
                    merCodes = organizationList.stream().map(UcOrganizationMerchantPossessDto::getMerCode).collect(Collectors.toList());
                } else {
                    return PageUtil.toPage(new ArrayList<>(), 0);
                }
            }
            if (StringUtils.isNotEmpty(criteria.getMerCode())) {
                merCodes = new ArrayList<>();
                merCodes.add(criteria.getMerCode());
            }
            if (!merCodes.isEmpty()) {
                ConditionUtil.put(condition, "MER_CODE", "$in(" + String.join(",", merCodes) + ")");
            }
            // condition
            params.put("condition", JSON.toJSONString(condition));
            params.put("count", 1);
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            // 增加统计参数
            params.remove("sign");
            params.put("analysis", "count(*)");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            // 查询统计
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0) {
                List<MerchantList> merchantLists = JSON.parseArray(resListJson.getString("data"), MerchantList.class);
                for (MerchantList merchant : merchantLists) {
                    //gateway
                    merchant.setGateway(GatewayEnum.getDescByName(merchant.getGateway()));
                }
                List<String> merCodeList = merchantLists.stream().map(t -> t.getMerCode().trim()).collect(Collectors.toList());
                Map<String, Object> accountParams = new HashMap<>();
                accountParams.put("orgId", criteria.getOrgId());
                accountParams.put("merCode", String.join(",", merCodeList));
                accountParams.put("timestamp", new Date().getTime() / 1000);
                accountParams.put("sign", SignUtil.getSign(accountParams, KeyPathConstant.OPP_KEY));
                // 查询内容
                System.out.println("url" + dataServerProperties.getOppUrl() + "/merchant" + "?" + SignUtil.mapToUrlParamsEncoding(accountParams));
                HttpRequest accountResRequestList = HttpUtil.createGet(dataServerProperties.getOppUrl() + "/merchant" + "?" + SignUtil.mapToUrlParamsEncoding(accountParams));
                HttpResponse accountResponseList = accountResRequestList.execute();
                System.out.println("body" + accountResponseList.body());
                JSONObject accountRes = JSON.parseObject(accountResponseList.body());
                JSONObject data = accountRes.getJSONObject("data");
                JSONArray list = data.getJSONArray("list");
                List<OppMerchantDto> oppMerchantList = JSON.parseArray(list.toJSONString(), OppMerchantDto.class);
                Map<String, OppMerchantDto> oppMerchantMap = oppMerchantList.stream().collect(Collectors.toMap(OppMerchantDto::getMerCode, oppMerchantDto -> oppMerchantDto, (oldObj, newObj) -> oldObj));
                List<OppMerchantDto> resList = new ArrayList<>();
                for (MerchantList merchantList : merchantLists) {
                    OppMerchantDto oppMerchantDto = new OppMerchantDto();
                    oppMerchantDto.setMerCode(merchantList.getMerCode().trim());
                    oppMerchantDto.setMerName(merchantList.getMerName());
                    oppMerchantDto.setGateway(merchantList.getGateway());
                    oppMerchantDto.setType(getType(merchantList.getGateway()));
                    oppMerchantDto.setEffective("0");
                    if (oppMerchantMap.containsKey(oppMerchantDto.getMerCode())) {
                        OppMerchantDto oppMerchant = oppMerchantMap.get(oppMerchantDto.getMerCode());
                        oppMerchantDto.setEffective("1");
                        oppMerchantDto.setOrgId(oppMerchant.getOrgId());
                        oppMerchantDto.setStatus(oppMerchant.getStatus());
                        oppMerchantDto.setId(oppMerchant.getId());
                    }
                    resList.add(oppMerchantDto);
                }
                return PageUtil.toPage(resList, resCountJson.getInteger("data"));
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String getType(String gateway) {
        if (StringUtils.isEmpty(gateway)) {
            return null;
        }
        switch (gateway) {
            case "upop":
                return "pos";
            case "wechat":
            case "Alipay":
                return "online";
            default:
                return null;
        }
    }

    /**
     * 获取一个的OPP交易机构的密钥
     *
     * @param orgId
     * @return
     */
    @Override
    public String privateKey(String orgId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("orgId", orgId);
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getOppUrl() + "/organization/privateKey" + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            JSONObject res = JSON.parseObject(responseList.body());
            if (res.getInteger("code") != 0) {
                throw new CustomException(400, res.getString("message"));
            }
            JSONObject data = res.getJSONObject("data");
            return data.getString("privateKey");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 重置一个的OPP交易机构的密钥
     *
     * @param merchantResetKey
     * @return
     */
    @Override
    public String resetPrivateKey(OppMerchantResetKey merchantResetKey) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("orgId", merchantResetKey.getOrgId());
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createRequest(Method.PUT, dataServerProperties.getOppUrl() + "/organization/resetkey");
            String body = JSON.toJSONString(params);
            requestList.header("Accept", "*/*").body(body);
            HttpResponse responseList = requestList.execute();
            JSONObject res = JSON.parseObject(responseList.body());
            JSONObject data = res.getJSONObject("data");
            return data.getString("privateKey");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建
     *
     * @param create
     * @return
     */
    @Override
    public RestResult create(OppMerchantAdd create) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("merCode", create.getMerCode());
            params.put("orgId", create.getOrgId());
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createRequest(Method.POST, dataServerProperties.getOppUrl() + "/merchant");
            String body = JSON.toJSONString(params);
            requestList.body(body);
            HttpResponse responseList = requestList.execute();
            JSONObject res = JSON.parseObject(responseList.body());
            if (res.getInteger("code") != 0) {
                throw new BadRequestException(res.getString("message"));
            }
            return RestUtil.ok();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新
     * @param update
     * @param id
     * @return
     */
    @Override
    public RestResult update(OppMerchantUpdate update, Integer id) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("merCode", update.getMerCode());
            params.put("status", update.getStatus());
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createRequest(Method.PATCH, dataServerProperties.getOppUrl() + "/merchant/" + id);
            requestList.body(JSON.toJSONString(params));
            HttpResponse responseList = requestList.execute();
            JSONObject res = JSON.parseObject(responseList.body());
            if (res.getInteger("code") != 0) {
                throw new BadRequestException(res.getString("message"));
            }
            return RestUtil.ok();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 移除可用的OPP商户
     *
     * @param delete
     * @return
     */
    @Override
    public RestResult delete(OppMerchantAdd delete) {
        try {
            PageResult<OppMerchantDto> merchantPage = merchant(new OppMerchantCriteria(delete.getMerCode(),delete.getOrgId()), Pageable.ofSize(1));
            List<OppMerchantDto> merchantList = merchantPage.getData();
            if (merchantList.isEmpty()) {
                return RestUtil.toRest(RestEnum.OOP_NOT_EXIST);
            }
            OppMerchantDto merchant = merchantList.get(0);
            if (StringUtils.isEmpty(merchant.getId())) {
                return RestUtil.toRest(RestEnum.MERCHANT_NOT_CREATED);
            }
            Map<String, Object> params = new HashMap<>();
            params.put("merCode", delete.getMerCode());
            params.put("orgId", delete.getOrgId());
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            // 查询内容
            HttpRequest requestList = HttpUtil.createRequest(Method.DELETE, dataServerProperties.getOppUrl() + "/merchant/" + merchant.getId() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();
            JSONObject res = JSON.parseObject(responseList.body());
            if (res.getInteger("code") != 0) {
                throw new BadRequestException(res.getString("message"));
            }
            return RestUtil.ok();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取Code
     *
     * @return
     */
    @Override
    public JSONObject getCode() {
        try {
            Map<String, Object> params = new TreeMap<>();
            params.put("name", "exampleCode");
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getOppUrl() + "/file/download" + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestList.execute();
            byte[] fileContent = responseCount.bodyBytes();
            return new JSONObject().fluentPut("code", new String(fileContent));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 技术文档下载
     *
     * @return
     */
    @Override
    public ResponseEntity<StreamingResponseBody> technicalManualDownload() {
        try {
            Map<String, Object> params = new TreeMap<>();
            params.put("name", "exampleCode");
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.OPP_KEY));
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getOppUrl() + "/file/download" + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestList.execute();
            byte[] fileContent = responseCount.bodyBytes();
            InputStream inputStream = new ByteArrayInputStream(fileContent);

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "technicalManual.txt" + "\""); // You can set the filename as needed
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            StreamingResponseBody responseBody = outputStream -> {
                int numberOfBytesToWrite;
                byte[] data = new byte[1024];
                while ((numberOfBytesToWrite = inputStream.read(data, 0, data.length)) != -1) {
                    outputStream.write(data, 0, numberOfBytesToWrite);
                }
                inputStream.close();
            };

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(fileContent.length)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(responseBody);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
