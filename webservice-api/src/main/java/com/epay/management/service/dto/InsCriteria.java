package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@Setter
@AllArgsConstructor
public class InsCriteria implements Serializable {

    public InsCriteria() {
    }

    public InsCriteria(String code) {
        this.insCode = code;
    }

    private String searchKeyword;

    private String insCode;
    
    private String orgId;

    private String merCode;

    private String insName;

    private String termCode;

    private String insCountry;

}
