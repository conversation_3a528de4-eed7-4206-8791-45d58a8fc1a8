package com.epay.management.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epay.base.login.JwtUserDto;
import com.epay.config.KeyPathConstant;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.BadRequestException;
import com.epay.exception.CustomException;
import com.epay.management.domain.Merchant;
import com.epay.management.domain.MerchantInfoDetail;
import com.epay.management.domain.TransactionList;
import com.epay.management.enums.GatewayEnum;
import com.epay.management.enums.IsUpopMerchantEnum;
import com.epay.management.enums.TransactionPaymentMethodEnum;
import com.epay.management.service.MerchantService;
import com.epay.management.service.TransactionService;
import com.epay.management.service.dto.RefundReq;
import com.epay.management.service.dto.TransactionCriteria;
import com.epay.management.utils.RefundNoGenerator;
import com.epay.management.utils.SupportFuncUtil;
import com.epay.management.utils.TransactionUtil;
import com.epay.pay.utils.PayUtil;
import com.epay.user.domain.UcUserMerchantPossess;
import com.epay.user.service.dto.*;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交易数据管理
 *
 * <AUTHOR>
 * @date 2024/4/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionServiceImpl implements TransactionService {

    private final DataServerProperties dataServerProperties;
    private final MerchantService merchantService;
    private final PasswordEncoder passwordEncoder;
    private final RefundNoGenerator refundNoGenerator;

    /**
     * realtime
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<TransactionList> realtime(TransactionCriteria criteria, Pageable pageable) {
        log.info("realtime查询开始 - 请求参数: {}", JSON.toJSONString(criteria));
        if (!"realtime".equals(criteria.getType()) && !"history".equals(criteria.getType()) && !"all".equals(criteria.getType())) {
            throw new BadRequestException(RestEnum.TYPE_ERROR.getMessage());
        }
        SecurityUtils.getCurrentLoginUser();
        List<String> merCodes = new ArrayList<>();
        JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
        if ("realtime".equals(criteria.getType()) && !currentUser.getPermissions().contains("A200020011")) {
            throw new CustomException(HttpStatus.FORBIDDEN.value(), "not operation permission！");
        }
        if ("history".equals(criteria.getType()) && !currentUser.getPermissions().contains("A200020013")) {
            throw new CustomException(HttpStatus.FORBIDDEN.value(), "not operation permission！");
        }
        for (String key : currentUser.getAuthMap().keySet()) {
            if ("realtime".equals(criteria.getType()) && currentUser.getAuthMap().get(key).contains("A200020011")) {
                merCodes.add(key);
            }
            if ("history".equals(criteria.getType()) && currentUser.getAuthMap().get(key).contains("A200020013")) {
                merCodes.add(key);
            }
            if ("all".equals(criteria.getType()) && currentUser.getAuthMap().get(key).contains("A200020011") && currentUser.getAuthMap().get(key).contains("A200020013")) {
                merCodes.add(key);
            }
        }
        try {
            if (StringUtils.isNotEmpty(criteria.getTermCode())) {
                // 处理treminalNo条件
                Map<String, Object> params = new HashMap<>();
                params.put("timestamp", new Date().getTime() / 1000);
                params.put("table", "TBL_TERM_INFO");
                Map<String, String> condition = new HashMap<>();
                if (StringUtils.isNotEmpty(criteria.getTermCode())) {
                    condition.put("TERM_CODE", criteria.getTermCode());
                }
                params.put("condition", JSON.toJSONString(condition));
                params.put("count", 1);
                params.put("skip", 0);
                params.put("limit", Integer.MAX_VALUE);
                params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));

                // 查询内容
                HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
                HttpResponse responseList = requestList.execute();

                // 查询统计
                JSONObject resListJson = JSON.parseObject(responseList.body());
                JSONArray data = resListJson.getJSONArray("data");
                List<String> merCodeList = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    merCodeList.add(data.getJSONObject(i).getString("MER_CODE").trim());
                }
                merCodes.retainAll(merCodeList);
            }

            if (StringUtils.isNotEmpty(criteria.getMenuType())) {
                // 处理treminalNo条件
                Map<String, Object> params = new HashMap<>();
                params.put("timestamp", new Date().getTime() / 1000);
                params.put("table", "TBL_MER_INFO");
                Map<String, String> condition = new HashMap<>();
                condition.put("MER_CODE", "$in(" + String.join(",", merCodes) + ")");
                if (StringUtils.isNotEmpty(criteria.getMenuType())) {
                    if ("online".equals(criteria.getMenuType())) {
                        condition.put("IS_UPOP_MERCHANT", "$notin(0)");
                    } else if ("offline".equals(criteria.getMenuType())) {
                        condition.put("IS_UPOP_MERCHANT", "0");
                    }
                }
                params.put("condition", JSON.toJSONString(condition));
                params.put("count", 1);
                params.put("skip", 0);
                params.put("limit", Integer.MAX_VALUE);
                params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));

                // 查询内容
                HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
                HttpResponse responseList = requestList.execute();

                // 查询统计
                JSONObject resListJson = JSON.parseObject(responseList.body());
                JSONArray data = resListJson.getJSONArray("data");
                List<String> merCodeList = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    merCodeList.add(data.getJSONObject(i).getString("MER_CODE").trim());
                }
                merCodes.retainAll(merCodeList);
            }

            if (merCodes.isEmpty()) {
                return PageUtil.noData();
            }
//            if (StringUtils.isNotEmpty(criteria.getTransactionType())) {
//                MerchantDetailCriteria merchantCriteria = new MerchantDetailCriteria();
//                merchantCriteria.setType(criteria.getTransactionType());
//                merchantCriteria.setMerCodes(merCodes);
//                List<MerchantInfoDetail> merchantDetaiList = merchantService.merchantDetailLi
//                st(merchantCriteria);
//                if (!merchantDetaiList.isEmpty()) {
//                    List<String> merCodeList = merchantDetaiList.stream().map(MerchantInfoDetail::getMerCode).collect(Collectors.toList());
//                    merCodes.retainAll(merCodeList);
//                }
//            }
//            MerchantCriteria merchantCriteria = new MerchantCriteria();
//            merchantCriteria.setMerCodes();
//            merchantService.merchant(merCodeList);
            // 存储请求数据
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            JSONObject condition = new JSONObject();
            condition.put("merCode", "$in(" + String.join(",", merCodes) + ")");
            if (StringUtils.isNotEmpty(criteria.getInsCode())) {
                condition.put("insCode", "$like(%" + criteria.getInsCode() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getStatus())) {
                condition.put("status", criteria.getStatus());
            }
            if (StringUtils.isNotEmpty(criteria.getTransactionType())) {
                String transactionType = getTransactionType(criteria);
                if (StringUtils.isNotEmpty(transactionType)) {
                    condition.put("isUpopMerchant", transactionType);
                }
            }
            if (StringUtils.isNotEmpty(criteria.getMerCode())) {
                boolean flag = false;
                for (String merCode : merCodes) {
                    if (merCode.contains(criteria.getMerCode())) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    return PageUtil.noData();
                }
                if (condition.containsKey("merCode")) {
                    condition.put("@merCode", "$like(%" + criteria.getMerCode() + "%)");
                } else {
                    condition.put("merCode", "$like(%" + criteria.getMerCode() + "%)");
                }
            }
            if (StringUtils.isNotEmpty(criteria.getSearchKeyword())) {
                if (DateUtil.isValidDate(criteria.getSearchKeyword())) {
//                    condition.put("createTime", "$eq(" + criteria.getSearchKeyword() + ")");
                    condition.put("createTime", "$gte(" + DateUtil.getStartOfDay(criteria.getSearchKeyword()) + ")");
                    condition.put("@createTime", "$lte(" + DateUtil.getEndOfDay(criteria.getSearchKeyword()) + ")");
                } else {
                    condition.put("merCode", "$like(%" + criteria.getSearchKeyword() + "%)");
                }
            }

            if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                condition.put("tradeType", criteria.getPaymentType());
            }
            if (StringUtils.isNotEmpty(criteria.getStartDate())) {
                condition.put("createTime", "$gte(" + criteria.getStartDate() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getVoucherNo())) {
                condition.put("voucherNo", "$like(%" + criteria.getVoucherNo() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getEndDate())) {
                condition.put("createTime", "$lte(" + criteria.getEndDate() + ")");
            }
            if (StringUtils.isNotEmpty(criteria.getCardNo())) {
                condition.put("acctNum", "$like(%" + criteria.getCardNo() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getReferenceNo())) {
                condition.put("referenceNo", "$like(%" + criteria.getReferenceNo() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getOrderNo())) {
                condition.put("orderNo", "$like(%" + criteria.getOrderNo() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getTraceNo())) {
                condition.put("traceNo", "$like(%" + criteria.getTraceNo() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getTraceType())) {
                condition.put("traceType", criteria.getTraceType());
            }
            if (StringUtils.isNotEmpty(criteria.getTermCode())) {
                condition.put("termCode", "$like(%" + criteria.getTermCode() + "%)");
            }
            if (StringUtils.isNotEmpty(criteria.getCardNo())) {
                condition.put("cardNo", "$like(%" + criteria.getCardNo() + "%)");
            }
            if ("realtime".equals(criteria.getType())) {
                if (TransactionPaymentMethodEnum.UNION_PAY.getName().equals(criteria.getPaymentMethod())) {
                    params.put("ignoreTables", "TBL_WECHAT_ONLINE_TXN,TBL_ALIPAY_ONLINE_TXN");
                } else if (TransactionPaymentMethodEnum.ALIPAY.getName().equals(criteria.getPaymentMethod())) {
                    params.put("ignoreTables", "TBL_DIRECT_POS_A,TBL_DIRECT_POS_B,TBL_TRANS_LOG,TBL_ONLINE_TXN_A,TBL_ONLINE_TXN_B,TBL_WECHAT_ONLINE_TXN");
                } else if (TransactionPaymentMethodEnum.WECHAT.getName().equals(criteria.getPaymentMethod())) {
                    params.put("ignoreTables", "TBL_DIRECT_POS_A,TBL_DIRECT_POS_B,TBL_TRANS_LOG,TBL_ONLINE_TXN_A,TBL_ONLINE_TXN_B,TBL_ALIPAY_ONLINE_TXN");
                }
            }
            if ("history".equals(criteria.getType())) {
                if (TransactionPaymentMethodEnum.UNION_PAY.getName().equals(criteria.getPaymentMethod())) {
                    params.put("ignoreTables", "TBL_WECHAT_ONLINE_TXN,TBL_ALIPAY_ONLINE_TXN");
                } else if (TransactionPaymentMethodEnum.ALIPAY.getName().equals(criteria.getPaymentMethod())) {
                    params.put("ignoreTables", "TBL_DIRECT_POS_HISTORY_A,TBL_DIRECT_POS_HISTORY_B,TBL_TRANS_LOG_HIS,TBL_ONLINE_TXN_A,TBL_ONLINE_TXN_B,TBL_WECHAT_ONLINE_TXN");
                } else if (TransactionPaymentMethodEnum.WECHAT.getName().equals(criteria.getPaymentMethod())) {
                    params.put("ignoreTables", "TBL_DIRECT_POS_HISTORY_A,TBL_DIRECT_POS_HISTORY_B,TBL_TRANS_LOG_HIS,TBL_ONLINE_TXN_A,TBL_ONLINE_TXN_B,TBL_ALIPAY_ONLINE_TXN");
                }
            }
            params.put("condition", JSON.toJSONString(condition));
            for (Sort.Order order : pageable.getSort()) {
                params.put("sort", order.getProperty());
                params.put("sortType", order.getDirection().name());
            }
            params.put("total", 1);
            params.put("select", criteria.getType());
            params.put("startRow", pageable.getOffset() + 1);
            params.put("endRow", pageable.getOffset() + pageable.getPageSize());
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));

            log.info("查询条件: {}", JSON.toJSONString(condition));
            log.info("请求参数: {}", JSON.toJSONString(params));

            // 查询内容
            String transactionQueryUrl = dataServerProperties.getTransactionUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params);
            log.info("请求URL: {}", transactionQueryUrl);
            HttpRequest requestList = HttpUtil.createGet(transactionQueryUrl);
            HttpResponse responseList = requestList.execute();
            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            log.info("响应结果: {}", resListJson.toJSONString());

            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0) {
                JSONObject resData = JSON.parseObject(resListJson.getString("data"));
                // 创建临时的ParserConfig对象
                List<TransactionList> res = JSON.parseArray(resData.getString("list"), TransactionList.class);
                StringUtils.trimStringsInList(res);
                List<String> merCodeList = res.stream().map(TransactionList::getMerCode).collect(Collectors.toList());
                List<MerchantInfoDetail> merchantList = merchantService.merchantDetailList(merCodeList);
                Map<String, String> merchantMap = merchantList.stream().collect(Collectors.toMap(t -> t.getMerCode().trim(), t -> t.getMerType()));
                for (TransactionList transaction : res) {
                    transaction.setPaymentMethod(GatewayEnum.getDescByName(transaction.getInsProp()));
                    transaction.setTraceType(TransactionUtil.getTradeType(transaction.getTraceType(), transaction.getPaymentMethod(), IsUpopMerchantEnum.getDescByCode(transaction.getIsUpopMerchant())));
                    transaction.setType(merchantMap.get(transaction.getMerCode()));
                    transaction.setCardNo(SensitiveUtil.maskNumber(transaction.getCardNo()));
                }
                log.info("查询成功，返回记录数: {}", resData.getInteger("total"));
                return PageUtil.toPage(res, resData.getInteger("total"));
            } else {
                log.error("查询失败: code={}, message={}", resListJson.getInteger("code"), resListJson.getString("message"));
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            log.error("realtime查询异常: {}", e.getMessage(), e);
            throw new BadRequestException(e.getMessage());
        }
    }

//    private static String handleProp(TransactionList transaction) {
//        String payment = GatewayEnum.getDescByName(transaction.getInsProp());
//        String merchantType = IsUpopMerchantEnum.getDescByCode(transaction.getIsUpopMerchant());
//        if (GatewayEnum.UNION.getDesc().equals(payment)) {
//            if (IsUpopMerchantEnum.POS.getDesc().equals(merchantType)) {

    /// /                transaction.setTermTraceNum();
//            }
//            if (IsUpopMerchantEnum.Online.getDesc().equals(merchantType)) {
//
//            }
//            if (IsUpopMerchantEnum.APP.getDesc().equals(merchantType)) {
//
//            }
//        }
//        if (GatewayEnum.WECHAT.getDesc().equals(payment)) {
//            if (IsUpopMerchantEnum.POS.getDesc().equals(merchantType)) {
//
//            }
//            if (IsUpopMerchantEnum.Online.getDesc().equals(merchantType)) {
//
//            }
//            if (IsUpopMerchantEnum.APP.getDesc().equals(merchantType)) {
//
//            }
//        }
//        if (GatewayEnum.ALIPAY.getDesc().equals(payment)) {
//            if (IsUpopMerchantEnum.POS.getDesc().equals(merchantType)) {
//
//            }
//            if (IsUpopMerchantEnum.Online.getDesc().equals(merchantType)) {
//
//            }
//            if (IsUpopMerchantEnum.APP.getDesc().equals(merchantType)) {
//
//            }
//        }
//        return "";
//    }
    private static String getTransactionType(TransactionCriteria criteria) {
        String type = "";
        if ("pos".equals(criteria.getTransactionType())) {
            type = "0";
        } else if ("npp".equals(criteria.getTransactionType())) {
            type = "8";
        } else if ("online".equals(criteria.getTransactionType())) {
            type = "$in(1,5,6)";
        } else if ("nbl".equals(criteria.getTransactionType())) {
            type = "$in(2,5,6)";
        } else if ("app".equals(criteria.getTransactionType())) {
            type = "9";
        }
        return type;
    }

    /**
     * refund
     *
     * @param refundReq
     * @return
     */
    @Override
    public RestResult refund(RefundReq refundReq) {
        try {
            // 密码校验
            if (!passwordEncoder.matches(refundReq.getPassword(), SecurityUtils.getCurrentLoginUser().getPassword())) {
                return RestUtil.toRest(RestEnum.PASSWORD_ERR);
            }
            // 查询交易数据
            PageResult<TransactionList> page = realtime(new TransactionCriteria(refundReq.getType(), refundReq.getOrderNo()), PageRequest.of(0, 1));
            List<TransactionList> list = page.getData();
            if (list.isEmpty()) {
                return RestUtil.toRest(RestEnum.TRANSACTION_NOT_EXIST);
            }
            // 校验商户权限
            JwtUserDto currentUser = (JwtUserDto) SecurityUtils.getCurrentUser();
            List<String> merCodes = new ArrayList<>();
            for (String key : currentUser.getAuthMap().keySet()) {
                if ("history".equals(refundReq.getType()) && currentUser.getAuthMap().get(key).contains("A200020014")) {
                    merCodes.add(key);
                }
                if ("realtime".equals(refundReq.getType()) && currentUser.getAuthMap().get(key).contains("A200020014")) {
                    merCodes.add(key);
                }
            }
            if (!merCodes.contains(list.get(0).getMerCode())) {
                throw new CustomException(HttpStatus.FORBIDDEN.value(), "not permission！");
            }
            // 操作退款
            // 查询商户信息
            Merchant merchant = merchantService.merchantInfo(list.get(0).getMerCode());
            if (merchant == null) {
                return RestUtil.toRest(RestEnum.MER_CODE_NOT_EXIST);
            }
            // 校验交易
            TransactionList transactionList = list.get(0);
            if (!SupportFuncUtil.isRefund(transactionList.getSupportFuncFlag(),transactionList.getPaymentMethod())) {
                return RestUtil.toRest(RestEnum.TRANSACTION_NOT_REFUND);
            }
            if (TransactionPaymentMethodEnum.WECHAT.getName().equals(transactionList.getPaymentMethod()) ||
                    TransactionPaymentMethodEnum.ALIPAY.getName().equals(transactionList.getPaymentMethod())) {
                return getWechatAliResult(refundReq, transactionList, PayUtil.getKey(merchant.getMerKey()));
            } else {
                return getUniPayResult(refundReq, transactionList, PayUtil.getKey(merchant.getMerKey()));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    private RestResult getUniPayResult(RefundReq refundReq, TransactionList transactionList, String merKey) {
        Map<String, Object> reqMap = new TreeMap<>();
        // 退款订单号
        reqMap.put("out_refund_no", "ER" + refundNoGenerator.generateUniqueRefundNo());
        // 原交易订单号
        reqMap.put("out_trade_no", transactionList.getOrderNo());
        // 退款金额
        reqMap.put("refund_fee", String.valueOf(refundReq.getAmount().multiply(new BigDecimal(100)).intValue()));
        // 退款币种
        reqMap.put("refund_fee_type", transactionList.getCurrency());
        // 交易ID
        reqMap.put("transaction_id", transactionList.getReferenceNo());
        // 版本号
        reqMap.put("msg_version", "1.2");
        // 接口类型
        reqMap.put("service", TransactionUtil.getRefund(GatewayEnum.getDescByName(transactionList.getInsProp())));
        // 商户号
        reqMap.put("sub_mch_id", transactionList.getMerCode().trim());
        // 通知地址
        reqMap.put("notify_url", "http://opptest.hejhej.top/api/payment/notify");
        // 字元集
        // 签名
        reqMap.put("sign", Md5Utils.md5Sign(reqMap, merKey));
        // 签名方式
        reqMap.put("sign_type", "MD5");

        // 查询内容
        HttpRequest request = HttpUtil.createPost(dataServerProperties.getRefundUrl());
        log.info("refund request: {}", JSON.toJSONString(reqMap));
        request.body(JSONObject.toJSONString(reqMap));
        HttpResponse response = request.execute();
        // 处理结果
        JSONObject resListJson = JSON.parseObject(response.body());
        log.info("refund response: {}", resListJson.toJSONString());
        // 判断请求是否成功
        if (resListJson.containsKey("returncode") && resListJson.getString("returncode").equals("FAIL")) {
            throw new BadRequestException(resListJson.getString("returnmsg"));
        }
        if ("SUCCESS".equals(resListJson.getString("result_code"))) {
            return RestUtil.toRest(RestEnum.SUCCESS);
        } else {
            throw new BadRequestException(resListJson.getString("err_code_des"));
        }
    }


    private RestResult getWechatAliResult(RefundReq refundReq, TransactionList transactionList, String merKey) {
        Map<String, Object> reqMap = new TreeMap<>();
        // 接口类型
        reqMap.put("service", TransactionUtil.getRefund(GatewayEnum.getDescByName(transactionList.getInsProp())));
        // 商户号
        reqMap.put("sub_mch_id", transactionList.getMerCode().trim());
        // 设备终端号
        reqMap.put("device_info", PayUtil.getDeviceInfo(SecurityUtils.getCurrentLoginUser().getUserId(), "billpay"));
        // 原消费金额
        reqMap.put("total_fee", String.valueOf(transactionList.getAmount().intValue()));
        // 交易币种
        reqMap.put("currency", transactionList.getCurrency());
        // 原交易订单号
        reqMap.put("out_trade_no", transactionList.getOrderNo());
        // 退款订单号
        reqMap.put("out_refund_no", "ER" + refundNoGenerator.generateUniqueRefundNo());
        // 退款金额
        reqMap.put("refund_fee", String.valueOf(refundReq.getAmount().multiply(new BigDecimal(100)).intValue()));
        // 退款币种
        reqMap.put("refund_fee_type", transactionList.getCurrency());
        // 通知地址
        reqMap.put("notify_url", "http://opptest.hejhej.top/api/payment/notify");
        // 版本号
        reqMap.put("msg_version", "1.2");
        // 字元集
        reqMap.put("charset", "UTF-8");
        // 签名
        reqMap.put("sign", Md5Utils.md5Sign(reqMap, merKey));

        // 查询内容
        HttpRequest request = HttpUtil.createPost(dataServerProperties.getRefundUrl());
        log.info("refund request: {}", JSON.toJSONString(reqMap));
        request.body(JSONObject.toJSONString(reqMap));
        HttpResponse response = request.execute();
        // 处理结果
        JSONObject resListJson = JSON.parseObject(response.body());
        log.info("refund response: {}", resListJson.toJSONString());
        // 判断请求是否成功
        if (resListJson.containsKey("returncode") && resListJson.getString("returncode").equals("FAIL")) {
            throw new BadRequestException(resListJson.getString("returnmsg"));
        }
        if ("SUCCESS".equals(resListJson.getString("result_code"))) {
            return RestUtil.toRest(RestEnum.SUCCESS);
        } else {
            throw new BadRequestException(resListJson.getString("err_code_des"));
        }
    }

}
