package com.epay.management.service;

import com.epay.management.domain.ReportList;
import com.epay.management.service.dto.ReportCriteria;
import com.epay.management.service.dto.ReportFile;
import com.epay.utils.PageResult;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
public interface ReportService {

    /**
     * 报表功能
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> data(ReportCriteria criteria, Pageable pageable);

    /**
     * 报表文件下载
     * @param file
     * @return
     */
    ResponseEntity<StreamingResponseBody> file(ReportFile file);

    /**
     * 报表预览
     * @param file
     * @return
     */
    void preview(HttpServletResponse response, ReportFile file);
}
