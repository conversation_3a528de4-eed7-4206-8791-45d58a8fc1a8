package com.epay.management.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Data
@AllArgsConstructor
public class MerchantCriteria {

	public MerchantCriteria() {
	}

	public MerchantCriteria(String merCode) {
		this.merCode = merCode;
	}

	public MerchantCriteria(List<String> merCodes) {
		this.merCodes = merCodes;
	}

	private String searchKeyword;

	private String insCode;
	private String merCode;
	private List<String> merCodes;
	private String merName;
	private String orgId;
	private String userId;
	private String orgName;
	private String merStatus;
	private String type;
	private String paymentType;
	private String startTime;
	private String endTime;
	private Boolean isOrgBind;
	private Boolean isTerminalCount = true;


}
