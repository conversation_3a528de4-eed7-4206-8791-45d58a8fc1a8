package com.epay.management.service.impl;

import cn.hutool.json.JSONObject;
import com.epay.management.domain.MpsPermission;
import com.epay.management.domain.MpsRole;
import com.epay.management.domain.MpsRolePermissionRelation;
import com.epay.management.repository.MpsPermissionRepository;
import com.epay.management.repository.MpsRolePermissionRelationRepository;
import com.epay.management.repository.MpsRoleRepository;
import com.epay.management.service.MpsRoleService;
import com.epay.management.service.dto.*;
import com.epay.management.service.mapstruct.MpsPermissionMapper;
import com.epay.management.service.mapstruct.MpsRoleAddUpdateMapper;
import com.epay.management.service.mapstruct.MpsRoleMapper;
import com.epay.utils.*;
import com.epay.utils.enums.RestEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.Transient;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Service
@RequiredArgsConstructor
public class MpsRoleServiceImpl implements MpsRoleService {

    private final MpsRoleRepository roleRepository;
    private final MpsRolePermissionRelationRepository rolePermissionRelationRepository;
    private final MpsPermissionRepository permissionRepository;
    private final MpsRoleAddUpdateMapper roleAddUpdateMapper;
    private final MpsRoleMapper managerMapper;
    private final MpsPermissionMapper permissionMapper;

    /**
     * 查询角色
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public PageResult<MpsPermissionDto> queryAll(MpsRoleCriteria criteria, Pageable pageable) {
        Page<MpsPermission> page = permissionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(permissionMapper::toDto));
    }

    /**
     * 修改用户角色的权限信息
     *
     * @param update
     * @param id
     * @return
     */
    @Override
    @Transient
    public RestResult update(MpsRolePermissionAddUpdate update, Integer id) {
        Optional<MpsRole> role = roleRepository.findById(id);
        if (!role.isPresent()) {
            return RestUtil.toRest(RestEnum.ROLE_NOT_EXIST);
        }
        MpsRole mpsRole = role.get();
        rolePermissionRelationRepository.deleteAll(rolePermissionRelationRepository
                .findAll((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("roleId"), mpsRole.getRoleId())));
        // 添加权限关联
        for (String permissionCode : update.getPermissionCode().split(",")) {
            MpsRolePermissionRelation rolePermissionRelation = new MpsRolePermissionRelation();
            rolePermissionRelation.setRoleId(mpsRole.getRoleId());
            rolePermissionRelation.setPermissionCode(permissionCode);
            rolePermissionRelationRepository.save(rolePermissionRelation);
        }
        return RestUtil.ok();
    }

    /**
     * 创建角色
     *
     * @param create
     * @return
     */
    @Override
    public RestResult create(MpsRoleAddUpdate create) {
        // 校验名称是否存在
        Optional<MpsRole> role = roleRepository.findOne((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("roleName"), create.getRoleName()));
        if (role.isPresent()) {
            return RestUtil.toRest(RestEnum.ROLE_IS_EXIST);
        }
        // 创建
        MpsRole insert = roleAddUpdateMapper.toEntity(create);
        insert.setRoleId(IdGenerator.generateUniqueCode());
        roleRepository.save(insert);
        return RestUtil.ok(new JSONObject().set("roleId", insert.getRoleId()));
    }

    /**
     * 删除角色
     *
     * @param id
     */
    @Override
    @Transactional
    public void delete(Integer id) {
        List<MpsRole> managers = roleRepository.findAll((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("id"), id));
        for (MpsRole manager : managers) {
            manager.setValid(0);
        }
        roleRepository.saveAll(managers);
        // 删除权限关联
        List<MpsRolePermissionRelation> rolePermissionRelations = rolePermissionRelationRepository
                .findAll((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("roleId"), managers.get(0).getRoleId()));
        for (MpsRolePermissionRelation rolePermissionRelation : rolePermissionRelations) {
            rolePermissionRelation.setValid(0);
        }
        rolePermissionRelationRepository.saveAll(rolePermissionRelations);
    }

}
