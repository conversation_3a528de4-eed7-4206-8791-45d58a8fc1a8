package com.epay.analytic.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分析历史
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
public class AnalyticHistory implements Serializable {


    private String merCode;

    private String merName;

    private String channel;

    private String settDate;

    private String acqInsCode;

    private BigDecimal merSaleAmt;

    private Integer transNum;

    private String paymentMethod;

    private String transactionCurrency;

    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "SETT_DATE")
    public void setSettDate(String settDate) {
        this.settDate = settDate;
    }

    @JSONField(name = "ACQ_INS_CODE")
    public void setAcqInsCode(String acqInsCode) {
        this.acqInsCode = acqInsCode;
    }

    @JSONField(name = "MER_SALE_AMT")
    public void setMerSaleAmt(BigDecimal merSaleAmt) {
        this.merSaleAmt = merSaleAmt;
    }

    @JSONField(name = "TRANS_NUM")
    public void setTransNum(Integer transNum) {
        this.transNum = transNum;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setTransactionCurrency(String transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }
}
