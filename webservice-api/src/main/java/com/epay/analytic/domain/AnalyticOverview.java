package com.epay.analytic.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.epay.base.BaseEntity;
import lombok.Getter;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 分析概览
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
public class AnalyticOverview extends BaseEntity implements Serializable {


    private String merCode;

    private String merName;

    private String channel;

    private String settDate;

    private String acqInsCode;

    private String transCurr;

    private BigDecimal acceptSaleTransAmt1;

    private BigDecimal acceptSaleTransAmt2;

    private BigDecimal acceptRefundTransAmt1;

    private BigDecimal acceptRefundTransAmt2;

    private Integer acceptSaleNum1;

    private Integer acceptSaleNum2;

    private Integer acceptRefundNum1;

    private Integer acceptRefundNum2;

    private Integer acceptAuthNum1;

    private Integer acceptAuthNum2;

    private BigDecimal saleAmount;

    private BigDecimal refundAmount;

    private BigDecimal natAmount;

    private Integer saleNum;

    private String paymentMethod;

    private String transactionCurrency;

    private Map<String,Integer> typeMap;

    @JSONField(name = "MER_CODE")
    public void setMerCode(String merCode) {
        this.merCode = merCode;
    }

    @JSONField(name = "SETT_DATE")
    public void setSettDate(String settDate) {
        this.settDate = settDate;
    }

    @JSONField(name = "ACQ_INS_CODE")
    public void setAcqInsCode(String acqInsCode) {
        this.acqInsCode = acqInsCode;
    }

    @JSONField(name = "TRANS_CURR")
    public void setTransCurr(String transCurr) {
        this.transCurr = transCurr;
    }

    @JSONField(name = "ACCEPT_SALE_TRANS_AMT_1")
    public void setAcceptSaleTransAmt1(BigDecimal acceptSaleTransAmt1) {
        this.acceptSaleTransAmt1 = acceptSaleTransAmt1;
    }

    @JSONField(name = "ACCEPT_SALE_TRANS_AMT_2")
    public void setAcceptSaleTransAmt2(BigDecimal acceptSaleTransAmt2) {
        this.acceptSaleTransAmt2 = acceptSaleTransAmt2;
    }

    @JSONField(name = "ACCEPT_REFUND_TRANS_AMT_1")
    public void setAcceptRefundTransAmt1(BigDecimal acceptRefundTransAmt1) {
        this.acceptRefundTransAmt1 = acceptRefundTransAmt1;
    }

    @JSONField(name = "ACCEPT_REFUND_TRANS_AMT_2")
    public void setAcceptRefundTransAmt2(BigDecimal acceptRefundTransAmt2) {
        this.acceptRefundTransAmt2 = acceptRefundTransAmt2;
    }

    @JSONField(name = "ACCEPT_SALE_NUM_1")
    public void setAcceptSaleNum1(Integer acceptSaleNum1) {
        this.acceptSaleNum1 = acceptSaleNum1;
    }

    @JSONField(name = "ACCEPT_SALE_NUM_2")
    public void setAcceptSaleNum2(Integer acceptSaleNum2) {
        this.acceptSaleNum2 = acceptSaleNum2;
    }

    @JSONField(name = "ACCEPT_REFUND_NUM_1")
    public void setAcceptRefundNum1(Integer acceptRefundNum1) {
        this.acceptRefundNum1 = acceptRefundNum1;
    }

    @JSONField(name = "ACCEPT_REFUND_NUM_2")
    public void setAcceptRefundNum2(Integer acceptRefundNum2) {
        this.acceptRefundNum2 = acceptRefundNum2;
    }

    @JSONField(name = "ACCEPT_AUTH_NUM_1")
    public void setAcceptAuthNum1(Integer acceptAuthNum1) {
        this.acceptAuthNum1 = acceptAuthNum1;
    }

    @JSONField(name = "ACCEPT_AUTH_NUM_2")
    public void setAcceptAuthNum2(Integer acceptAuthNum2) {
        this.acceptAuthNum2 = acceptAuthNum2;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }

    public void setSaleNum(Integer saleNum) {
        this.saleNum = saleNum;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public void setNatAmount(BigDecimal natAmount) {
        this.natAmount = natAmount;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setTransactionCurrency(String transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }
}
