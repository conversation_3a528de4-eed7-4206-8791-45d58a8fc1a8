package com.epay.analytic.rest;

import com.epay.analytic.service.AnalyticService;
import com.epay.analytic.service.dto.AnalyticHistoryCriteria;
import com.epay.analytic.service.dto.AnalyticOverViewCriteria;
import com.epay.logging.annotation.Log;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import com.epay.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 数据分析
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/analytic")
public class AnalyticController {

    private final AnalyticService analyticService;

    /**
     * 查询分析概览
     *
     * @param criteria 查询条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/overview")
    public ResponseEntity<RestResult> queryOverview(AnalyticOverViewCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(analyticService.queryOverview(criteria, pageable, SecurityUtils.getCurrentLoginUser())), HttpStatus.OK);
    }

    /**
     * 查询分析历史
     *
     * @param criteria 查询条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Log(isSaveResponseData = false)
    @GetMapping("/history")
    public ResponseEntity<RestResult> queryHistory(AnalyticHistoryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(RestUtil.ok(analyticService.queryHistory(criteria, pageable, SecurityUtils.getCurrentLoginUser())), HttpStatus.OK);
    }

}
