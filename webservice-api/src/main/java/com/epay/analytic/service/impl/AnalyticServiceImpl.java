package com.epay.analytic.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epay.analytic.domain.AnalyticHistory;
import com.epay.analytic.domain.AnalyticOverview;
import com.epay.analytic.service.AnalyticService;
import com.epay.analytic.service.dto.AnalyticHistoryCriteria;
import com.epay.analytic.service.dto.AnalyticOverViewCriteria;
import com.epay.base.login.UcUserLoginDto;
import com.epay.config.KeyPathConstant;
import com.epay.config.bean.DataServerProperties;
import com.epay.exception.BadRequestException;
import com.epay.management.domain.MerchantList;
import com.epay.management.service.MerchantService;
import com.epay.management.service.dto.MerchantCriteria;
import com.epay.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnalyticServiceImpl implements AnalyticService {

    private final DataServerProperties dataServerProperties;
    private final MerchantService merchantService;

    /**
     * 查询分析概览
     *
     * @param criteria         查询条件
     * @param pageable         分页参数
     * @param currentLoginUser
     * @return 分页结果
     */
    @Override
    public Map<String, Object> queryOverview(AnalyticOverViewCriteria criteria, Pageable pageable, UcUserLoginDto currentLoginUser) {
        Map<String, Object> resMap = new HashMap<>();
        try {
            // 获取当前用户的商户支付方式信息
            Map<String, List<String>> userMerchantPaymentMethods = SecurityUtils.getCurrentUserMerchantPaymentMethods();
            
            MerchantCriteria merchantCriteria = new MerchantCriteria();
            merchantCriteria.setUserId(currentLoginUser.getUserId());
            merchantCriteria.setType(criteria.getChannel());
            merchantCriteria.setPaymentType(criteria.getPaymentType());
            merchantCriteria.setIsTerminalCount(false);
            PageResult<MerchantList> merchantPage = merchantService.merchant(merchantCriteria, PageRequest.of(0, Integer.MAX_VALUE));
            List<MerchantList> merchantList = merchantPage.getData();
            if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                merchantList = merchantList.stream().filter(t -> t.getGateway().equals(criteria.getPaymentType())).collect(Collectors.toList());
            }
            Map<String, MerchantList> merchantMap = merchantList.stream().collect(Collectors.toMap(MerchantList::getMerCode, t -> t));
            if (merchantList.isEmpty()) {
                Map<String, Integer> countMap = new HashMap<>();
                countMap.put("unionPay", 0);
                countMap.put("wechatPay", 0);
                countMap.put("aliPay", 0);
                resMap.put("page", PageUtil.noData());
                resMap.put("countMap", countMap);
                return resMap;
            }

            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "VIW_MER_TRANS_STAT");
            Map<String, String> condition = new HashMap<>();

            if (criteria.getSettDate() != null) {
                condition.put("SETT_DATE", DateUtil.parseTimeStr(criteria.getSettDate()));
            }

            if (!merchantList.isEmpty()) {
                condition.put("MER_CODE", "$in(" + merchantList.stream().map(MerchantList::getMerCode).collect(Collectors.joining(",")) + ")");
            }

            // 分页参数
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());

            // 条件参数
            if (!condition.isEmpty()) {
                params.put("condition", JSON.toJSONString(condition));
            }
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));

            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();

            // 查询总数
            params.put("analysis", "count(*)");
            params.remove("skip");
            params.remove("limit");
            params.remove("sign");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            log.info("out {}", dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();

            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());

            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<AnalyticOverview> res = JSON.parseArray(resListJson.getString("data"), AnalyticOverview.class);

                // 过滤结果：只保留用户有权限的商户支付方式
                List<AnalyticOverview> filteredRes = res.stream()
                    .filter(overview -> {
                        String merCode = overview.getMerCode();
                        if (merCode == null) {
                            return false;
                        }
                        
                        // 检查用户是否有该商户的支付方式权限
                        List<String> userPaymentMethods = userMerchantPaymentMethods.get(merCode);
                        if (userPaymentMethods == null || userPaymentMethods.isEmpty()) {
                            log.debug("用户没有商户 {} 的支付方式权限", merCode);
                            return false;
                        }
                        
                        // 获取商户的支付方式
                        MerchantList merchant = merchantMap.get(merCode.trim());
                        if (merchant == null) {
                            log.debug("找不到商户 {} 的信息", merCode);
                            return false;
                        }
                        
                        // 获取商户的支付方式名称（已经是转换后的值）
                        String merchantPaymentMethod = merchant.getGateway();
                        if (merchantPaymentMethod == null) {
                            log.debug("商户 {} 的支付方式为空", merCode);
                            return false;
                        }
                        
                        // 检查商户的支付方式是否在用户的权限列表中
                        boolean hasPermission = userPaymentMethods.contains(merchantPaymentMethod);
                        if (!hasPermission) {
                            log.debug("用户没有商户 {} 的支付方式 {} 权限，用户权限: {}", merCode, merchantPaymentMethod, userPaymentMethods);
                        }
                        return hasPermission;
                    })
                    .collect(Collectors.toList());

                for (AnalyticOverview overview : filteredRes) {
                    MerchantList merchant = merchantMap.get(overview.getMerCode().trim());
                    if (merchant != null) {
                        overview.setPaymentMethod(merchant.getGateway());
                        overview.setMerName(merchant.getMerName());
                        overview.setChannel(merchant.getMerType());
                        overview.setTransactionCurrency(merchant.getTransactionCurrency());
                    }
                    overview.setSaleAmount((overview.getAcceptSaleTransAmt1().add(overview.getAcceptSaleTransAmt2())).multiply(new BigDecimal(100)));
                    overview.setSaleNum(overview.getAcceptSaleNum1() + overview.getAcceptSaleNum2() + overview.getAcceptRefundNum1() + overview.getAcceptRefundNum2());
                    overview.setRefundAmount(overview.getAcceptRefundTransAmt1().add(overview.getAcceptRefundTransAmt2()).multiply(new BigDecimal(100)).abs());
                    overview.setNatAmount(overview.getSaleAmount().subtract(overview.getRefundAmount()));
                }

                StringUtils.trimStringsInList(filteredRes);

                Map<String, Integer> countMap = getCountMap(params, condition, merchantCriteria);
                PageResult<AnalyticOverview> page = PageUtil.toPage(filteredRes, filteredRes.size());
                resMap.put("page", page);
                resMap.put("countMap", countMap);
                return resMap;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 查询分析历史
     *
     * @param criteria         查询条件
     * @param pageable         分页参数
     * @param currentLoginUser
     * @return 分页结果
     */
    @Override
    public Map<String, Object> queryHistory(AnalyticHistoryCriteria criteria, Pageable pageable, UcUserLoginDto currentLoginUser) {
        try {
            Map<String, Object> resMap = new HashMap<>();
            MerchantCriteria merchantCriteria = new MerchantCriteria();
            merchantCriteria.setUserId(currentLoginUser.getUserId());
            merchantCriteria.setType(criteria.getChannel());
            merchantCriteria.setPaymentType(criteria.getPaymentType());
            merchantCriteria.setIsTerminalCount(false);
            PageResult<MerchantList> merchantPage = merchantService.merchant(merchantCriteria, PageRequest.of(0, Integer.MAX_VALUE));

            List<MerchantList> merchantList = merchantPage.getData();
            if (StringUtils.isNotEmpty(criteria.getPaymentType())) {
                merchantList = merchantList.stream().filter(t -> t.getGateway().equals(criteria.getPaymentType())).collect(Collectors.toList());
            }

            Map<String, MerchantList> merchantMap = merchantList.stream().collect(Collectors.toMap(MerchantList::getMerCode, t -> t));

            if (merchantList.isEmpty()) {
                Map<String, Integer> countMap = new HashMap<>();
                countMap.put("unionPay", 0);
                countMap.put("wechatPay", 0);
                countMap.put("aliPay", 0);
                resMap.put("page", PageUtil.noData());
                resMap.put("countMap", countMap);
                return resMap;
            }

            // 存储请求数据
            Map<String, Object> params = new HashMap<>();
            params.put("timestamp", new Date().getTime() / 1000);
            params.put("table", "TBL_MERCHANT_REPORT_HISTORY");
            Map<String, String> condition = new HashMap<>();

            if (criteria.getStartDate() != null && criteria.getEndDate() != null) {
                condition.put("SETT_DATE", "$between(" + DateUtil.parseTimeStr(criteria.getStartDate()) + "," + DateUtil.parseTimeStr(criteria.getEndDate()) + ")");
            }

            if (!merchantList.isEmpty()) {
                condition.put("MER_CODE", "$in(" + merchantList.stream().map(MerchantList::getMerCode).collect(Collectors.joining(",")) + ")");
            }

            // 分页参数
            params.put("skip", pageable.getOffset());
            params.put("limit", pageable.getPageSize());

            // 条件参数
            if (!condition.isEmpty()) {
                params.put("condition", JSON.toJSONString(condition));
            }
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));

            // 查询内容
            HttpRequest requestList = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseList = requestList.execute();

            // 查询总数
            params.put("analysis", "count(*)");
            params.remove("skip");
            params.remove("limit");
            params.remove("sign");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            log.info("{}", dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();

            // 处理结果
            JSONObject resListJson = JSON.parseObject(responseList.body());
            JSONObject resCountJson = JSON.parseObject(responseCount.body());

            // 判断请求是否成功
            if (resListJson.getInteger("code") == 0 && resCountJson.getInteger("code") == 0) {
                List<AnalyticHistory> res = JSON.parseArray(resListJson.getString("data"), AnalyticHistory.class);


                for (AnalyticHistory overview : res) {
                    MerchantList merchant = merchantMap.get(overview.getMerCode().trim());
                    if (merchant != null) {
                        overview.setPaymentMethod(merchant.getGateway());
                        overview.setMerName(merchant.getMerName());
                        overview.setChannel(merchant.getMerType());
                        overview.setTransactionCurrency(merchant.getTransactionCurrency());
                        overview.setMerSaleAmt(overview.getMerSaleAmt().multiply(new BigDecimal(100)).abs());
                    }
                }

                StringUtils.trimStringsInList(res);

                Map<String, Integer> countMap = getCountMap(params, condition, merchantCriteria);
                PageResult<AnalyticHistory> page = PageUtil.toPage(res, resCountJson.getInteger("data"));
                resMap.put("page", page);
                resMap.put("countMap", countMap);
                return resMap;
            } else {
                throw new BadRequestException(resListJson.getString("message"));
            }
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }


    private Map<String, Integer> getCountMap(Map<String, Object> params, Map<String, String> condition, MerchantCriteria merchantCriteria) throws Exception {
        // 获取当前用户的商户支付方式信息
        Map<String, List<String>> userMerchantPaymentMethods = SecurityUtils.getCurrentUserMerchantPaymentMethods();
        
        // 收集用户有权限的所有支付类型
        Map<String, Integer> resMap = new HashMap<>();
        Set<String> userPaymentTypes = new HashSet<>();
        
        // 从用户权限中提取所有唯一的支付类型
        for (List<String> paymentMethods : userMerchantPaymentMethods.values()) {
            userPaymentTypes.addAll(paymentMethods);
        }
        
        // 如果用户没有任何支付类型权限，直接返回空结果
        if (userPaymentTypes.isEmpty()) {
            return resMap;
        }
        
        // 只统计用户有权限的支付类型
        for (String type : userPaymentTypes) {
            merchantCriteria.setPaymentType(type);
            PageResult<MerchantList> merchantPage = merchantService.merchant(merchantCriteria, PageRequest.of(0, Integer.MAX_VALUE));
            List<MerchantList> merchantList = merchantPage.getData();
            if (StringUtils.isNotEmpty(merchantCriteria.getPaymentType())) {
                merchantList = merchantList.stream().filter(t -> t.getGateway().equals(merchantCriteria.getPaymentType())).collect(Collectors.toList());
            }
            if (!merchantList.isEmpty()) {
                condition.put("MER_CODE", "$in(" + merchantList.stream().map(MerchantList::getMerCode).collect(Collectors.joining(",")) + ")");
            } else {
                resMap.put(type, 0);
                continue;
            }
            // 条件参数
            params.put("condition", JSON.toJSONString(condition));
            // 查询总数
            params.put("analysis", "count(*)");
            params.remove("skip");
            params.remove("limit");
            params.remove("sign");
            params.put("sign", SignUtil.getSign(params, KeyPathConstant.MERCHANT_KEY));
            HttpRequest requestCount = HttpUtil.createGet(dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            log.info("{}", dataServerProperties.getUrl() + "?" + SignUtil.mapToUrlParamsEncoding(params));
            HttpResponse responseCount = requestCount.execute();
            JSONObject resCountJson = JSON.parseObject(responseCount.body());
            if (resCountJson.getInteger("code") == 0) {
                resMap.put(type, resCountJson.getInteger("data"));
            }
        }
        return resMap;
    }


}
