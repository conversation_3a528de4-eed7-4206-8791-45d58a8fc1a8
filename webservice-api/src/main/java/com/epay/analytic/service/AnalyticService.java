package com.epay.analytic.service;

import com.epay.analytic.domain.AnalyticHistory;
import com.epay.analytic.domain.AnalyticOverview;
import com.epay.analytic.service.dto.AnalyticHistoryCriteria;
import com.epay.analytic.service.dto.AnalyticOverViewCriteria;
import com.epay.base.login.UcUserLoginDto;
import com.epay.utils.PageResult;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
public interface AnalyticService {

    /**
     * 查询分析概览
     *
     * @param criteria         查询条件
     * @param pageable         分页参数
     * @param currentLoginUser
     * @return 分页结果
     */
    Map<String,Object> queryOverview(AnalyticOverViewCriteria criteria, Pageable pageable, UcUserLoginDto currentLoginUser);

    /**
     * 查询分析历史
     *
     * @param criteria         查询条件
     * @param pageable         分页参数
     * @param currentLoginUser
     * @return 分页结果
     */
    Map<String, Object> queryHistory(AnalyticHistoryCriteria criteria, Pageable pageable, UcUserLoginDto currentLoginUser);

}
