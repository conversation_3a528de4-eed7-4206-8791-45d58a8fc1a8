server:
  port: 8087
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json

spring:
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  data:
    redis:
      repositories:
        enabled: false

  #Jpa 配置
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect


modern-email:
  client-id: f73d5ddc-7a94-4517-a8dc-bee9e54596e9
  tenant-id: 82121568-413f-433d-a962-02bee46c1538
  client-secret: ****************************************
  sender-email: <EMAIL>
