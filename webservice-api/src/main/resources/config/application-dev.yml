#配置数据源
spring:
  datasource:
    url: jdbc:mysql://**************:3306/epay_service
    username: epay
    password: f12Amdx_84750H
#    url: *********************************************
#    username: root
#    password: 123456

  redis:
    database: ${REDIS_DB:0}
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:}
    timeout: 5000
    ssl: false

# 登录相关配置
login:
  #  是否限制单用户登录
  single-login: true
  # Redis用户登录缓存配置
  user-cache:
    # 存活时间/秒
    idle-time: 21600

#jwt
jwt:
  header: Authorization
  # 登录方式
  login-type: LoginType
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码  （更改密钥需要清除redis key：online-web-token:、user-login-web-cache:）
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认8小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 28800000
  # app在线用户key
  online-management-key: "online-web-token:"
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000


# 数据服务
data-server:
  url: https://test.dynamicg.com/agentdata/api/data/table
  file-url: https://test.dynamicg.com/agentdata/api/file/download
  transaction-url: https://test.dynamicg.com/agentdata/api/data/transaction
  user-url: http://127.0.0.1:8089/api
  web-url: https://dopp.dynamicg.com/oppmertest/api/proxydev?path=/api
  refund-url: https://api.test.gtgintl.net/wechat/unified/v1/refund
  pay-url: https://api.test.gtgintl.net/wechat/unified/v1/pay
  online-pay-url: https://api.test.gtgintl.net/wechat/unified/v1/onlinepay
  email-jump-url: http://**************:8002/#/billpay/Pay
  opp-url: http://*************:8085/api

pay-url:
  front-end-page-url: http://**************:8002/#/billpay/Pay?pay=done
  front-fail-page-url: http://**************:8002/#/billpay/Pay?pay=fail
  call-page-url: http://**************:8087/api/bill/paymentCallback




#是否开启 swagger-ui
swagger:
  enabled: true

# 文件存储路径
file:
  baseUrl: http://localhost:8087/
  path: D:\upload\file\
  avatar: D:\upload\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5

# 密码加密传输，前端公钥加密，后端私钥解密（暂未使用）
blowfish:
  key: "#QDbFTk$fn&&=dkM4VS3P5VrmjSHaxwv"

# 不能变更
aes:
  dataKey: "#QDbFTk$fn&&=dkM4VS3P5VrmjSHaxwv"

id-init:
  manager-id: 10000
