<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <contextName>ePay</contextName>
    <property name="log.charset" value="utf-8" />
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="log.dir" value="logs" />

    <!-- 输出到控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!-- 输出到文件，每天一个新文件 -->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
        <file>${log.dir}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${log.dir}/app-%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- keep 30 days' worth of history -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 普通日志输出到文件 -->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="file" />
    </root>

    <!-- 监控sql日志输出,如需监控 Sql 打印，请设置为 INFO -->
    <logger name="jdbc.sqlonly" level="ERROR" additivity="false">
        <appender-ref ref="console" />
    </logger>

</configuration>