import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.epay.config.KeyPathConstant;
import com.epay.user.service.dto.UcUserCriteria;
import com.epay.user.service.dto.UcUserResetPasswd;
import com.epay.utils.RestResult;
import com.epay.utils.SignUtil;

import java.util.Map;

public class ApiTest2 {

    public static void main(String[] args) throws Exception {

//        UcUserCriteria criteria = new UcUserCriteria();
//        criteria.setEmail("<EMAIL>");
//        Map<String, Object> paramsMap = BeanUtil.beanToMap(criteria, false, true);
//        paramsMap.put("timestamp", System.currentTimeMillis() / 1000L);
////        paramsMap.put("timestamp", 1724812517);
//        paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
//        // 查询内容
//        String url = "http://127.0.0.1:8089/api/user" + "?" + SignUtil.mapToUrlParamsEncoding(paramsMap);
//        HttpRequest requestList = HttpUtil.createGet(url);
//        HttpResponse responseList = requestList.execute();
//        RestResult res = JSON.parseObject(responseList.body(), RestResult.class);
//        System.out.println(JSON.toJSONString(res));

        //GHFlOSVEf/wGDrHAgLgxKKZ9Km7VAwcikgxVW44LjWCTjHGVmDC/4Pu++Do8HFEOGLntcH4F8HEghb5d2mF+v+B/4n8J8ZTPTJSST1VL1m/rucsOkChLsQKJFGRl5ct/xlS1bXTnb/jrpHCRDt6gRdxgtC0oBjfYYHL4fOTa90s=
        //GHFlOSVEf/wGDrHAgLgxKKZ9Km7VAwcikgxVW44LjWCTjHGVmDC/4Pu++Do8HFEOGLntcH4F8HEghb5d2mF+v+B/4n8J8ZTPTJSST1VL1m/rucsOkChLsQKJFGRl5ct/xlS1bXTnb/jrpHCRDt6gRdxgtC0oBjfYYHL4fOTa90s=


        UcUserResetPasswd resetPasswd = new UcUserResetPasswd();
        resetPasswd.setUserId("2000000076");
        resetPasswd.setPassword("Aa11111#");
        Map<String, Object> paramsMap = BeanUtil.beanToMap(resetPasswd, false, true);
//        paramsMap.put("timestamp", System.currentTimeMillis() / 1000L);
        paramsMap.put("timestamp", 1724824972);
        paramsMap.put("sign", SignUtil.getSign(paramsMap, KeyPathConstant.USER_KEY));
        // 查询内容
        String url = "http://121.43.178.172:8089/api/user/resetpasswd";

        HttpRequest request = HttpUtil.createRequest(Method.PUT, url);
        String paramsJson = JSON.toJSONString(paramsMap);
        System.out.println(paramsJson);
        request.body(paramsJson);
        HttpResponse response = request.execute();
        RestResult res = JSON.parseObject(response.body(), RestResult.class);
        System.out.println(JSON.toJSONString(res));

        //jFHx8cBHLNe1/6Bzq/nV2md315dU+UA4iKceHxmSiWrquwy5RGOnDZQAp+2orOXMFi7y75mMg0N+DTqjmLTWImx+CKBM/zOjvrpbX/qx/gXO3M/2RtZF5wYmPQKQvif5qDA6IQ9G5NLe+dnYVafZEXd49I/DW0IN3TLVsJhMz+U=
        //jFHx8cBHLNe1/6Bzq/nV2md315dU+UA4iKceHxmSiWrquwy5RGOnDZQAp+2orOXMFi7y75mMg0N+DTqjmLTWImx+CKBM/zOjvrpbX/qx/gXO3M/2RtZF5wYmPQKQvif5qDA6IQ9G5NLe+dnYVafZEXd49I/DW0IN3TLVsJhMz+U=
    }
}
