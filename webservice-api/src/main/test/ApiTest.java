import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.data.domain.Pageable;
import com.epay.user.service.dto.UcOrganizationMerchantPossessCriteria;
import com.epay.user.util.CallUtil;
import com.epay.utils.RestResult;
import com.epay.utils.SignUtil;

import java.io.UnsupportedEncodingException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

public class ApiTest {

    public static void main(String[] args) throws UnsupportedEncodingException {

//        UcOrganizationMerchantPossessCriteria criteria = new UcOrganizationMerchantPossessCriteria();
//        criteria.setOrgId("0362024001");
//        RestResult res = CallUtil.query("http://121.43.178.172:8089/api/organization/merchant", criteria, new Pageable());
        Map<String, Object> params = new HashMap<>();
        params.put("orgId", "0362024001");
        params.put("timestamp", System.currentTimeMillis() / 1000L);
        params.put("sign", rsaSign(params,"-----BEGIN PRIVATE KEY-----\n" +
                "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJqxJ++l9sDoz55j\n" +
                "J+QjhNdL4FgG3FoSaD739JpYU+5Rj4aKuG4EvsHgMWoUMnW0Xxl0ATNJDiFb0DGB\n" +
                "fLK2EUDiRelWLtAPmMRwvrlpdikXqM+Heak5TzJO9XwsyTUg0a+p23WjNJ51Lpvs\n" +
                "34EBV3x267ManUEwJY69sSbnhFX1AgMBAAECgYAR9v73UuPcNW1fLI7GbOWRh0tP\n" +
                "jcRrZXXb0joC7r0YkTpE2rledYidxWaXti7CAqeEsjcteI9+ikYcabhMnN5V505K\n" +
                "DoWqtFtO3uneOybOm+Obgqsjmk3nkFQ4U5v3p6NcoV3mS4mRV9UyiIyVcpNOUnc1\n" +
                "GMlhSRZT3549Nu4SJQJBANiLlzeHBEhVTD+tTpD92VCHXijOXPaL4xtMiTQrgDvp\n" +
                "A2XLW6436dOz5erdMGa2ZjS5SrTLaJCr4lOVNbP8ETsCQQC24IS/YBPTK4wHeDx+\n" +
                "w0J36WSDAd3apNcMPxN4BHznf42IOWrNVeCc0cfxsR5qGfn6ka2nSsNzUymBwEp/\n" +
                "eMKPAkEAueOizQz/dSScDvG0Ry1TFxkNkJQOI6tUKwQZqApLy6Isw3691iGzfYOq\n" +
                "NGLqgNHFjS8e3XLb8Y1L/ZpR/o69LwJBAKIz4YocnXjfVwQJER8d8QaKbCwGq9AS\n" +
                "BOtHxuzcjiyGZfI9ew1E7kQpjT5B6sgQ4jfsnimTlmfjix0agPkvEuECQAzzA9BY\n" +
                "Ecwc68GFD/IlDtVHI4PxV6Cll3JOPHPYc/AGuGlcQWX3Te/jkMvXMy+F5x2U3Lmr\n" +
                "BV8vNfH7LVbcyNc=\n" +
                "-----END PRIVATE KEY-----"));
        // 查询内容
        HttpRequest requestList = HttpUtil.createGet("http://121.43.178.172:8089/api/organization/merchant" + "?" + SignUtil.mapToUrlParamsEncoding(params));
        HttpResponse responseList = requestList.execute();
        System.out.println(JSON.toJSONString(responseList.body()));
    }


    public static String rsaSign(Map<String, Object> params, String privateKey) {
        String privateKeyPEM = privateKey.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
        Map<String, Object> s = mapSortByKey(params);
        String x = mapToUrlParams(s);
        byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        KeyFactory keyFactory = null;
        try {
            keyFactory = KeyFactory.getInstance("RSA");
        } catch (NoSuchAlgorithmException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        PrivateKey pk = null;
        try {
            pk = keyFactory.generatePrivate(keySpec);
        } catch (InvalidKeySpecException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        byte[] su = null;
        try {
            su = signData(x.getBytes(), pk);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(su);

    }

    /**
     * Map 转 URL参数格式
     *
     * @param map
     * @return
     */
    public static String mapToUrlParams(Map<String, Object> map) {
        StringBuilder stringBuilder = new StringBuilder();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (stringBuilder.length() > 0) {
                stringBuilder.append('&');
            }
            stringBuilder.append(entry.getKey())
                    .append('=')
                    .append(entry.getValue());
        }
        return stringBuilder.toString();
    }

    /**
     * Map 通过 KEY 排序
     *
     * @param map
     * @return
     */
    public static Map<String, Object> mapSortByKey(Map<String, Object> map) {
        // 使用HashMap构造函数将map.entrySet()转换为List
        List<Map.Entry<String, Object>> list = new ArrayList<>(map.entrySet());

        // 使用Comparator对List进行排序，按照值（Value）进行排序
        Collections.sort(list, Map.Entry.comparingByKey());

        // 创建一个新的有序Map
        Map<String, Object> sortedMapByValue = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : list) {
            sortedMapByValue.put(entry.getKey(), entry.getValue());
        }
        return sortedMapByValue;
    }

    /**
     * 生成 SignData
     * @param data
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static byte[] signData(byte[] data, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }
}



