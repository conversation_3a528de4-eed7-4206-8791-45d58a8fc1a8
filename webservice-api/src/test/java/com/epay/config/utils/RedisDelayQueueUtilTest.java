package com.epay.config.utils;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Redis延迟队列工具测试类
 */
@SpringBootTest
public class RedisDelayQueueUtilTest {

    @Autowired
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testAddAndGetDelayQueue() throws InterruptedException {
        // 准备测试数据
        Map<String, String> testData = new HashMap<>();
        testData.put("orderId", "TEST_ORDER_001");
        testData.put("remark", "测试订单支付超时");

        String queueCode = "TEST_DELAY_QUEUE";

        // 添加延迟任务（延迟2秒）
        redisDelayQueueUtil.addDelayQueue(testData, 2, TimeUnit.SECONDS, queueCode);

        // 立即尝试获取，应该为null
        Object result1 = redisDelayQueueUtil.getDelayQueue(queueCode);
        assert result1 == null : "立即获取应该为null";

        // 等待3秒后再获取
        Thread.sleep(3000);
        Object result2 = redisDelayQueueUtil.getDelayQueue(queueCode);
        assert result2 != null : "延迟后获取应该有数据";

        System.out.println("测试成功！获取到的数据：" + result2);

        // 清理测试数据
        redisTemplate.delete(queueCode);
    }
}
