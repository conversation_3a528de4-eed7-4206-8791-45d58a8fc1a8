version: 0.2

env:
  variables:
    AWS_DEFAULT_REGION: ap-southeast-1
    AWS_ACCOUNT_ID: ************
    IMAGE_REPO_NAME: com.epay/epay-webservice

phases:
  install:
    runtime-versions:
      java: corretto11
    commands:
      - echo Installing kustomize...
      - cd /tmp
      - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
      - mv kustomize /usr/local/bin/
      - cd $CODEBUILD_SRC_DIR
      - echo Verifying installation...
      - kustomize version

  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - echo Configuring git credentials for CodeCommit...
      - git config --global credential.helper '!aws codecommit credential-helper $@'
      - git config --global credential.UseHttpPath true
      - PROJECT_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
      - PROJECT_BUILD_DATETIME=$(date +%Y%m%d%H%M%S)
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${PROJECT_VERSION}-${PROJECT_BUILD_DATETIME}
      - REPOSITORY_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com/${IMAGE_REPO_NAME}
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com

  build:
    commands:
      - echo Build started on `date`
      - echo Building the Maven application...
      - mvn clean package -DskipTests
      - echo Build completed on `date`
      - echo Building the Docker image...
      - docker build -t $IMAGE_REPO_NAME:latest .
      - docker tag $IMAGE_REPO_NAME:latest $REPOSITORY_URI:latest
      - docker tag $IMAGE_REPO_NAME:latest $REPOSITORY_URI:$PROJECT_VERSION
      - docker tag $IMAGE_REPO_NAME:latest $REPOSITORY_URI:$IMAGE_TAG
      - docker tag $IMAGE_REPO_NAME:latest $REPOSITORY_URI:$COMMIT_HASH

  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$PROJECT_VERSION
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - docker push $REPOSITORY_URI:$COMMIT_HASH
      - IMAGE_DIGEST=$(docker inspect --format='{{index .RepoDigests 0}}' $REPOSITORY_URI:$IMAGE_TAG | cut -d'@' -f2)
      - echo Creating imagedefinitions.json file...
      - printf '[{"name":"epay-webservice","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
      - echo Updating kustomize configuration...
      - cd kustomize/base
      - kustomize edit set image epay-webservice=$REPOSITORY_URI:$IMAGE_TAG@$IMAGE_DIGEST
      - cd ../..
      - echo Committing kustomize changes...
      - git config user.email "<EMAIL>"
      - git config user.name "AWS CodeBuild"
      - git add kustomize/base/kustomization.yaml
      - git commit -m "[codecommit img version update] $PROJECT_VERSION-$PROJECT_BUILD_DATETIME@$(echo $IMAGE_DIGEST | cut -c 1-12) [skip ci]" || echo "No changes to commit"
      - CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
      - echo "Current branch is $CURRENT_BRANCH"
      - git push origin HEAD:$CURRENT_BRANCH || echo "Failed to push changes"

artifacts:
  files:
    - imagedefinitions.json
    - "**/*"
