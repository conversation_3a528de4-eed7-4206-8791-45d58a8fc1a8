package com.epay.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;


/**
 * <AUTHOR>
 * @date 2024/6/1
 */
@Component
public class StringToTimestampConverter implements Converter<String, Timestamp> {

    @Override
    public Timestamp convert(String source) {
        if (source == null || source.trim().isEmpty()) {
            return null;
        }

        try {
            return Timestamp.valueOf(source);
        } catch (IllegalArgumentException e) {
            // 如果转换失败，尝试添加时间部分
            try {
                return Timestamp.valueOf(source + " 00:00:00");
            } catch (IllegalArgumentException ex) {
                throw new IllegalArgumentException("Cannot convert " + source + " to Timestamp. Expected format: yyyy-MM-dd HH:mm:ss");
            }
        }
    }
}
