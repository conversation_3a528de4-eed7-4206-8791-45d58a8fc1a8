package com.epay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "modern-email")
public class MailConfig {

    /**`
     * clientId
     */
    private String clientId;

    /**
     * tenantId
     */
    private String tenantId;

    /**
     * clientSecret
     */
    private String clientSecret;

    /**
     * senderEmail
     */
    private String senderEmail;
}
