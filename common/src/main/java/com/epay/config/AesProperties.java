package com.epay.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-04-14
 **/
@Data
@Component
public class AesProperties {

    public static String dataKey;

    @Value("${aes.dataKey}")
    public void setPrivateKey(String dataKey) {
        AesProperties.dataKey = dataKey;
    }

}