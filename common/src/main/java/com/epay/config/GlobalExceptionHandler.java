package com.epay.config;

import com.epay.exception.BadRequestException;
import com.epay.exception.CustomException;
import com.epay.utils.RestResult;
import com.epay.utils.RestUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<RestResult> handleValidationExceptions(MethodArgumentNotValidException ex) {
        StringBuilder resMeg = new StringBuilder();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            resMeg.append(error.getDefaultMessage()).append("、");
        });
        return new ResponseEntity<>(RestUtil.toRest(400, resMeg.substring(0, resMeg.length() - 1)), HttpStatus.OK);
    }

    //自定义异常
    @ExceptionHandler(CustomException.class)
    @ResponseBody //为了返回数据
    public ResponseEntity<RestResult> error(CustomException e) {
        return new ResponseEntity<>(RestUtil.toRest(e.getCode(), e.getMessage()), HttpStatus.OK);
    }

    //自定义异常
    @ExceptionHandler(BadRequestException.class)
    @ResponseBody //为了返回数据
    public ResponseEntity<RestResult> error(BadRequestException e) {
        return new ResponseEntity<>(RestUtil.toRest(e.getStatus(), e.getMessage()), HttpStatus.OK);
    }

    //自定义异常
    @ExceptionHandler(Exception.class)
    @ResponseBody //为了返回数据
    public ResponseEntity<RestResult> error(Exception e) {
        return new ResponseEntity<>(RestUtil.toRest(500, e.getMessage()), HttpStatus.OK);
    }


}
