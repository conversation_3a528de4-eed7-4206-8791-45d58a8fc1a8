package com.epay.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Slf4j
@Component
public class IdGenerator {

    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成一个包含字母和数字的10位唯一编码。
     * @return 10位唯一编码
     */
    public static String generateUniqueCode() {
        byte[] randomBytes = new byte[7]; // 7 bytes give enough randomness and encoding in base64 results in a string of length 10
        random.nextBytes(randomBytes);
        String encoded = Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
        return encoded.substring(0, 10); // Ensuring the length is 10
    }
}
