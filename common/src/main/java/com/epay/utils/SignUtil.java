package com.epay.utils;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
public class SignUtil {

    public static String getSign(Map<String, Object> params, String keyPath) throws Exception {
        params = processListParams(params);
        PrivateKey privateKey = loadPrivateKey(SignUtil.class.getResourceAsStream(keyPath));
        byte[] signature = signData(mapToUrlParams(mapSortByKey(params)).getBytes(), privateKey);
        String sign = Base64.getEncoder().encodeToString(signature);
        return sign;
    }

    /**
     * 加载 PrivateKey
     *
     * @param inputStream
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    public static PrivateKey loadPrivateKey(InputStream inputStream) throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
        String keyPem = convert(inputStream);
        String privateKeyPEM = keyPem.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
        byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }

    public static String convert(InputStream inputStream) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line).append("\n");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 生成 SignData
     *
     * @param data
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static byte[] signData(byte[] data, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }

    /**
     * Map 通过 KEY 排序
     *
     * @param map
     * @return
     */
    public static Map<String, Object> mapSortByKey(Map<String, Object> map) {
        // 使用HashMap构造函数将map.entrySet()转换为List
        List<Map.Entry<String, Object>> list = new ArrayList<>(map.entrySet());

        // 使用Comparator对List进行排序，按照值（Value）进行排序
        Collections.sort(list, Map.Entry.comparingByKey());

        // 创建一个新的有序Map
        Map<String, Object> sortedMapByValue = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : list) {
            sortedMapByValue.put(entry.getKey(), entry.getValue());
        }
        return sortedMapByValue;
    }

    /**
     * Map 转 URL参数格式
     *
     * @param map
     * @return
     */
    public static String mapToUrlParams(Map<String, Object> map) {
        StringBuilder stringBuilder = new StringBuilder();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (stringBuilder.length() > 0) {
                stringBuilder.append('&');
            }
            String patternStr = "(\\w+:(desc|asc)(,\\w+:(desc|asc))*)?";
            Pattern pattern = Pattern.compile(patternStr);
            if (entry.getKey().equals("sort") && StringUtils.isNotEmpty(String.valueOf(entry.getValue())) && pattern.matcher(String.valueOf(entry.getValue())).matches()) {
                String[] sorts = String.valueOf(entry.getValue()).split(",");
                for (String sort : sorts) {
                    if (stringBuilder.indexOf("sort") > 0) {
                        stringBuilder.append('&');
                    }
                    stringBuilder.append("sort")
                            .append('=').append(sort.split(":")[0]).append(",").append(sort.split(":")[1]);
                }
            } else {
                stringBuilder.append(entry.getKey())
                        .append('=')
                        .append(entry.getValue());
            }
        }
        return stringBuilder.toString();
    }

    /**
     * Map转URL参数格式 并 encoding
     * 遇到括号不进行编码处理
     *
     * @param map
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String mapToUrlParamsEncoding(Map<String, Object> map) throws UnsupportedEncodingException {
        StringBuilder stringBuilder = new StringBuilder();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (stringBuilder.length() > 0) {
                stringBuilder.append('&');
            }
            String patternStr = "(\\w+:(desc|asc)(,\\w+:(desc|asc))*)?";
            Pattern pattern = Pattern.compile(patternStr);
            if (entry.getKey().equals("sort") && StringUtils.isNotEmpty(String.valueOf(entry.getValue())) && pattern.matcher(String.valueOf(entry.getValue())).matches()) {
                String[] sorts = String.valueOf(entry.getValue()).split(",");
                for (String sort : sorts) {
                    if (stringBuilder.indexOf("sort") > 0) {
                        stringBuilder.append('&');
                    }
                    stringBuilder.append(URLEncoder.encode("sort", "utf-8"))
                            .append('=')
                            .append(URLEncoder.encode(sort.split(":")[0] + "," + sort.split(":")[1], "utf-8"));
                }
            } else {
                stringBuilder.append(URLEncoder.encode(entry.getKey(), "utf-8"))
                        .append('=')
                        .append(encodeValuePreserveBrackets(String.valueOf(entry.getValue())));
            }

        }
        return stringBuilder.toString();
    }

    /**
     * 对字符串进行URL编码，但保留括号不编码
     *
     * @param value 需要编码的字符串
     * @return 编码后的字符串
     * @throws UnsupportedEncodingException
     */
    private static String encodeValuePreserveBrackets(String value) throws UnsupportedEncodingException {
        if (value == null) {
            return "";
        }

        // 如果字符串中不包含括号，直接使用标准URL编码
        if (!value.contains("(") && !value.contains(")")) {
            return URLEncoder.encode(value, "utf-8");
        }

        // 临时替换括号为特殊标记
        String tempValue = value.replace("(", "__OPEN_BRACKET__")
                               .replace(")", "__CLOSE_BRACKET__");

        // 编码替换后的字符串
        String encoded = URLEncoder.encode(tempValue, "utf-8");

        // 将特殊标记替换回括号
        return encoded.replace("__OPEN_BRACKET__", "(")
                     .replace("__CLOSE_BRACKET__", ")");
    }

    private static Map<String, Object> processListParams(Map<String, Object> map) {
        Map<String, Object> processedMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof List) {
                List<?> valueList = (List<?>) value;
                // list 内容为对象的处理方式
                if (!valueList.isEmpty() && (valueList.get(0) instanceof Timestamp ||
                        valueList.get(0) instanceof Integer ||
                        valueList.get(0) instanceof String ||
                        valueList.get(0) instanceof Long)) {
                    // Convert list elements to string
                    String joined = valueList.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining(","));
                    processedMap.put(entry.getKey(), joined);
                    map.put(entry.getKey(), joined);
                }
            } else {
                processedMap.put(entry.getKey(), value);
            }
        }
        return processedMap;
    }

}
