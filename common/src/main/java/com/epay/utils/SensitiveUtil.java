package com.epay.utils;

public class SensitiveUtil {

    public static String maskNumber(String number) {
        // 去掉前两位
        if (StringUtils.isEmpty(number)) {
            return number;
        }
        if (number.length() <= 12) {
            return number;
        }
        if (number.startsWith("16") || number.startsWith("18")) {
            number = number.substring(2);
        }
        return number.substring(0, 6) +
                "****" +
                number.substring(number.length() - 4);
    }

}
