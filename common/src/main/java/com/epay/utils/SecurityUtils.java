package com.epay.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epay.base.login.JwtUserDto;
import com.epay.base.login.UcUserLoginDto;
import com.epay.utils.enums.DataScopeEnum;
import com.epay.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Slf4j
public class SecurityUtils {

    /**
     * 获取当前登录的用户
     * @return UserDetails
     */
    public static UserDetails getCurrentUser() {
        UserDetailsService userDetailsService = SpringContextHolder.getBean(UserDetailsService.class);
        return userDetailsService.loadUserByUsername(getCurrentUsername());
    }

    /**
     * 获取当前登录的用户详细数据
     * @return UserDetails
     */
    public static UcUserLoginDto getCurrentLoginUser() {
        UserDetailsService userDetailsService = SpringContextHolder.getBean(UserDetailsService.class);
        JwtUserDto jwtUser = (JwtUserDto) userDetailsService.loadUserByUsername(getCurrentUsername());
        return jwtUser.getUserLoginDto();
    }

    /**
     * 获取当前登录的用户详细数据
     * @return UserDetails
     */
    public static boolean isTemporary() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BadRequestException(HttpStatus.UNAUTHORIZED, "当前登录状态过期");
        }
        return !(authentication.getPrincipal() instanceof UserDetails);
    }

    /**
     * 获取系统用户名称
     *
     * @return 系统用户名称
     */
    public static String getCurrentUsername() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BadRequestException(HttpStatus.UNAUTHORIZED, "当前登录状态过期");
        }
        if (authentication.getPrincipal() instanceof UserDetails) {
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            return userDetails.getUsername();
        }
        throw new BadRequestException(HttpStatus.UNAUTHORIZED, "找不到当前登录的信息");
    }

    /**
     * 获取临时授权key
     *
     * @return 临时授权key
     */
    public static String getTempKey() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BadRequestException(HttpStatus.UNAUTHORIZED, "temporary auth expire");
        }
        if (authentication.getPrincipal() instanceof String) {
            return (String) authentication.getPrincipal();
        }
        throw new BadRequestException(HttpStatus.UNAUTHORIZED, "not find auth");
    }

    /**
     * 获取系统用户ID
     * @return 系统用户ID
     */
    public static Integer getCurrentUserId() {
        UserDetails userDetails = getCurrentUser();
        // 将 Java 对象转换为 JSONObject 对象
        JSONObject jsonObject = (JSONObject) JSON.toJSON(userDetails);
        return jsonObject.getJSONObject("managerDto").getInteger("managerId");
    }

    /**
     * 获取当前用户的数据权限
     * @return /
     */
    public static List<Long> getCurrentUserDataScope(){
        UserDetails userDetails = getCurrentUser();
        // 将 Java 对象转换为 JSONObject 对象
        JSONObject jsonObject = (JSONObject) JSON.toJSON(userDetails);
        JSONArray jsonArray = jsonObject.getJSONArray("dataScopes");
        return JSON.parseArray(jsonArray.toJSONString(), Long.class);
    }

    /**
     * 获取数据权限级别
     * @return 级别
     */
    public static String getDataScopeType() {
        List<Long> dataScopes = getCurrentUserDataScope();
        if(dataScopes.size() != 0){
            return "";
        }
        return DataScopeEnum.ALL.getValue();
    }

    /**
     * 获取当前登录用户的商户支付方式信息
     * @return 商户支付方式映射，key为商户ID，value为支付方式列表
     */
    public static Map<String, List<String>> getCurrentUserMerchantPaymentMethods() {
        UserDetailsService userDetailsService = SpringContextHolder.getBean(UserDetailsService.class);
        JwtUserDto jwtUser = (JwtUserDto) userDetailsService.loadUserByUsername(getCurrentUsername());
        return jwtUser.getMerchantPaymentMethods();
    }
}
