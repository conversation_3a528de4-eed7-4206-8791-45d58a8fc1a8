package com.epay.utils;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.epay.config.MailConfig;
import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.models.*;
import com.microsoft.graph.requests.GraphServiceClient;
import lombok.RequiredArgsConstructor;
import okhttp3.Request;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;

@Component
@RequiredArgsConstructor
public class EmailSender {

    private final MailConfig mailConfig;


    public void sendEmail(String to, String subject, String body) {
        // 创建凭证
        ClientSecretCredential credential = new ClientSecretCredentialBuilder()
                .clientId(mailConfig.getClientId())
                .clientSecret(mailConfig.getClientSecret())
                .tenantId(mailConfig.getTenantId())
                .build();

        // 创建认证提供者
        TokenCredentialAuthProvider authProvider = new TokenCredentialAuthProvider(
                Arrays.asList("https://graph.microsoft.com/.default"),
                credential
        );

        // 创建 Graph client
        GraphServiceClient<Request> graphClient = GraphServiceClient.builder()
                .authenticationProvider(authProvider)
                .buildClient();

        // 创建邮件
        Message message = new Message();
        message.subject = subject;
        message.body = new ItemBody();
        message.body.contentType = BodyType.HTML;
        message.body.content = body;

        Recipient recipient = new Recipient();
        recipient.emailAddress = new EmailAddress();
        recipient.emailAddress.address = to;
        message.toRecipients = Collections.singletonList(recipient);

        // 发送邮件
        graphClient.users(mailConfig.getSenderEmail())
                .sendMail(UserSendMailParameterSet.newBuilder().
                        withMessage(message).
                        build())
                .buildRequest()
                .post();
    }



}
