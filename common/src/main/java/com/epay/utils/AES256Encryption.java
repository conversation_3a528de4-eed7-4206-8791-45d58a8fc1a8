package com.epay.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class AES256Encryption {
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    public static String encrypt(String plainText, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, getKeySpec(key));
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static String decrypt(String encryptedText, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, getKeySpec(key));
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes);
    }

    private static SecretKeySpec getKeySpec(String key) throws Exception {
        byte[] keyBytes = new byte[32];
        byte[] b = key.getBytes("UTF-8");
        int len = b.length;
        if (len > keyBytes.length) len = keyBytes.length;
        System.arraycopy(b, 0, keyBytes, 0, len);
        return new SecretKeySpec(keyBytes, ALGORITHM);
    }

    public static void main(String[] args) {
        try {
            String key = "#QDbFTk$fn&&=dkM4VS3P5VrmjSHaxwv";
//            String plainText = "ES2409111104311307";
//            String plainText = "ES2409111104311305";
            String plainText = "ES2409111104311307";
            String encrypted = encrypt(plainText, key);
            String decrypted = decrypt(encrypted, key);
//http://localhost:6656/#/transaction/Pay?pay=done&data=gDijVjmnyGDu3bRUVbupJkmYIodVJbPhx0w2eYg4+i8=
            System.out.println("Plain Text : " + plainText);
            System.out.println("Encrypted  : " + encrypted);
            System.out.println("Decrypted  : " + decrypted);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
