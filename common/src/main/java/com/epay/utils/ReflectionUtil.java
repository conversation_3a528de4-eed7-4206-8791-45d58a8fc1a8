package com.epay.utils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;

public class ReflectionUtil {

    /**
     * 根据传入的类的Class对象，创建该类的实例，并设置指定属性的值。
     *
     * @param targetClass   目标类的Class对象
     * @param propertyName  属性名称
     * @param propertyValue 要设置的属性值
     * @param <T>           目标类的类型
     * @throws IllegalAccessException 如果没有访问属性的权限时抛出
     * @throws NoSuchFieldException   如果指定的属性不存在时抛出
     * @throws InstantiationException 如果无法实例化对象（比如类是抽象类等情况）抛出
     */
    public static <T> T setPropertyValue(Class<T> targetClass, String propertyName, Object propertyValue) throws Exception {
        // 通过默认的无参构造函数创建对象实例
        Constructor<T> constructor = targetClass.getConstructor();
        T targetObj = constructor.newInstance();
        // 获取指定名称的Field对象
        Field field = targetClass.getDeclaredField(propertyName);
        // 设置可访问性，以便能访问私有属性等
        field.setAccessible(true);
        // 根据属性的类型进行类型转换并设置值
        Class<?> fieldType = field.getType();
        if (fieldType == int.class || fieldType == Integer.class) {
            if (propertyValue instanceof Integer) {
                field.setInt(targetObj, (Integer) propertyValue);
            } else {
                throw new IllegalArgumentException("属性类型不匹配，期望Integer类型的值");
            }
        } else if (fieldType == double.class || fieldType == Double.class) {
            if (propertyValue instanceof Double) {
                field.setDouble(targetObj, (Double) propertyValue);
            } else {
                throw new IllegalArgumentException("属性类型不匹配，期望Double类型的值");
            }
        } else if (fieldType == String.class) {
            if (propertyValue instanceof String) {
                field.set(targetObj, propertyValue);
            } else {
                throw new IllegalArgumentException("属性类型不匹配，期望String类型的值");
            }
        } else if (fieldType == boolean.class || fieldType == Boolean.class) {
            if (propertyValue instanceof Boolean) {
                field.setBoolean(targetObj, (Boolean) propertyValue);
            } else {
                throw new IllegalArgumentException("属性类型不匹配，期望Boolean类型的值");
            }
        } else {
            // 可以根据需要继续扩展更多类型的处理
            throw new IllegalArgumentException("暂不支持的属性类型：" + fieldType.getName());
        }
        return targetObj;
    }
}
