package com.epay.utils;

import java.util.regex.Pattern;

/**
 * 邮箱验证工具类
 */
public class EmailValidator {
    
    private static final String EMAIL_REGEX = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    private static final Pattern pattern = Pattern.compile(EMAIL_REGEX);
    
    /**
     * 验证邮箱格式是否正确
     * @param email 待验证的邮箱
     * @return 邮箱格式正确返回true，否则返回false
     */
    public static boolean isValid(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return pattern.matcher(email).matches();
    }
    
    /**
     * 验证邮箱格式是否正确，如果为空则返回true
     * @param email 待验证的邮箱
     * @return 邮箱格式正确或为空返回true，否则返回false
     */
    public static boolean isValidOrEmpty(String email) {
        if (email == null || email.trim().isEmpty()) {
            return true;
        }
        return pattern.matcher(email).matches();
    }
} 