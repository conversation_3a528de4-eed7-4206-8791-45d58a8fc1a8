package com.epay.utils;


import com.epay.utils.enums.RestEnum;

public class RestUtil {

    /**
     * Rest 枚举处理
     */
    public static RestResult ok() {
        return new RestResult(RestEnum.SUCCESS.getCode(), RestEnum.SUCCESS.getMessage(), null);
    }

    /**
     * Rest 数据处理
     */
    public static RestResult ok(Object object) {
        return new RestResult(RestEnum.SUCCESS.getCode(), RestEnum.SUCCESS.getMessage(), object);
    }

    /**
     * Rest 枚举处理
     */
    public static RestResult toRest(RestEnum restEnum) {
        return new RestResult(restEnum.getCode(), restEnum.getMessage(), null);
    }

    /**
     * Rest 枚举处理
     */
    public static RestResult toRest(int code,String message) {
        return new RestResult(code, message, null);
    }



}
