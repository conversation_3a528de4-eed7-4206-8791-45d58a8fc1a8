package com.epay.utils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2024/5/30
 */
public class DateUtil extends cn.hutool.core.date.DateUtil {

    public static String transferMerchantDate(String inputDateStr) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        outputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date date = inputFormat.parse(inputDateStr);
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean isValidDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false); // 设置为严格模式，确保日期格式严格匹配
        try {
            sdf.parse(dateStr); // 尝试解析日期
            return true; // 如果解析成功，返回 true
        } catch (ParseException e) {
            return false; // 如果解析失败，返回 false
        }
    }

    public static String getEndOfDay(String dateStr) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDate date = LocalDate.parse(dateStr, inputFormatter); // 解析字符串为 LocalDate
        LocalDateTime endOfDay = date.atTime(23, 59, 59); // 获取当天的 23:59:59
        return endOfDay.format(outputFormatter); // 格式化为字符串
    }

    public static String getStartOfDay(String dateStr) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDate date = LocalDate.parse(dateStr, inputFormatter); // 解析字符串为 LocalDate
        LocalDateTime endOfDay = date.atTime(0, 0, 0); // 获取当天的 23:59:59
        return endOfDay.format(outputFormatter); // 格式化为字符串
    }


    public static String parseTimeStr(Timestamp timestamp) {
        // 转换为指定时区的日期（例如系统默认时区）
        LocalDate localDate = timestamp.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return localDate.format(formatter);
    }

}
