package com.epay.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class Md5Utils {

    public static String md5Sign(Map<String, Object> params, String key) {
        String content = getSignData(params, true);
        content += key;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(content.getBytes());
            byte[] digest = md.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private static String getSignData(Map<String, Object> params, boolean ifsort) {

        StringBuffer content = new StringBuffer();

        List<String> keys = new ArrayList<String>(params.keySet());
        if (ifsort) {
            Collections.sort(keys);
        }

        int p = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = (String) keys.get(i);
            if ("sign".equals(key)) {
                continue;
            }
            if ("signType".equals(key)) {
                continue;
            }
            Object obj = params.get(key);
            if (obj == null) {
                continue;
            }
            String value = "";
            if (obj instanceof String[]) {
                String[] arr = (String[]) obj;
                for (int j = 0; j < arr.length; j++) {
                    value += arr[j];
                }
                ;
            } else if (obj instanceof String) {
                value = (String) params.get(key);
            } else {
                value = String.valueOf(obj);
            }
            if (value != null) {
                content.append((p == 0 ? "" : "&") + key + "=" + value);
            } else {
                content.append((p == 0 ? "" : "&") + key + "=");
            }
            p = 1;
        }

        return content.toString();

    }
}
