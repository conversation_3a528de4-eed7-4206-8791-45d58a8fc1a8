package com.epay.utils;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 分页工具
 *
 * <AUTHOR>
 * @date 2024-04-14
 */
public class PageUtil extends cn.hutool.core.util.PageUtil {

    /**
     * List 分页
     */
    public static <T> List<T> paging(int page, int size, List<T> list) {
        int fromIndex = page * size;
        int toIndex = page * size + size;
        if (fromIndex > list.size()) {
            return Collections.emptyList();
        } else if (toIndex >= list.size()) {
            return list.subList(fromIndex, list.size());
        } else {
            return list.subList(fromIndex, toIndex);
        }
    }

    /**
     * Page 数据处理，预防redis反序列化报错
     */
    public static <T> PageResult<T> toPage(Page<T> page) {
        return new PageResult<>(page.getContent(), page.getTotalElements());
    }

    /**
     * 自定义分页
     */
    public static <T> PageResult<T> toPage(List<T> list, long totalElements) {
        return new PageResult<>(list, totalElements);
    }

    /**
     * 返回空数据
     */
    public static <T> PageResult<T> noData() {
        return new PageResult<>(new ArrayList<>(), 0);
    }

    /**
     * map增加分页参数
     */
    public static Map<String, Object> mapAddPage(Map<String, Object> map, Pageable pageable) {
        map.put("page", pageable.getPageNumber());
        map.put("size", pageable.getPageSize());
        map.put("sort", sortToRequestParam(pageable.getSort()));
        return map;
    }

    // 将Sort对象转换为请求参数的字符串，采用field:direction形式
    public static String sortToRequestParam(Sort sort) {
        StringBuilder sortParam = new StringBuilder();
        int index = 0;
        for (Sort.Order order : sort) {
            if (index > 0) {
                sortParam.append(",");
            }
            sortParam.append(order.getProperty()).append(":").append(order.getDirection().name().toLowerCase());
            index++;
        }
        return sortParam.toString();
    }


    /**
     * 转Pageable
     */
    public static org.springframework.data.domain.Pageable toPageable(com.epay.bean.Pageable pageable) {
        return PageRequest.of(pageable.getOffset(), pageable.getLimit(), Sort.by(Sort.Direction.DESC, pageable.getSort()));
    }
}
