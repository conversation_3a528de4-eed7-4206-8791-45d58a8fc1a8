package com.epay.utils.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RestEnum {

    SUCCESS(0, "success"),

    SYSTEM_ERROR(500, "system error"),

    CALL_FAIL(800, "call fail"),

    REPEAT_SUBMIT(1001, "please do not resubmit"),
    CODE_EXPIRE(1002, "code expire"),
    CODE_ERROR(1003, "code error"),
    ERR_PLEASE_REAPPLY(1004, "err please reapply"),


    PASSWORD_ERR(1101, "password is error"),
    USER_NOT_EXIST(1102, "user is not exist"),
    USER_FREEZE(1103, "user status is freeze"),
    EMAIL_EXIST(1104, "email is exist"),
    NAME_EXIST(1104, "name is exist"),
    INS_NOT_EXIST(1105, "ins not exist"),
    PRIMARY_CALIBRATION_FAIL(1106, "primary calibration fail"),


    ORG_ID_EXIST(1110, "orgId is exist"),
    ORG_NOT_EXIST(1111, "org is not exist"),
    ORG_NOT_ABLE(1112, "org is not able"),
    MER_CODE_NOT_BELONG_USER(1113, "merCode not belong user"),
    MER_CODE_NOT_EXIST(1114, "mer code not exist"),

    ROLE_NOT_EXIST(1120, "role is not exist"),
    ROLE_IS_EXIST(1121, "role is exist"),


    PERMISSION_NOT_EXIST(1130, "permission is not exist"),

    USER_ROLE_RELATION_NOT_EXIST(1140, "user role relation not exist"),

    ROLE_PERMISSION_RELATION_NOT_EXIST(1150, "role permission relation not exist"),
    USER_MERCHANT_POSSESS_NOT_EXIST(1150, "user merchant possess not exist"),
    AUTHORITY_TEMPLATE_NOT_EXIST(1160, "authority template not exist"),
    USER_AUTHORITY_NOT_EXIST(1170, "user authority not exist"),

    TRANSACTION_NOT_EXIST(1180, "transaction not exist"),
    TRANSACTION_NOT_REFUND(1190, "transaction not refund"),
    TRANSACTION_CURRENT_STATUS_NOT_REFUND(1191, "Refunds are not possible in the current status."),

    TEMPLATE_NOT_EXIST(1200, "template not exist"),
    TEMPLATE_NAME_ALREADY_EXIST(1201, "TEMPLATE NAME ALREADY EXIST"),
    TYPE_ERROR(1210, "type error"),
    FILE_NOT_FIND(1220, "file not find"),

    BILL_NOT_EXIST(1310, "bill not exist"),
    BILL_LOCKED(1311, "bill locked"),
    BILL_IS_DRAFT(1312, "bill is draft"),
    BILL_TIMEOUT(1313, "bill timeout"),
    BILL_ORDER_NAME_EXIST(1314, "bill order name exist"),
    BILL_ALREADY_PAY(1315, "already paid"),
    BILL_LOCK(1316, "bill lock"),

    BILL_PAY_ERR(1410, "bill pay err"),
    BILL_NOT_CONVERT_TO_DRAFT(1411, "bill not convert to draft"),
    EXPIRY_TIMESTAMP_ILLEGAL(1412, "expiry timestamp illegal"),
    EMAIL_FORMAT_ERROR(1413, "email format error"),
    TRANSACTION_NOT_PAY(1420, "transaction not pay"),
    BILL_EXPIRE(1421, "bill expire"),
    BILL_LOCK_NOT_DELETE(1421, "bill lock not delete"),
    BILL_MOUNT_ERROR(1422, "bill mount error"),
    MER_CODE_NOT_BELONG_ORDER(1423, "merCode not belong order"),
    BILL_HAS_TRANSACTION_CANNOT_DELETE(1424, "bill has transaction data, cannot delete"),

    TAG_NOT_EXIST(1430, "tag not exist"),
    TAG_IS_USED(1431, "tag is used"),

    OOP_NOT_EXIST(1140,"oop not exist"),
    MERCHANT_NOT_CREATED(1141, "merchant not created"),
    MERCHANT_CURRENCY_INCONSISTENT(1142, "Currency inconsistent, creation not allowed"),


    TAG_NAME_EXIST(1151, "tag name exist"),
    TAG_USED(1152, "tag used"),



    NOT_POWER(8000, "not power");



    private final int code;
    private final String message;

    public static RestEnum find(int code) {
        for (RestEnum restEnum : RestEnum.values()) {
            if (restEnum.getCode() == code) {
                return restEnum;
            }
        }
        return null;
    }

}
