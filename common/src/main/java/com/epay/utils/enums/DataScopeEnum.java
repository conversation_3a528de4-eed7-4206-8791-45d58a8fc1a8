package com.epay.utils.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-04-14
 * 数据权限枚举
 */
@Getter
@AllArgsConstructor
public enum DataScopeEnum {

    /* 全部的数据权限 */
    ALL("ALL", "全部的数据权限"),

    /* 同机构权限 */
    ORG_LEVEL("ORG_LEVEL", "同机构权限"),

    /* 商户权限 */
    MER_LEVEL("MER_LEVEL", "商户权限")

    ;

    private final String value;
    private final String description;

    public static DataScopeEnum find(String val) {
        for (DataScopeEnum dataScopeEnum : DataScopeEnum.values()) {
            if (dataScopeEnum.getValue().equals(val)) {
                return dataScopeEnum;
            }
        }
        return null;
    }

}
