package com.epay.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2024-04-14
 */
public class BlowfishUtil {

    private static final String ALGORITHM = "Blowfish";

    /**
     * 使用给定的密钥加密明文
     *
     * @param key   用于加密的密钥
     * @param data  需要加密的明文
     * @return 加密后的数据，使用Base64编码的字符串
     */
    public static String encrypt(String key, String data) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
            byte[] encrypted = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("加密过程中发生错误", e);
        }
    }

    /**
     * 使用给定的密钥解密文本
     *
     * @param key            用于解密的密钥
     * @param encryptedData  需要解密的加密文本
     * @return 解密后的文本
     */
    public static String decrypt(String key, String encryptedData) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] original = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
            return new String(original);
        } catch (Exception e) {
            throw new RuntimeException("解密过程中发生错误", e);
        }
    }

    public static void main(String[] args) {
        String key = "12345678";  // 确保密钥长度符合Blowfish算法要求
        String originalData = "Hello, world!";

        String encrypted = encrypt(key, originalData);
        System.out.println("加密后: " + encrypted);

        String decrypted = decrypt(key, encrypted);
        System.out.println("解密后: " + decrypted);
    }
}
