package com.epay.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
public class HashEncryption {

    public static String encrypt(String data) {
        try {
            // 获取SHA-256加密算法的实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 对输入数据data进行加密处理
            byte[] encodedhash = digest.digest(data.getBytes());

            // 将加密后的字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder(2 * encodedhash.length);
            for (int i = 0; i < encodedhash.length; i++) {
                String hex = Integer.toHexString(0xff & encodedhash[i]);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }
}
