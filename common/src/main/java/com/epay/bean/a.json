{"code": 0, "data": [{"MER_TRANS_CURR": "554", "MER_CODE": "620055400000001", "MER_NATION": "NZL", "MER_KEY": null, "MER_NAME": "DP NZ Testing", "SIGN_METHOD": "MD5", "MER_TYPE": "5311", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200554   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "913076458120001", "MER_NATION": "THA", "MER_KEY": "DpHk!@#4", "MER_NAME": "Forth Smart Service Public Co., Ltd.", "SIGN_METHOD": "MD5", "MER_TYPE": "5812", "MER_STATUS": "0", "MER_ACQ_CODE": "0899130764   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "1790000016     ", "MER_NATION": "THA", "MER_KEY": null, "MER_NAME": "AB Venture", "SIGN_METHOD": "MD5", "MER_TYPE": "5947", "MER_STATUS": "0", "MER_ACQ_CODE": "0899180764   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "989076453316010", "MER_NATION": "THA", "MER_KEY": null, "MER_NAME": "The Mall Group Co., Ltd .", "SIGN_METHOD": "MD5", "MER_TYPE": "5331", "MER_STATUS": "0", "MER_ACQ_CODE": "0829890764   "}, {"MER_TRANS_CURR": "242", "MER_CODE": "619024220230001", "MER_NATION": "FJI", "MER_KEY": "88888888", "MER_NAME": "<PERSON>", "SIGN_METHOD": "MD5", "MER_TYPE": "7011", "MER_STATUS": "0", "MER_ACQ_CODE": "0826190242   "}, {"MER_TRANS_CURR": "554", "MER_CODE": "620055453315015", "MER_NATION": "NZL", "MER_KEY": "88888888", "MER_NAME": "Skykiwi", "SIGN_METHOD": "MD5", "MER_TYPE": "5331", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200554   "}, {"MER_TRANS_CURR": "242", "MER_CODE": "549654351      ", "MER_NATION": "FJI", "MER_KEY": null, "MER_NAME": "Dynamic Payment Pty Ltd. (Test)", "SIGN_METHOD": "MD5", "MER_TYPE": "5812", "MER_STATUS": "0", "MER_ACQ_CODE": "09155658795  "}, {"MER_TRANS_CURR": "036", "MER_CODE": "620003653319998", "MER_NATION": "AUS", "MER_KEY": "88888888", "MER_NAME": "Today Group Poli Testing", "SIGN_METHOD": "MD5", "MER_TYPE": "5331", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200036   "}, {"MER_TRANS_CURR": "036", "MER_CODE": "620003620228888", "MER_NATION": "AUS", "MER_KEY": "88888888", "MER_NAME": "HungryPanda AU Purchase", "SIGN_METHOD": "MD5", "MER_TYPE": "5331", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200036   "}, {"MER_TRANS_CURR": "554", "MER_CODE": "620055420190006", "MER_NATION": "NZL", "MER_KEY": "88888888", "MER_NAME": "UPOP testing   Risk Card", "SIGN_METHOD": "MD5", "MER_TYPE": "7011", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200554   "}, {"MER_TRANS_CURR": "156", "MER_CODE": "535034453110007", "MER_NATION": "HKG", "MER_KEY": null, "MER_NAME": "RMB Settlement Testing", "SIGN_METHOD": "MD5", "MER_TYPE": "5311", "MER_STATUS": "0", "MER_ACQ_CODE": "0825350344   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "908076453110010", "MER_NATION": "THA", "MER_KEY": null, "MER_NAME": "The Mall Group Co., Ltd.", "SIGN_METHOD": "MD5", "MER_TYPE": "5311", "MER_STATUS": "0", "MER_ACQ_CODE": "0899080764   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "989011657340010", "MER_NATION": "THA", "MER_KEY": "88888888", "MER_NAME": "OK 168 TECHNOLOGY CO., LTD.", "SIGN_METHOD": "MD5", "MER_TYPE": "5734", "MER_STATUS": "0", "MER_ACQ_CODE": "0829890116   "}, {"MER_TRANS_CURR": "036", "MER_CODE": "619003653110004", "MER_NATION": "AUS", "MER_KEY": null, "MER_NAME": "K10 Testing", "SIGN_METHOD": "MD5", "MER_TYPE": "5311", "MER_STATUS": "0", "MER_ACQ_CODE": "0826190036   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "620076453119998", "MER_NATION": "THA", "MER_KEY": null, "MER_NAME": "VeryPlanet Test Merchant", "SIGN_METHOD": "MD5", "MER_TYPE": "5311", "MER_STATUS": "0", "MER_ACQ_CODE": "0829890764   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "918076400000001", "MER_NATION": "THA", "MER_KEY": null, "MER_NAME": "DP Alipay Testing", "SIGN_METHOD": "MD5", "MER_TYPE": "5812", "MER_STATUS": "0", "MER_ACQ_CODE": "0899180764   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "918076453110001", "MER_NATION": "THA", "MER_KEY": null, "MER_NAME": "<PERSON>", "SIGN_METHOD": "MD5", "MER_TYPE": "5311", "MER_STATUS": "0", "MER_ACQ_CODE": "0899180764   "}, {"MER_TRANS_CURR": "764", "MER_CODE": "989076459470211", "MER_NATION": "THA", "MER_KEY": "88888888", "MER_NAME": "Deleted", "SIGN_METHOD": "MD5", "MER_TYPE": "5947", "MER_STATUS": "0", "MER_ACQ_CODE": "0829890764   "}, {"MER_TRANS_CURR": "036", "MER_CODE": "620003620230002", "MER_NATION": "AUS", "MER_KEY": "88888888", "MER_NAME": "Building World PTY LTD", "SIGN_METHOD": "MD5", "MER_TYPE": "5719", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200036   "}, {"MER_TRANS_CURR": "036", "MER_CODE": "620003653319999", "MER_NATION": "AUS", "MER_KEY": "88888888", "MER_NAME": "AU UPOP Poli Testing", "SIGN_METHOD": "MD5", "MER_TYPE": "5331", "MER_STATUS": "0", "MER_ACQ_CODE": "0826200036   "}], "message": "success", "timestamp": 1713445920}