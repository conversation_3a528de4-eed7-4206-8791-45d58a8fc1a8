package com.epay.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Data
public class Pageable {

    public Pageable() {
    }

    public Pageable(Integer offset, Integer limit) {
        this.offset = offset;
        this.limit = limit;
    }

    /**
     * 开始位置
     */
    private Integer offset = 0;

    /**
     * 查询记录数， 默认为20
     */
    private Integer limit = 20;

    /**
     * 排序字段，默认为id
     */
    private String sort = "id";
}
