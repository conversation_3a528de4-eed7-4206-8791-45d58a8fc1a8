package com.epay.base.login;

import com.epay.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

/**
* <AUTHOR>
* @date 2024-04-14
*/
@Getter
@Setter
public class UcUserDto extends BaseDto {


	private Integer id;

	private String email;

	private String username;

	private String userId;

	private String password;

	private String phoneNumber;

	/**
	 * student is 1
	 */
	private String roleName;

	private String roleId;

	private Integer status;

	private String type;

	/**
	 * id card is 1,passport is 2,driver license is 3
	 */
	private Integer certType;

	private String certId;

	private String orgName;

	private String orgId;

	private Integer gender;

	private String address;

	private String avatarUrl;

	private String country;

	private String provinces;

}
