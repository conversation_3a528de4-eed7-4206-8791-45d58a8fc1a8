package com.epay.base.login;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-14
 */
@Getter
@AllArgsConstructor
public class JwtUserDto implements UserDetails {

    private final UcUserLoginDto userLoginDto;

    private final List<String> permissions;

    private final List<String> orgPermissions;

    private final Map<String, List<String>> authMap;

    private final List<AuthorityDto> authorities;

    // 新增字段：用户关联的商户ID列表
    private final List<String> merchantIds;

    // 新增字段：商户对应的支付方式信息，key为商户ID，value为支付方式列表
    private final Map<String, List<String>> merchantPaymentMethods;

    @Override
    @JSONField(serialize = false)
    public String getPassword() {
        return userLoginDto.getPassword();
    }

    @Override
    @JSONField(serialize = false)
    public String getUsername() {
        return userLoginDto.getEmail();
    }

    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    @JSONField(serialize = false)
    public boolean isEnabled() {
        return userLoginDto.getStatus() == 1;
    }
}
