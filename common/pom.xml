<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.epay</groupId>
        <artifactId>epay-webservice</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>common</artifactId>

    <properties>
        <hutool.version>5.8.20</hutool.version>
        <graph.version>5.65.0</graph.version>
        <azure.version>1.10.1</azure.version>
    </properties>

    <dependencies>
        <!--工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>com.microsoft.graph</groupId>
            <artifactId>microsoft-graph</artifactId>
            <version>${graph.version}</version>
        </dependency>

        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-identity</artifactId>
            <version>${azure.version}</version>
        </dependency>
    </dependencies>

</project>