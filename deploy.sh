#!/bin/bash

# Epay Webservice Kustomize 管理脚本
# 用法: ./deploy.sh [COMMAND] [ENVIRONMENT] [OPTIONS]

set -e

#=============================================================================
# 环境变量配置 - 请根据实际情况修改以下值
#=============================================================================

# AWS基础配置 - 请修改为您的实际值
AWS_ACCOUNT_ID="************"  # 替换为您的AWS账户ID
AWS_REGION="ap-southeast-1"    # AWS区域
ECR_REGISTRY="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"

# 镜像标签配置
IMAGE_TAG="latest"             # 默认镜像标签

# ECR镜像路径配置（基于build.sh）
ECR_ORG="com.epay"            # ECR组织路径

# Kubernetes配置
KUSTOMIZE_NAMESPACE="dp-epay"

# Kustomize配置路径（相对于当前脚本位置）
KUSTOMIZE_BASE_PATH="kustomize"

#=============================================================================
# 脚本变量（无需修改）
#=============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

#=============================================================================
# 环境变量设置函数
#=============================================================================

set_environment_variables() {
    local environment=$1
    
    # 导出AWS基础配置
    export AWS_ACCOUNT_ID="$AWS_ACCOUNT_ID"
    export AWS_REGION="$AWS_REGION"
    export ECR_REGISTRY="$ECR_REGISTRY"
    export IMAGE_TAG="$IMAGE_TAG"
    
    # 设置组件镜像配置
    export EPAY_WEBSERVICE_IMAGE_REGISTRY="$ECR_REGISTRY/$ECR_ORG"
    export EPAY_WEBSERVICE_IMAGE_TAG="$IMAGE_TAG"
    
    # 环境特定配置
    case $environment in
        uat)
            export SPRING_PROFILES_ACTIVE="k8s,uat"
            export DEPLOYMENT_ENVIRONMENT="uat"
            ;;
        prod)
            export SPRING_PROFILES_ACTIVE="k8s,prod"
            export DEPLOYMENT_ENVIRONMENT="prod"
            ;;
    esac
    
    export KUSTOMIZE_NAMESPACE="$KUSTOMIZE_NAMESPACE"
}

#=============================================================================
# 帮助信息
#=============================================================================

show_help() {
    cat << EOF
Epay Webservice Kustomize 管理脚本

用法:
    $0 COMMAND [ENVIRONMENT] [OPTIONS]

命令:
    deploy      部署组件
    remove      移除组件
    check       检查配置
    env         显示环境变量
    help        显示帮助信息

环境:
    uat         UAT环境
    prod        PROD环境

选项:
    -d, --dry-run        执行干运行
    -f, --force          强制执行，跳过确认
    -v, --verbose        显示详细输出
    --wait               等待部署完成
    --keep-pvc           移除时保留PVC
    --skip-aws           跳过AWS检查
    --restart            强制重启Pod（即使配置没变）
    --tag TAG            指定镜像标签

示例:
    $0 deploy uat                 # 部署到UAT
    $0 deploy prod --dry-run      # 干运行部署到PROD
    $0 remove uat --keep-pvc      # 移除UAT但保留存储
    $0 deploy uat --tag v1.2.0    # 部署指定标签版本
    $0 check uat                  # 检查UAT环境配置
    $0 env                        # 显示环境变量

重要提示:
    1. 首次使用前请编辑脚本顶部的环境变量配置
    2. PROD环境操作需要额外确认
    3. 建议先在UAT环境测试
    4. 脚本会自动验证当前EKS集群是否匹配目标环境:
       - UAT环境需要连接到 dp-uat-cluster
       - PROD环境需要连接到 dp-prod-cluster

EOF
}

#=============================================================================
# 工具检查
#=============================================================================

check_prerequisites() {
    local missing_tools=()
    
    for tool in kubectl kustomize; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
}

#=============================================================================
# 集群验证
#=============================================================================

validate_cluster() {
    local environment=$1
    
    # 跳过AWS检查时也跳过集群检查
    if [ "$SKIP_AWS" = true ]; then
        log_warning "跳过集群验证"
        return 0
    fi
    
    # 获取当前集群名称
    local current_cluster
    current_cluster=$(kubectl config current-context 2>/dev/null | cut -d'/' -f2 2>/dev/null || echo "unknown")
    
    # 定义期望的集群名称
    local expected_cluster
    case $environment in
        uat)
            expected_cluster="dp-uat-cluster"
            ;;
        prod)
            expected_cluster="dp-prod-cluster"
            ;;
        *)
            log_error "未知环境: $environment"
            return 1
            ;;
    esac
    
    log_info "当前集群: $current_cluster"
    log_info "期望集群: $expected_cluster"
    
    # 检查集群是否匹配
    if [[ "$current_cluster" != *"$expected_cluster"* ]]; then
        log_error "集群不匹配！"
        log_error "当前连接的是 '$current_cluster'，但 $environment 环境需要 '$expected_cluster'"
        log_error "请使用以下命令切换到正确的集群:"
        log_error "  aws eks update-kubeconfig --region $AWS_REGION --name $expected_cluster"
        return 1
    fi
    
    log_success "集群验证通过: $expected_cluster"
    return 0
}

#=============================================================================
# 参数验证
#=============================================================================

validate_parameters() {
    local environment=$1
    
    case $environment in
        uat|prod) ;;
        *) log_error "无效环境: $environment"; exit 1 ;;
    esac
}

#=============================================================================
# 配置检查功能
#=============================================================================

check_configuration() {
    local environment=$1
    
    log_info "检查 $(echo $environment | tr '[:lower:]' '[:upper:]') 环境配置..."
    
    local errors=0
    
    # 检查集群验证
    if ! validate_cluster "$environment"; then
        ((errors++))
    fi
    
    # 检查环境变量
    for var in AWS_ACCOUNT_ID ECR_REGISTRY IMAGE_TAG; do
        if [ -n "${!var}" ]; then
            log_success "$var: ${!var}"
        else
            log_error "$var 未设置"
            ((errors++))
        fi
    done
    
    # 检查Kustomize配置
    local dir="$KUSTOMIZE_BASE_PATH/overlays/$environment"
    if [ -d "$dir" ]; then
        if kustomize build "$dir" &> /dev/null; then
            log_success "Epay Webservice 配置正确"
        else
            log_error "Epay Webservice 配置语法错误"
            ((errors++))
        fi
    else
        log_error "Epay Webservice overlay不存在: $dir"
        ((errors++))
    fi
    
    # 检查Kubernetes资源
    if [ "$SKIP_AWS" != true ]; then
        kubectl get namespace "$KUSTOMIZE_NAMESPACE" &> /dev/null && \
            log_success "命名空间存在" || log_warning "命名空间不存在"
    fi
    
    if [ $errors -eq 0 ]; then
        log_success "配置检查通过！"
        return 0
    else
        log_error "发现 $errors 个问题"
        return 1
    fi
}

#=============================================================================
# 部署功能 - 使用sed进行变量替换
#=============================================================================

deploy_component() {
    local environment=$1
    local dry_run=$2
    
    local component_path="$KUSTOMIZE_BASE_PATH/overlays/$environment"
    local component_name="Epay Webservice"
    
    if [ ! -d "$component_path" ]; then
        log_error "组件路径不存在: $component_path"
        return 1
    fi
    
    log_info "部署 $component_name 到 $(echo $environment | tr '[:lower:]' '[:upper:]') 环境..."
    
    local kubectl_cmd="kubectl apply -f -"
    if [ "$dry_run" = true ]; then
        kubectl_cmd="kubectl apply --dry-run=client -f -"
    fi
    
    # 使用sed进行变量替换
    if kustomize build "$component_path" | \
       sed "s|\${EPAY_WEBSERVICE_IMAGE_REGISTRY}|${EPAY_WEBSERVICE_IMAGE_REGISTRY}|g" | \
       sed "s|\${EPAY_WEBSERVICE_IMAGE_TAG}|${EPAY_WEBSERVICE_IMAGE_TAG}|g" | \
       eval "$kubectl_cmd"; then
        
        log_success "$component_name 部署成功"
        
        # 检查是否需要强制重启
        if [ "$FORCE_RESTART" = true ] && [ "$dry_run" = false ]; then
            log_info "强制重启 $component_name..."
            
            # Epay Webservice使用deployment
            local resource_type="deployment"
            local resource_name="epay-webservice"
            
            log_info "重启 $resource_name..."
            if kubectl rollout restart "$resource_type/$resource_name" -n "$KUSTOMIZE_NAMESPACE" 2>/dev/null; then
                log_success "$resource_name 重启成功"
                
                # 如果设置了等待选项，等待重启完成
                if [ "$WAIT_DEPLOYMENT" = true ]; then
                    log_info "等待 $resource_name 重启完成..."
                    if ! kubectl rollout status "$resource_type/$resource_name" -n "$KUSTOMIZE_NAMESPACE" --timeout=300s; then
                        log_error "$resource_name 重启超时"
                        return 1
                    fi
                fi
            else
                log_warning "$resource_name 重启失败，可能资源不存在"
            fi
        fi
        
        if [ "$dry_run" = false ]; then
            log_info "等待Pod启动..."
            sleep 5
            
            # 检查Pod状态
            kubectl get pods -n "$KUSTOMIZE_NAMESPACE" -l "app=epay-webservice" 2>/dev/null || \
                log_info "没有找到相关资源"
        fi
        
        return 0
    else
        log_error "$component_name 部署失败"
        return 1
    fi
}

wait_for_deployment() {
    local resource_type="deployment"
    local resource_name="epay-webservice"
    
    log_info "等待 Epay Webservice 部署完成..."
    
    log_info "等待 $resource_name 就绪..."
    kubectl rollout status "$resource_type/$resource_name" -n "$KUSTOMIZE_NAMESPACE" --timeout=300s
    
    log_success "Epay Webservice 部署完成"
}

#=============================================================================
# 移除功能
#=============================================================================

remove_component() {
    local environment=$1
    local dry_run=$2
    
    local component_path="$KUSTOMIZE_BASE_PATH/overlays/$environment"
    local component_name="Epay Webservice"
    
    log_info "移除 $component_name 从 $(echo $environment | tr '[:lower:]' '[:upper:]') 环境..."
    
    local kubectl_cmd="kubectl delete -f -"
    if [ "$dry_run" = true ]; then
        kubectl_cmd="kubectl delete --dry-run=client -f -"
    fi
    
    if kustomize build "$component_path" | eval "$kubectl_cmd" 2>/dev/null; then
        log_success "$component_name 移除成功"
    else
        log_warning "$component_name 移除失败或资源不存在"
    fi
    
    # 移除PVC（如果不保留）
    if [ "$KEEP_PVC" != true ] && [ "$dry_run" = false ]; then
        remove_pvc
    fi
}

remove_pvc() {
    kubectl delete pvc -n "$KUSTOMIZE_NAMESPACE" -l "app=epay-webservice" --ignore-not-found=true
}

#=============================================================================
# 确认操作
#=============================================================================

confirm_operation() {
    local operation=$1
    local environment=$2
    
    if [ "$FORCE" = true ]; then
        return 0
    fi
    
    # 获取当前集群信息
    local current_cluster
    current_cluster=$(kubectl config current-context 2>/dev/null | cut -d'/' -f2 2>/dev/null || echo "unknown")
    
    echo
    log_warning "即将执行以下操作:"
    echo "  操作: $operation"
    echo "  组件: Epay Webservice"
    echo "  环境: $(echo $environment | tr '[:lower:]' '[:upper:]')"
    echo "  集群: $current_cluster"
    echo "  命名空间: $KUSTOMIZE_NAMESPACE"
    echo "  镜像标签: $IMAGE_TAG"
    
    if [ "$operation" = "remove" ] && [ "$KEEP_PVC" != true ]; then
        echo -e "  ${RED}警告: 将删除PVC存储卷（数据将丢失）${NC}"
    fi
    
    echo
    
    if [ "$environment" = "prod" ]; then
        log_error "这是生产环境操作！"
        read -p "请输入 'CONFIRM-PROD' 确认操作: " -r
        if [ "$REPLY" != "CONFIRM-PROD" ]; then
            log_info "操作已取消"
            exit 0
        fi
    else
        read -p "确认继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
}

#=============================================================================
# 环境变量显示
#=============================================================================

show_environment() {
    echo "Epay Webservice 环境变量配置:"
    echo
    echo "=== AWS配置 ==="
    echo "AWS_ACCOUNT_ID: $AWS_ACCOUNT_ID"
    echo "AWS_REGION: $AWS_REGION"
    echo "ECR_REGISTRY: $ECR_REGISTRY"
    echo "IMAGE_TAG: $IMAGE_TAG"
    echo
    echo "=== 组件镜像 ==="
    echo "Epay Webservice: $ECR_REGISTRY/$ECR_ORG/epay-webservice:$IMAGE_TAG"
    echo
    echo "=== Kubernetes ==="
    echo "Namespace: $KUSTOMIZE_NAMESPACE"
    echo "Kustomize Path: $KUSTOMIZE_BASE_PATH"
}

#=============================================================================
# 初始化
#=============================================================================

init_environment() {
    # 创建命名空间（如果不存在）
    if ! kubectl get namespace "$KUSTOMIZE_NAMESPACE" &> /dev/null; then
        log_info "创建命名空间 $KUSTOMIZE_NAMESPACE..."
        kubectl create namespace "$KUSTOMIZE_NAMESPACE"
    fi
}

#=============================================================================
# 主函数
#=============================================================================

main() {
    local command=""
    local environment=""
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            deploy|remove|check|env|help)
                command="$1"
                shift
                ;;
            uat|prod)
                environment="$1"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                dry_run=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --wait)
                WAIT_DEPLOYMENT=true
                shift
                ;;
            --keep-pvc)
                KEEP_PVC=true
                shift
                ;;
            --skip-aws)
                SKIP_AWS=true
                shift
                ;;
            --restart)
                FORCE_RESTART=true
                shift
                ;;
            --tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查命令
    if [ -z "$command" ]; then
        log_error "缺少命令"
        show_help
        exit 1
    fi
    
    # 处理特殊命令
    case $command in
        help)
            show_help
            exit 0
            ;;
        env)
            show_environment
            exit 0
            ;;
        check)
            if [ -z "$environment" ]; then
                environment="uat"
            fi
            set_environment_variables "$environment"
            check_configuration "$environment"
            exit $?
            ;;
    esac
    
    # 检查必需参数
    if [ -z "$environment" ]; then
        log_error "缺少环境参数"
        show_help
        exit 1
    fi
    
    # 验证参数
    validate_parameters "$environment"
    
    # 设置环境变量
    set_environment_variables "$environment"
    
    # 检查前提条件
    check_prerequisites
    
    # 验证集群
    if ! validate_cluster "$environment"; then
        exit 1
    fi
    
    # 初始化环境
    init_environment
    
    # 确认操作
    confirm_operation "$command" "$environment"
    
    log_info "开始执行 $command 操作..."
    
    # 执行命令
    case $command in
        deploy)
            deploy_component "$environment" "$dry_run"
            
            if [ "$dry_run" = false ]; then
                echo
                log_info "部署状态:"
                kubectl get pods -n "$KUSTOMIZE_NAMESPACE" -l "app=epay-webservice" 2>/dev/null || \
                    log_warning "无法获取Pod状态"
            fi
            ;;
        remove)
            remove_component "$environment" "$dry_run"
            
            if [ "$dry_run" = false ]; then
                echo
                log_info "剩余资源:"
                kubectl get pods -n "$KUSTOMIZE_NAMESPACE" -l "app=epay-webservice" 2>/dev/null || \
                    log_info "没有找到相关资源"
            fi
            ;;
    esac
}

# 执行主函数
main "$@"