#配置数据源
spring:
  datasource:
    url: ${DB_URL:****************************************}
    username: ${DB_USERNAME:epay}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 30
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 20000
      validation-timeout: 5000
      leak-detection-threshold: 60000

  redis:
    database: ${REDIS_DB:0}
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 5000
    ssl: true

# 登录相关配置
login:
  #  是否限制单用户登录
  single-login: true
  # Redis用户登录缓存配置
  user-cache:
    # 存活时间/秒
    idle-time: 21600

#jwt
jwt:
  header: Authorization
  # 登录方式
  login-type: LoginType
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ${JWT_BASE64_SECRET:ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=}
  # 令牌过期时间 此处单位/毫秒 ，默认8小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 28800000
  # app在线用户key
  online-management-key: "online-web-token:"
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

# 数据服务
data-server:
  url: http://data-agent-oracle-service.default:8088/api/data/table
  file-url: http://data-agent-oracle-service.default:8088/api/file/download
  transaction-url: http://data-agent-oracle-service.default:8088/api/data/transaction
  user-url: http://user-center:8080/api
  web-url: https://dopp.dynamicg.com/oppmertest/api/proxydev?path=/api
  refund-url: http://online-payment-gw.dp-online/wechat/unified/v1/refund
  pay-url: http://online-payment-gw.dp-online/wechat/unified/v1/pay
  online-pay-url: http://online-payment-gw.dp-online/wechat/unified/v1/onlinepay
  email-jump-url: http://121.43.178.172:8002/#/billpay/Pay
  opp-url: http://47.99.150.240:8085/api

pay-url:
  front-end-page-url: http://121.43.178.172:8002/#/billpay/Pay?pay=done
  front-fail-page-url: http://121.43.178.172:8002/#/billpay/Pay?pay=fail
  call-page-url: http://121.43.178.172:8087/api/bill/paymentCallback

#是否开启 swagger-ui
swagger:
  enabled: false

# 文件存储路径 - 使用EFS挂载(epay-webservice目录可读写，其他目录只读)
file:
  baseUrl: http://121.43.178.172:8087/
  # epay-webservice专用目录，具有读写权限
  path: /data/epay-webservice/upload/file/
  avatar: /data/epay-webservice/upload/avatar/
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5

#密码加密传输，前端公钥加密，后端私钥解密
blowfish:
  key: ${BLOWFISH_KEY:"#QDbFTk$fn&&=dkM4VS3P5VrmjSHaxwv"}
#密码加密传输，前端公钥加密，后端私钥解密
aes:
  dataKey: ${AES_DATAKEY:"#QDbFTk$fn&&=dkM4VS3P5VrmjSHaxwv"}

id-init:
  manager-id: 10000
