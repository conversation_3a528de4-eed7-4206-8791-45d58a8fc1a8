apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: epay-webservice-secret-provider
spec:
  provider: aws
  parameters:
    region: ap-southeast-1
    usePodIdentity: "true"
    objects: |
      - objectName: "epay-webservice/prod/credentials"
        objectType: "secretsmanager"
        jmesPath:
          - path: "db_username"
            objectAlias: "db-username"
          - path: "db_password"
            objectAlias: "db-password"
          - path: "db_url"
            objectAlias: "db-url"
          - path: "redis_host"
            objectAlias: "redis-host"
          - path: "redis_port"
            objectAlias: "redis-port"
          - path: "redis_password"
            objectAlias: "redis-password"
          - path: "blowfish_key"
            objectAlias: "blowfish-key"
          - path: "aes_datakey"
            objectAlias: "aes-datakey"
          - path: "jwt_base64_secret"
            objectAlias: "jwt-base64-secret"