<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <contextName>epay-webservice-prod</contextName>
    
    <!-- 定义日志输出路径 -->
    <property name="log.dir" value="/var/log/epay-webservice/${POD_NAME}" />
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n" />
    
    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 普通日志文件输出 -->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/app.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 错误日志文件输出 -->
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 日志级别配置 -->
    <logger name="org.springframework" level="WARN" />
    <logger name="org.springframework.web" level="WARN" />
    <logger name="org.springframework.security" level="WARN" />
    <logger name="org.hibernate" level="ERROR" />
    <logger name="org.hibernate.SQL" level="ERROR" />
    <logger name="com.epay" level="INFO" />
    
    <!-- SQL日志 - 生产环境禁用 -->
    <logger name="jdbc.sqlonly" level="ERROR" />
    <logger name="jdbc.sqltiming" level="OFF" />
    <logger name="jdbc.audit" level="OFF" />
    <logger name="jdbc.resultset" level="OFF" />
    <logger name="jdbc.resultsettable" level="OFF" />
    <logger name="jdbc.connection" level="OFF" />
    
    <!-- 根日志级别 -->
    <root level="WARN">
        <appender-ref ref="console" />
        <appender-ref ref="file" />
        <appender-ref ref="error" />
    </root>
</configuration>