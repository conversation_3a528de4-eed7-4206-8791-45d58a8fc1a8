# Static EFS PV configuration for PRODUCTION environment
apiVersion: v1
kind: PersistentVolume
metadata:
  name: efs-pv-epay-webservice-prod
  labels:
    environment: prod
    storage-type: efs
    component: epay-webservice
spec:
  capacity:
    storage: 500Gi  # Increased for production
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: efs-sc-static-prod
  mountOptions:
    - tls
    - _netdev
    - iam
    - rsize=1048576
    - wsize=1048576
    - hard
    - intr
    - timeo=600
    - fsc  # Enable local caching for better performance
  claimRef:
    namespace: dp-epay
    name: epay-webservice-efs-pvc
  csi:
    driver: efs.csi.aws.com
    # Production: Regional EFS with access point - same as dp-posp
    volumeHandle: fs-062dd31735980312a::fsap-0e3e18094275e4c12
    volumeAttributes:
      encryptInTransit: "true"