apiVersion: apps/v1
kind: Deployment
metadata:
  name: epay-webservice
spec:
  replicas: 1
  template:
    spec:
      initContainers:
        - name: wait-for-secrets
          image: busybox:1.35
          securityContext:
            runAsUser: 0
            runAsNonRoot: false
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          command:
            - sh
            - -c
            - |
              echo "等待AWS Secrets Manager密钥挂载..."
              RETRY_COUNT=0
              MAX_RETRIES=30

              while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
                if [ -f /mnt/secrets/db-username ] && [ -f /mnt/secrets/db-password ]; then
                  echo "密钥文件已就绪"
                  
                  # 如果db-url存在，使用它；否则使用默认配置
                  if [ -f /mnt/secrets/db-url ]; then
                    cat /mnt/secrets/db-url | { read DB_URL; echo "DB_URL=$DB_URL" > /shared/db-config; }
                    echo "已加载数据库URL"
                  fi
                  
                  cat /mnt/secrets/db-username | { read DB_USERNAME; echo "DB_USERNAME=$DB_USERNAME" >> /shared/db-config; }
                  cat /mnt/secrets/db-password | { read DB_PASSWORD; echo "DB_PASSWORD=$DB_PASSWORD" >> /shared/db-config; }
                  echo "已加载数据库凭据"
                  
                  # 读取Redis配置
                  if [ -f /mnt/secrets/redis-host ]; then
                    cat /mnt/secrets/redis-host | { read REDIS_HOST; echo "REDIS_HOST=$REDIS_HOST" >> /shared/db-config; }
                    echo "已加载Redis主机"
                  fi
                  
                  if [ -f /mnt/secrets/redis-port ]; then
                    cat /mnt/secrets/redis-port | { read REDIS_PORT; echo "REDIS_PORT=$REDIS_PORT" >> /shared/db-config; }
                    echo "已加载Redis端口"
                  fi
                  
                  if [ -f /mnt/secrets/redis-password ]; then
                    cat /mnt/secrets/redis-password | { read REDIS_PASSWORD; echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> /shared/db-config; }
                    echo "已加载Redis密码"
                  fi
                  
                  # 读取Blowfish key
                  if [ -f /mnt/secrets/blowfish-key ]; then
                    cat /mnt/secrets/blowfish-key | { read BLOWFISH_KEY; echo "BLOWFISH_KEY=$BLOWFISH_KEY" >> /shared/db-config; }
                    echo "已加载Blowfish密钥"
                  fi
                  
                  # 读取AES DataKey
                  if [ -f /mnt/secrets/aes-datakey ]; then
                    cat /mnt/secrets/aes-datakey | { read AES_DATAKEY; echo "AES_DATAKEY=$AES_DATAKEY" >> /shared/db-config; }
                    echo "已加载AES DataKey"
                  fi
                  
                  # 读取JWT Base64 Secret
                  if [ -f /mnt/secrets/jwt-base64-secret ]; then
                    cat /mnt/secrets/jwt-base64-secret | { read JWT_BASE64_SECRET; echo "JWT_BASE64_SECRET=$JWT_BASE64_SECRET" >> /shared/db-config; }
                    echo "已加载JWT Base64 Secret"
                  fi
                  
                  # 设置Redisson URL用于SSL连接
                  if [ -f /mnt/secrets/redis-host ] && [ -f /mnt/secrets/redis-port ]; then
                    REDIS_HOST=$(cat /mnt/secrets/redis-host)
                    REDIS_PORT=$(cat /mnt/secrets/redis-port)
                    echo "REDISSON_URL=rediss://$REDIS_HOST:$REDIS_PORT" >> /shared/db-config
                    echo "已设置Redisson SSL URL: rediss://$REDIS_HOST:$REDIS_PORT"
                  else
                    echo "REDISSON_URL=rediss://127.0.0.1:6379" >> /shared/db-config
                    echo "使用默认Redisson SSL URL"
                  fi
                  
                  echo "所有密钥配置已准备完成"
                  
                  # 创建日志目录并设置权限
                  mkdir -p /var/log/epay-webservice/${POD_NAME}
                  chown -R 1000:1000 /var/log/epay-webservice/${POD_NAME}
                  chmod -R 755 /var/log/epay-webservice/${POD_NAME}
                  echo "Created log directory: /var/log/epay-webservice/${POD_NAME} with proper permissions"
                  
                  # 创建epay-webservice专用目录并设置权限
                  mkdir -p /data/epay-webservice/upload/file
                  mkdir -p /data/epay-webservice/upload/avatar
                  
                  # 只设置epay-webservice目录权限，不修改其他目录
                  chown -R 1000:2000 /data/epay-webservice
                  chmod -R 755 /data/epay-webservice
                  
                  echo "Created EFS directories: /data/epay-webservice with proper permissions"
                  
                  exit 0
                fi
                
                echo "等待密钥文件...($RETRY_COUNT/$MAX_RETRIES)"
                sleep 10
                RETRY_COUNT=$((RETRY_COUNT + 1))
              done

              echo "警告: 密钥文件未找到，使用默认配置"
              touch /shared/db-config
          volumeMounts:
            - name: secrets-store
              mountPath: /mnt/secrets
              readOnly: true
            - name: shared-data
              mountPath: /shared
            - name: log-volume
              mountPath: /var/log/epay-webservice
            - name: efs-data
              mountPath: /data
      containers:
        - name: epay-webservice
          command:
            - sh
            - -c
            - |
              # 读取密钥配置
              if [ -f /shared/db-config ]; then
                while IFS='=' read -r key value; do
                  if [ -n "$key" ] && [ -n "$value" ]; then
                    export "$key"="$value"
                  fi
                done < /shared/db-config
                echo "密钥配置已加载"
              fi

              # 验证文件权限
              echo "验证EFS挂载权限..."
              if [ -w /data/epay-webservice ]; then
                echo "✅ epay-webservice目录可写"
              else
                echo "❌ epay-webservice目录不可写"
                exit 1
              fi
              
              if [ -r /data ]; then
                echo "✅ EFS根目录可读"
              else
                echo "❌ EFS根目录不可读"
                exit 1
              fi
              
              # 测试是否能写入其他目录 (仅检查，不修改)
              if touch /data/test-write-check 2>/dev/null; then
                echo "ℹ️  信息: 可以写入EFS根目录"
                rm -f /data/test-write-check 2>/dev/null
              else
                echo "ℹ️  信息: 无法写入EFS根目录"
              fi

              # 启动应用
              exec java $JAVA_OPTS -Dspring.config.location=/app/config/application-uat.yml -Dlogging.config=file:/app/config/logback.xml -jar /app/app.jar
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "uat"
            - name: JAVA_OPTS
              value: "-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Dspring.jpa.show-sql=true -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
            - name: secrets-store
              mountPath: /mnt/secrets
              readOnly: true
            - name: shared-data
              mountPath: /shared
            - name: log-volume
              mountPath: /var/log/epay-webservice
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1024Mi"
              cpu: "500m"
      volumes:
        - name: config-volume
          configMap:
            name: epay-webservice-config
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: epay-webservice-secret-provider
        - name: shared-data
          emptyDir: {}
        - name: log-volume
          hostPath:
            path: /var/log/epay-webservice
            type: DirectoryOrCreate
