apiVersion: v1
kind: Service
metadata:
  name: epay-webservice
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-name: uat-epay-webservice-nlb
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-port: "8080"
    service.beta.kubernetes.io/aws-load-balancer-subnets: uat-subnet-public1,uat-subnet-public-redundant
    service.beta.kubernetes.io/aws-load-balancer-private-ipv4-addresses: ***********,************
    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
spec:
  type: LoadBalancer
  ports:
    - name: http
      port: 8080
      targetPort: 8080
      protocol: TCP
    - name: debug
      port: 5005
      targetPort: 5005
