# Static EFS PV configuration for UAT environment
apiVersion: v1
kind: PersistentVolume
metadata:
  name: efs-pv-epay-webservice-uat
  labels:
    environment: uat
    storage-type: efs
    component: epay-webservice
spec:
  capacity:
    storage: 100Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: efs-sc-static
  mountOptions:
    - tls
    - _netdev
    - iam
    - rsize=1048576
    - wsize=1048576
    - hard
    - intr
    - timeo=600
    - az=ap-southeast-1a  # Cross-AZ access for One Zone EFS
  claimRef:
    namespace: dp-epay
    name: epay-webservice-efs-pvc
  csi:
    driver: efs.csi.aws.com
    # UAT: One Zone EFS for cost optimization - same as dp-posp
    volumeHandle: fs-066fc8f257e06d72a::fsap-079153c63615c14c4
    volumeAttributes:
      encryptInTransit: "true"