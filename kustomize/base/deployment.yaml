apiVersion: apps/v1
kind: Deployment
metadata:
  name: epay-webservice
  labels:
    app: epay-webservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: epay-webservice
  template:
    metadata:
      labels:
        app: epay-webservice
    spec:
      serviceAccountName: epay-webservice
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      initContainers:
        - name: wait-for-secrets
          image: busybox:1.35
          securityContext:
            runAsUser: 0
            runAsNonRoot: false
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          command:
            - sh
            - -c
            - |
              echo "等待AWS Secrets Manager密钥挂载..."
              RETRY_COUNT=0
              MAX_RETRIES=30

              while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
                if [ -f /mnt/secrets/db-username ] && [ -f /mnt/secrets/db-password ]; then
                  echo "密钥文件已就绪"
                  
                  # 如果db-url存在，使用它；否则使用默认配置
                  if [ -f /mnt/secrets/db-url ]; then
                    cat /mnt/secrets/db-url | { read DB_URL; echo "DB_URL=$DB_URL" > /shared/db-config; }
                    echo "已加载数据库URL"
                  fi
                  
                  cat /mnt/secrets/db-username | { read DB_USERNAME; echo "DB_USERNAME=$DB_USERNAME" >> /shared/db-config; }
                  cat /mnt/secrets/db-password | { read DB_PASSWORD; echo "DB_PASSWORD=$DB_PASSWORD" >> /shared/db-config; }
                  echo "已加载数据库凭据"
                  
                  # 读取Redis配置
                  if [ -f /mnt/secrets/redis-host ]; then
                    cat /mnt/secrets/redis-host | { read REDIS_HOST; echo "REDIS_HOST=$REDIS_HOST" >> /shared/db-config; }
                    echo "已加载Redis主机"
                  fi
                  
                  if [ -f /mnt/secrets/redis-port ]; then
                    cat /mnt/secrets/redis-port | { read REDIS_PORT; echo "REDIS_PORT=$REDIS_PORT" >> /shared/db-config; }
                    echo "已加载Redis端口"
                  fi
                  
                  if [ -f /mnt/secrets/redis-password ]; then
                    cat /mnt/secrets/redis-password | { read REDIS_PASSWORD; echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> /shared/db-config; }
                    echo "已加载Redis密码"
                  fi
                  
                  # 读取Blowfish key
                  if [ -f /mnt/secrets/blowfish-key ]; then
                    cat /mnt/secrets/blowfish-key | { read BLOWFISH_KEY; echo "BLOWFISH_KEY=$BLOWFISH_KEY" >> /shared/db-config; }
                    echo "已加载Blowfish密钥"
                  fi
                  
                  echo "所有密钥配置已准备完成"
                  
                  # 创建日志目录并设置权限
                  mkdir -p /var/log/epay-webservice/${POD_NAME}
                  chown -R 1000:1000 /var/log/epay-webservice/${POD_NAME}
                  chmod -R 755 /var/log/epay-webservice/${POD_NAME}
                  echo "Created log directory: /var/log/epay-webservice/${POD_NAME} with proper permissions"
                  
                  exit 0
                fi
                
                echo "等待密钥文件...($RETRY_COUNT/$MAX_RETRIES)"
                sleep 10
                RETRY_COUNT=$((RETRY_COUNT + 1))
              done

              echo "警告: 密钥文件未找到，使用默认配置"
              touch /shared/db-config
          volumeMounts:
            - name: secrets-store
              mountPath: /mnt/secrets
              readOnly: true
            - name: shared-data
              mountPath: /shared
            - name: log-volume
              mountPath: /var/log/epay-webservice
      containers:
        - name: epay-webservice
          image: epay-webservice:latest
          imagePullPolicy: Always
          command:
            - sh
            - -c
            - |
              # 读取密钥配置
              if [ -f /shared/db-config ]; then
                while IFS='=' read -r key value; do
                  if [ -n "$key" ] && [ -n "$value" ]; then
                    export "$key"="$value"
                  fi
                done < /shared/db-config
                echo "密钥配置已加载"
              fi

              # 启动应用
              exec java $JAVA_OPTS -Dspring.config.location=/app/config/application-uat.yml -Dlogging.config=file:/app/config/logback.xml -jar /app/app.jar
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "k8s,uat"
            - name: SERVER_PORT
              value: "8080"
            - name: JAVA_OPTS
              value: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
            - name: secrets-store
              mountPath: /mnt/secrets
              readOnly: true
            - name: shared-data
              mountPath: /shared
            - name: log-volume
              mountPath: /var/log/epay-webservice
            - name: efs-data
              mountPath: /data
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - ALL
            # 限制文件系统写入权限
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 2000
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: config-volume
          configMap:
            name: epay-webservice-config
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: epay-webservice-secret-provider
        - name: shared-data
          emptyDir: {}
        - name: log-volume
          hostPath:
            path: /var/log/epay-webservice
            type: DirectoryOrCreate
        - name: efs-data
          persistentVolumeClaim:
            claimName: epay-webservice-efs-pvc